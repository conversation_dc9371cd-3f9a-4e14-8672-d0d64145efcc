import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { UserService } from '../../entities/user-management/user.service';
import { APP_ROUTES, AppConstants } from '../constants';
import { StorageService } from './storage.service';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../enums';

@Injectable({
  providedIn: 'root'
})
export class PermissionGuard implements CanActivate {
  constructor(
    private readonly router: Router,
    private readonly storageService: StorageService,
    private readonly userService: UserService
  ) {}
  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (this.storageService.get(AppConstants.authenticationToken) !== null) {
      const allowedRoles = next.data['permittedRoles'] as Array<ROLE_TYPE>;
      const allowedAuthorities = allowedRoles.map(item => AUTHORITY_ROLE_STRING[item]);
      if (allowedAuthorities) {
        if (this.userService.roleMatch(allowedAuthorities)) {
          return true;
        } else {
          this.router.navigateByUrl(APP_ROUTES.DASHBOARD);
          return false;
        }
      }
      return true;
    }
  }
}
