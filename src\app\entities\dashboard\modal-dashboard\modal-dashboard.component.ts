import { AfterContentChecked, ChangeDetector<PERSON>ef, Component, Input, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { ImageDropboxGalleryComponent } from '../../../@shared/components/image-dropbox-gallery/image-dropbox-gallery.component';
import { GenerateReport, NewReport, ReportTypes } from '../../../@shared/models/report.model';
import { WorkOrder, WorkOrderData } from '../../../@shared/models/workOrder.model';
import { SortByMonthPipe } from '../../../@shared/pipes/sort-by-month.pipe';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { PageOpenFromConstant } from '../../operations/custom-forms/custom-forms.model';
import { ReportService } from '../../report/report.service';
import { ReschedulerModalComponent } from '../../workorder-management/rescheduler-modal/rescheduler-modal.component';
import {
  AvailableModuleTorqueFormsResponse,
  DownloadAllViewFormResponse,
  FormStatus,
  ModuleTorqueFormDTO,
  ViewFormsList,
  ViewFormsResponse,
  WO_STATUSES
} from '../../workorder-management/workorder.model';
import { WorkorderService } from '../../workorder-management/workorder.service';
import { DashboardService } from '../dashboard.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

const findByLabel = (dataArray, label) => dataArray.find(item => item.label === label);

@Component({
  selector: 'sfl-modal-dashboard',
  templateUrl: './modal-dashboard.component.html',
  styleUrls: ['./modal-dashboard.component.scss'],
  providers: [SortByMonthPipe]
})
export class ModalDashboardComponent implements OnInit, AfterContentChecked, OnDestroy {
  modalRef: BsModalRef;
  @Input() month = '';
  @Input() day = '';
  @Input() year = '';
  @Input() message: any;
  @Input() eventId: number;
  @Input() monthViseWO: any;
  @Input() assessmentDTO: any;
  @Input() isCompactView = false;
  @Input() customerId: number;
  @Input() portfolioId: number;
  @Input() siteId: number;
  @Input() workorderId: number;
  editPermission = false;
  loading = false;
  isWorkOrderDetail = false;
  isWorkOrderRescheduler = false;
  user: string;
  workOrderTypeList = [
    {
      woName: 'SV',
      woType: 1,
      keyName: 'svwo',
      task: 'siteVisitTask'
    },
    {
      woName: 'IPM',
      woType: 2,
      keyName: 'ipmwo',
      task: 'inverterPMTask'
    },
    {
      woName: 'MVPM',
      woType: 3,
      keyName: 'mvpmwo',
      task: 'mvpmTask'
    },
    {
      woName: 'MVTH',
      woType: 14,
      keyName: 'mvthwo',
      task: 'mvthTask'
    },
    {
      woName: 'TPM',
      woType: 12,
      keyName: 'tpmwo',
      task: 'tpmTask'
    },
    {
      woName: 'THERMAL',
      woType: 4,
      keyName: 'thermalWO',
      task: 'thermalTask'
    },
    {
      woName: 'AERIAL',
      woType: 5,
      keyName: 'aswo',
      task: 'aerialScanTask'
    },
    {
      woName: 'IV',
      woType: 7,
      keyName: 'ivwo',
      task: 'electricalIVTask'
    },
    {
      woName: 'VOC',
      woType: 6,
      keyName: 'vocwo',
      task: 'electricalVOCTask'
    },
    {
      woName: 'VGT',
      woType: 8,
      keyName: 'vgtwo',
      task: 'vegetationTask'
    },
    {
      woName: 'TRQ',
      woType: 13,
      keyName: 'trqwo',
      task: 'trqTask'
    }
  ];
  subscription: Subscription = new Subscription();
  @ViewChild('viewFormsTemplate', { static: false }) viewFormsTemplate: TemplateRef<any>;
  @ViewChild('viewModuleTorqueFormsTemplate', { static: false }) viewModuleTorqueFormsTemplate: TemplateRef<any>;
  @ViewChild('historyModalTemplate', { static: false }) historyModalTemplate: TemplateRef<any>;
  viewFromModalRef: BsModalRef;
  viewModuleTorqueFormsRef: BsModalRef;
  historyModalRef: BsModalRef;
  uploadedFormsData: ViewFormsList[] = [];
  formEditNoteHistory: any;
  viewFromDetails: ViewFormsList;
  pagination = {
    pageSize: 100,
    itemsCount: 100,
    totalCount: 0,
    currentPage: 1
  };
  assessmentId: number;
  WoStatuses = WO_STATUSES;
  completedFormMapId: number[] = [];
  pageOpenFromConstant = PageOpenFromConstant;
  selectedZones: ModuleTorqueFormDTO[];
  isAnyFormStartOrComplete: boolean;
  completedMTFormMapId: number[];
  workorderDetails: { customerId: number; portfolioId: number; siteId: number; workorderId: number } = {
    customerId: 0,
    portfolioId: 0,
    siteId: 0,
    workorderId: 0
  };
  newReportData: NewReport = new NewReport();
  generateNewReportData: GenerateReport;
  newReportBtnText = 'Start New Report';
  isReportExistForWO = false;
  allowStartNewReport = false;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService,
    private readonly dashboardService: DashboardService,
    private readonly workorderService: WorkorderService,
    private readonly modalService: BsModalService,
    private readonly commonService: CommonService,
    private readonly reportService: ReportService
  ) {}

  ngOnInit() {
    this.editPermission = this.storageService.get('permission').WO;
    this.user = this.storageService.get('user').authorities;
    this.dashboardService.reloadWorkOrder$.subscribe(woId => {
      if (woId !== 0) {
        this.refreshWorkOrderDetails(woId);
      }
    });
    this.workorderDetails.customerId = this.message.workorders[0].customerId;
    this.workorderDetails.portfolioId = this.message.workorders[0].portfolioId;
    this.workorderDetails.siteId = this.message.workorders[0].siteId;
    this.workorderDetails.workorderId = this.workorderId;
    // check if the report is already created for the workorder or not
    this.checkReportCreationStatus();
  }

  ngAfterContentChecked() {
    this.cdr.detectChanges();
  }

  refreshWorkOrderDetails(woId) {
    this.loading = true;

    this.dashboardService.getWODetailsById(woId).subscribe({
      next: (res: any) => {
        if (this.message.workorders) {
          this.message.workorders = [...res];
        }

        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  onPDFOrPPTReportLink(reportLink, isPDF: boolean) {
    if (reportLink) {
      if (isPDF) {
        window.open(reportLink);
      } else {
        window.open(reportLink, '_self');
      }
    }
  }

  onClose() {
    this._bsModalRef.hide();
  }

  onEdit() {
    const data = this.message.workorders.filter(x => x.label === 'Work order detail');
    this.router.navigate(['/entities/workorders/add'], {
      queryParams: {
        id: data[0].id,
        assementType: data[0].assementType,
        workOrderNumber: data[0].workOrderNumber,
        workOrderId: this.workorderId,
        backToDashboard: true
      }
    });
    this._bsModalRef.hide();
  }

  onViewReport(reportType) {
    const id = this.eventId;
    if (reportType === 'SV') {
      this.router.navigate(['/entities/reports/sitevisits/edit/' + id + '/false'], { queryParams: { returnTo: 'Dashboard' } });
    } else if (reportType === 'VGT') {
      this.router.navigate(['/entities/reports/vegetation/edit/' + id + '/false']);
    } else {
      this.router.navigate(['/entities/reports/mvpm/edit/' + id + '/false'], { queryParams: { returnTo: 'Dashboard' } });
    }
    this._bsModalRef.hide();
  }

  onWOClick(woDetails: any) {
    if (woDetails?.id) {
      this.loading = true;
      this.dashboardService.getWODetailsById(woDetails.id).subscribe({
        next: res => {
          this.eventId = woDetails.id;
          this.isWorkOrderDetail = true;
          this.isCompactView = false;
          this.message = { workorders: res };
          this.cdr.detectChanges();
          this.loading = false;
        },
        error: () => (this.loading = false)
      });
    }
  }

  checkWOLockingStatus() {
    const data = {
      id: this.message.workorders[0].id,
      assementType: this.message.workorders[0].assementType,
      isReLockWo: false
    };

    this.workorderService.woLockingStatus(data).subscribe({
      next: res => {
        if (res.islocked) {
          setTimeout(() => {
            this.alertService.showInfoToast(res.message);
          }, 0);
        } else {
          this.lockUnlockWO(data);
        }
      }
    });
  }

  lockUnlockWO(data, isLocked = false) {
    this.loading = true;

    if (!isLocked) {
      this.workorderService.lockWO(data).subscribe({
        next: () => {
          this.loading = false;
          this.showRescheduler();
        },
        error: () => (this.loading = false)
      });
    } else {
      this.workorderService.unlockWO(data).subscribe({
        next: () => (this.loading = false),
        error: () => (this.loading = false)
      });
    }
  }

  async showRescheduler() {
    // prepare work order details data
    const filteredWO = this.message.workorders.filter(x => x.label === 'Work order detail')[0];
    const tempWOData = [];
    filteredWO.data.forEach(item => {
      tempWOData[item.label] = item.value;
    });
    // to be used
    const workOrderDetails = {
      datePerformed: tempWOData['Date Performed'],
      dateScheduled: tempWOData['Scheduled Date'],
      dateReScheduled: tempWOData['Reschedule Date'],
      tentativeMonth: tempWOData['Tentative Month'],
      fieldTech: tempWOData['Field Tech'],
      period: tempWOData['Period'],
      photoLink: tempWOData['Photos Link'],
      siteName: tempWOData['Site Name'],
      typeOfWorkOrder: tempWOData['Type of Work Order'],
      workOrderNumber: tempWOData['Work Order #'],
      workOrderStatus: tempWOData['Work Order Status'],
      id: this.eventId,
      portfolioId: this.portfolioId,
      siteId: this.siteId,
      rescheduleCount: tempWOData['Reschedule Count']
    };

    const workOrders = {
      id: filteredWO.id,
      assementType: filteredWO.assementType,
      selectedWorkOrderId: this.workorderDetails.workorderId,
      frequncyType: ''
    };

    const allWorkOrder = (await this.getWorkOrder(workOrders)) as WorkOrderData[];
    const workOrderData = allWorkOrder.filter(woData => woData.id === this.workorderDetails.workorderId)[0];

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      ignoreBackdropClick: true,
      initialState: {
        workOrderData: workOrderData,
        primaryFieldtTechdata: []
      },
      class: 'rescheduler-modal'
    };
    this.dashboardService.openReschedulerModal(ReschedulerModalComponent, ngModalOptions, ModalDashboardComponent);
  }

  getWorkOrder(workOrders: WorkOrder): Promise<WorkOrderData[]> {
    return new Promise((resolve, reject) => {
      this.loading = true;
      this.subscription.add(
        this.workorderService.getWorkOrder(workOrders).subscribe({
          next: res => {
            resolve(res.workOrders);
            this.loading = false;
          },
          error: () => {
            reject();
            this.loading = false;
          }
        })
      );
    });
  }

  openDropBoxImageGallery() {
    const WoDetailsMessage = this.message.workorders[0];
    const requestParamsConfig = {
      id: 0,
      customerId: WoDetailsMessage.customerId,
      portfolioId: WoDetailsMessage.portfolioId,
      siteId: WoDetailsMessage.siteId,
      entityId: this.eventId,
      entityNumber: WoDetailsMessage.workOrderNumber,
      isCustomerFacing: false,
      imagePreviewId: 0,
      moduleType: 1,
      sortBy: '',
      direction: '',
      page: 0,
      itemsCount: 15
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-dialog image-gallery-modal',
      initialState: {
        requestParamsConfig: requestParamsConfig
      }
    };
    // using the dashboard service to open the modal as we have multiple modal to show and need to maintain the state.
    this.modalRef = this.modalService.show(ImageDropboxGalleryComponent, ngModalOptions);
    this.onClose();
  }

  openTemplateModal(template: TemplateRef<any>, assesmentId, isTrq = false, isApiCall = true) {
    this.assessmentId = this.assessmentId ? this.assessmentId : assesmentId;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-xl'
    };

    setTimeout(() => {
      if (isTrq) {
        this.viewModuleTorqueFormsRef = this.modalService.show(template, ngModalOptions);
      } else {
        this.viewFromModalRef = this.modalService.show(template, ngModalOptions);
      }
      if (isApiCall) {
        if (isTrq) {
          this.getTorqueViewFormsForQESTForms();
        } else {
          this.getViewFormsForQESTForms();
        }
      }
    }, 0);
  }

  onOpenHistoryModal(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    setTimeout(() => {
      this.historyModalRef = this.modalService.show(template, ngModalOptions);
    }, 0);
  }

  getHistoryForQESTForms(template: TemplateRef<any>, viewForms: ViewFormsList, isTRQ = false) {
    this.viewFromDetails = viewForms;
    this.loading = true;
    this.subscription.add(
      this.workorderService.getCompletedFormNoteHistory(viewForms.qestwoMapId).subscribe({
        next: (res: any) => {
          if (res) {
            this.viewFromModalRef ? this.viewFromModalRef.hide() : null;
            this.viewModuleTorqueFormsRef ? this.viewModuleTorqueFormsRef.hide() : null;
            this.onOpenHistoryModal(template);
            this.formEditNoteHistory = res;
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getViewFormsForQESTForms(reqParam = null) {
    this.loading = true;
    const params = {
      workorderId: this.eventId,
      qESTFormId: null,
      isApplyPagination: true,
      sortBy: 'UpdatedDate',
      direction: 'desc',
      page: reqParam ? reqParam.page : 0,
      itemsCount: reqParam ? reqParam.itemsCount : this.pagination.itemsCount
    };
    this.subscription.add(
      this.workorderService.getWorkOrderViewFormsList(params).subscribe({
        next: (res: ViewFormsResponse) => {
          if (res) {
            this.pagination.totalCount = res.totalMappedQESTForm;
            this.uploadedFormsData = res.listOfMappedQESTForm;
            this.completedFormMapId = this.uploadedFormsData.filter(form => form.formStatus === 3).map(form => form.qestwoMapId);
            this.loading = false;
            this._bsModalRef.setClass('d-none');
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getTorqueViewFormsForQESTForms(reqParam = null) {
    this.loading = true;
    const params = {
      workorderId: this.eventId,
      qESTFormId: null,
      isApplyPagination: true,
      sortBy: 'UpdatedDate',
      direction: 'desc',
      page: reqParam ? reqParam.page : 0,
      itemsCount: reqParam ? reqParam.itemsCount : this.pagination.itemsCount
    };
    this.subscription.add(
      this.workorderService.viewModuleTorqueForms(params).subscribe({
        next: (res: AvailableModuleTorqueFormsResponse) => {
          if (res) {
            this.pagination.totalCount = res.totalMappedQESTForm;
            this.selectedZones = res.listOfMappedQESTForm;
            this.completedMTFormMapId = this.selectedZones.filter(form => form.formStatus === 3).map(form => form.qestwoMapId);
            this.isAnyFormStartOrComplete = this.selectedZones.some(inverter => inverter.formStatus === 1 || inverter.formStatus === 3);
            this.loading = false;
            this._bsModalRef.setClass('d-none');
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  downloadUploadedForm(viewForms, isTRQ = false) {
    this.loading = true;

    this.workorderService.downloadQESTFrom(viewForms.qestwoMapId).subscribe({
      next: (res: Blob) => {
        const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
        if (isTRQ) {
          link.download = `${viewForms?.siteName ? viewForms?.siteName : ''} - ${
            viewForms?.workorderName ? viewForms.workorderName : ''
          } - ${viewForms.zoneName}.pdf`;
        } else {
          link.download = `${viewForms.formName}-${viewForms.deviceName}.pdf`;
        }
        link.click();
        this.loading = false;
      },
      error: _e => {
        this.loading = false;
      }
    });
  }

  onDeleteUploadedForm(qestWoMapId: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.deleteViewForm(qestWoMapId);
      }
    });
  }

  deleteViewForm(qestWoMapId) {
    this.subscription.add(
      this.workorderService.deleteUploadedForm(qestWoMapId).subscribe({
        next: (res: any) => {
          if (res) {
            this.getViewFormsForQESTForms();
            this.alertService.showSuccessToast(res.message);
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onChangeSize(isTRQ = false) {
    this.pagination.currentPage = 0;
    this.pagination.itemsCount = Number(this.pagination.pageSize);
    const params = {
      page: 0,
      itemsCount: this.pagination.itemsCount
    };
    if (isTRQ) {
      this.getTorqueViewFormsForQESTForms(params);
    } else {
      this.getViewFormsForQESTForms(params);
    }
  }

  onPageChange(obj, isTRQ = false) {
    this.pagination.currentPage = obj;
    const params = {
      page: obj - 1,
      itemsCount: this.pagination.itemsCount
    };
    if (isTRQ) {
      this.getTorqueViewFormsForQESTForms(params);
    } else {
      this.getViewFormsForQESTForms(params);
    }
  }

  getStatusLabel(status: number) {
    switch (status) {
      case FormStatus.NotStarted:
        return 'Not Started';
      case FormStatus.InProgress:
        return 'In Progress';
      case FormStatus.Draft:
        return 'Draft';
      case FormStatus.Completed:
        return 'Completed';
      default:
        return 'Not Started';
    }
  }

  goToFormFillingPage(viewForms: ViewFormsList, fromWhichModal) {
    const data = this.message.workorders.filter(x => x.label === 'Work order detail');
    this.router.navigate(['/entities/operations/custom-forms/fill-form', viewForms.qestwoMapId], {
      queryParams: {
        id: this.assessmentId,
        workOrderId: this.eventId,
        formId: viewForms.qestFormId,
        assementType: data[0].assementType,
        pageOpenFrom: fromWhichModal,
        isPmReviewForm: true
      }
    });
    this.viewFromModalRef ? this.viewFromModalRef.hide() : null;
    this.viewModuleTorqueFormsRef ? this.viewModuleTorqueFormsRef.hide() : null;
    this._bsModalRef.hide();
  }

  downloadAllForms(isTRQ = false) {
    this.loading = true;
    let completedFormsId = [];
    if (isTRQ) {
      completedFormsId = this.selectedZones.filter(form => form.formStatus === 3).map(form => form.qestwoMapId);
    } else {
      completedFormsId = this.uploadedFormsData.filter(form => form.formStatus === 3).map(form => form.qestwoMapId);
    }
    const params = {
      listOfQESTWOMapId: completedFormsId,
      workOrderId: this.eventId
    };
    this.workorderService.downloadAllViewModalQESTFrom(params).subscribe({
      next: (res: DownloadAllViewFormResponse) => {
        this.alertService.showSuccessToast(res.message);
        this.loading = false;
      },
      error: _e => {
        this.loading = false;
      }
    });
  }

  allowRescheduleWO(message) {
    const workOrderData = message?.workorders[0]?.data || [];
    const reportData = message?.workorders[1]?.data || [];
    const findByLabel = (dataArray, label) => dataArray.find(item => item.label === label);

    const workOrderStatus = findByLabel(workOrderData, 'Work Order Status');
    let reportCompleteDate = findByLabel(reportData, 'Report Complete Date');
    const scheduledDate = findByLabel(workOrderData, 'Scheduled Date');
    const workorderType = findByLabel(workOrderData, 'Type of Work Order');
    if (!reportCompleteDate) {
      reportCompleteDate = { value: '' };
    }
    if (
      this.editPermission &&
      workorderType?.value !== 'Medium Voltage PM' &&
      workOrderStatus?.value !== 'Report Complete' &&
      reportCompleteDate &&
      (reportCompleteDate.value === '' || reportCompleteDate.value === '-') &&
      scheduledDate &&
      scheduledDate.value !== '' &&
      scheduledDate.value !== '-' &&
      checkAuthorisations([ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER])
    ) {
      return true;
    }
    return false;
  }

  buttonCondition() {
    this.allowStartNewReport = false;
    const workOrderData = this.message?.workorders[0]?.data || [];

    const workOrderStatus = findByLabel(workOrderData, 'Work Order Status');
    const workorderType = findByLabel(workOrderData, 'Type of Work Order');
    this.newReportData.reportType = workorderType.value === 'Site Visit' ? 1 : workorderType.value === 'Medium Voltage PM' ? 3 : 0;

    if (workorderType.value === 'Site Visit' || workorderType.value === 'Medium Voltage PM') {
      this.allowStartNewReport = !(
        workOrderStatus.value === WO_STATUSES.FIELD_WORK_COMPLETE.name ||
        workOrderStatus.value === WO_STATUSES.REPORT_COMPLETE.name ||
        workOrderStatus.value === WO_STATUSES.REPORT_STARTED.name ||
        workOrderStatus.value === WO_STATUSES.REPORT_DRAFTED.name
      );
    } else {
      this.allowStartNewReport = false;
    }
  }

  checkReportCreationStatus() {
    const workOrderData = this.message?.workorders[0]?.data || [];
    const workorderType = findByLabel(workOrderData, 'Type of Work Order');
    if (workorderType.value === 'Site Visit' || workorderType.value === 'Medium Voltage PM') {
      this.subscription.add(
        this.reportService.getDataById(this.workorderDetails.workorderId, 'Order', true).subscribe({
          next: report => {
            if (report.reports.length) {
              // report is already exist for this WO
              // this.newReportBtnText = 'Update Report';
              this.isReportExistForWO = true;
              this.newReportData.id = report.reports[0].reportGuid;
            } else {
              this.isReportExistForWO = false;
              this.newReportData.id = '';
            }
            this.buttonCondition();
          },
          error: () => {
            // if we are here it could mean the report is not generated for this WO.
            // update details for report creation
            this.newReportData.id = null;
            this.newReportData.isFromMobileUploaded = false;
            this.newReportData.customerId = this.message.workorders[0].customerId;
            this.newReportData.portfolioId = this.message.workorders[0].portfolioId;
            this.newReportData.siteId = this.message.workorders[0].siteId;
            this.newReportData.workorderId = this.workorderId;
            // this.newReportBtnText = 'Start New Report';
            this.isReportExistForWO = false;
            this.buttonCondition();
          }
        })
      );
    }
  }

  updateWorOderStatus(params) {
    this.reportService.updateWorkOrderStatus(params).subscribe({
      next: res => {
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  commonWoStatusUpdateLogic(data) {
    if (data[0].assementType === 'SV' || data[0].assementType === 'MVPM') {
      const param = {
        woId: this.workorderId,
        woStatusId: WO_STATUSES.FIELD_WORK_PARTIALLY_COMPLETE.id
      };
      this.updateWorOderStatus(param);
    }
  }

  startNewReport() {
    const data = this.message.workorders.filter(x => x.label === 'Work order detail');
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Do you want to create a JHA or continue to Report?',
        confirmBtnText: 'Create JHA',
        cancelBtnText: 'Continue to Report',
        hideCloseIcon: true
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (this.isReportExistForWO && !result) {
        // continue to report
        this.commonWoStatusUpdateLogic(data);
        this.onClose();
        this.onViewReport(this.message?.workorders[0].assementType);
      } else if (this.isReportExistForWO && result) {
        this.commonWoStatusUpdateLogic(data);
        // create JHA
        this.onClose();
        this.router.navigate(['/entities/safety/jha/upload'], {
          queryParams: {
            checkin: 1,
            cust: this.workorderDetails.customerId,
            port: this.workorderDetails.portfolioId,
            site: this.workorderDetails.siteId,
            workorderId: this.workorderDetails.workorderId,
            workOrderNumber: this.message?.workorders[0].workOrderNumber,
            reportId: this.newReportData.id,
            returnTo: 'Dashboard'
          }
        });
      } else if (!this.isReportExistForWO && result) {
        // report is not created, user opted to create JHA. Create report and then Goto JHA
        // generate the report with empty JHA then redirect to JHA create screen and then report fill
        this.commonWoStatusUpdateLogic(data);
        const user = this.storageService.get('user');
        // /api/mobile/v2/uploadreportdata
        const date = new Date();
        const offset = -date.getTimezoneOffset(); // Get offset in minutes
        const offsetHours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, '0');
        const offsetMinutes = String(Math.abs(offset) % 60).padStart(2, '0');
        const timeZoneOffset = (offset >= 0 ? '+' : '-') + offsetHours + ':' + offsetMinutes;

        const localDateWithOffset =
          date.getFullYear() +
          '-' +
          String(date.getMonth() + 1).padStart(2, '0') +
          '-' +
          String(date.getDate()).padStart(2, '0') +
          'T' +
          String(date.getHours()).padStart(2, '0') +
          ':' +
          String(date.getMinutes()).padStart(2, '0') +
          ':' +
          String(date.getSeconds()).padStart(2, '0') +
          '.' +
          String(date.getMilliseconds()).padStart(3, '0') +
          timeZoneOffset;

        this.generateNewReportData = {
          reportId: null, // we will get the report id from the API success.
          reportName: this.message?.workorders[0].workOrderNumber,
          siteId: this.message?.workorders[0].siteId,
          reportTypeId: this.message?.workorders[0].workOrderNumber.includes('SV-')
            ? ReportTypes.reportTypeSVId
            : ReportTypes.reportTypeMVPMId, // check here what type of wo it is then decide
          workorderId: this.workorderDetails.workorderId,
          reporterId: user.userId,
          reportCreatedDate: localDateWithOffset,
          reportVersion: 3, // fixed to 3 for now
          miscellaneous: '',
          comment: null,
          uploadJHA: null,
          uploadJHAv3ReportIds: [],
          uploadEquipmentStatus: null,
          uploadChecklist: null,
          uploadNC: null,
          isFromMobileUploaded: false
        };

        this.reportService.createNewSVReport(this.generateNewReportData).subscribe({
          next: res => {
            this.alertService.showSuccessToast(res.message);
            this.loading = false;
            this.onClose();
            this.router.navigate(['/entities/safety/jha/upload'], {
              queryParams: {
                checkin: 1,
                cust: this.workorderDetails.customerId,
                port: this.workorderDetails.portfolioId,
                site: this.workorderDetails.siteId,
                workorderId: this.workorderDetails.workorderId,
                workOrderNumber: this.message?.workorders[0].workOrderNumber,
                reportId: res.id,
                returnTo: 'Dashboard'
              }
            });
          },
          error: e => {
            this.loading = false;
          }
        });
      } else if (!this.isReportExistForWO && !result) {
        // report is not created, user opted to continue to Report. Create report then Goto Report
        this.commonWoStatusUpdateLogic(data);
        const user = this.storageService.get('user');
        // /api/mobile/v2/uploadreportdata
        const date = new Date();
        const offset = -date.getTimezoneOffset(); // Get offset in minutes
        const offsetHours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, '0');
        const offsetMinutes = String(Math.abs(offset) % 60).padStart(2, '0');
        const timeZoneOffset = (offset >= 0 ? '+' : '-') + offsetHours + ':' + offsetMinutes;

        const localDateWithOffset =
          date.getFullYear() +
          '-' +
          String(date.getMonth() + 1).padStart(2, '0') +
          '-' +
          String(date.getDate()).padStart(2, '0') +
          'T' +
          String(date.getHours()).padStart(2, '0') +
          ':' +
          String(date.getMinutes()).padStart(2, '0') +
          ':' +
          String(date.getSeconds()).padStart(2, '0') +
          '.' +
          String(date.getMilliseconds()).padStart(3, '0') +
          timeZoneOffset;

        this.generateNewReportData = {
          reportId: null, // we will get the report id from the API success.
          reportName: this.message?.workorders[0].workOrderNumber,
          siteId: this.message?.workorders[0].siteId,
          reportTypeId: this.message?.workorders[0].workOrderNumber.includes('SV-')
            ? ReportTypes.reportTypeSVId
            : ReportTypes.reportTypeMVPMId, // check here what type of wo it is then decide
          workorderId: this.workorderDetails.workorderId,
          reporterId: user.userId,
          reportCreatedDate: localDateWithOffset,
          reportVersion: 3, // fixed to 3 for now
          miscellaneous: '',
          comment: null,
          uploadJHA: null,
          uploadJHAv3ReportIds: [],
          uploadEquipmentStatus: null,
          uploadChecklist: null,
          uploadNC: null,
          isFromMobileUploaded: false
        };

        this.reportService.createNewSVReport(this.generateNewReportData).subscribe({
          next: res => {
            this.alertService.showSuccessToast(res.message);
            this.loading = false;
            this.onClose();
            this.onViewReport(this.message?.workorders[0].assementType);
          },
          error: e => {
            this.loading = false;
          }
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
