import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerService } from '../../customer-management/customer.service';
import { ContractPdfViewerComponent } from './add-edit-contract/contract-pdf-viewer/contract-pdf-viewer.component';
import { ContractService } from './contract.service';
import {
  ContractHistory,
  ContractObject,
  ContractSiteDDL,
  ContractStatuses,
  ContractsDTO,
  CustomerContractSites,
  SiteContractFilterDropsValues,
  SiteContractFilters,
  SiteContractUpdateRequest,
  Types,
  roleIdOrder
} from './contracts.model';
import { ROLE_TYPE } from '../../../@shared/enums';

@Component({
  selector: 'sfl-contracts',
  templateUrl: './contracts.component.html',
  styleUrls: ['./contracts.component.scss']
})
export class ContractsComponent implements OnInit, OnDestroy {
  loading = false;
  customerList = [];
  selectedCustomer;
  today = new Date();
  contractsDTO: ContractsDTO[] = [];
  selectedContract: ContractObject;
  types = Object.keys(Types).map(key => ({ key, name: Types[key] }));
  contractStatusFilterList = Object.keys(ContractStatuses).map(id => ({ id, name: ContractStatuses[id] }));
  selectedType = 'TECHNICIAN';
  selectedStatusFilter: string[] = [];
  technicianBaseRatesByFinancialYears = [];
  electricianBaseRatesByFinancialYears = [];
  mvTechnicianBaseRatesByFinancialYears = [];
  bessTechnicianBaseRatesByFinancialYears = [];
  administrativeBaseRatesByFinancialYears = [];
  engineeringBaseRatesByFinancialYears = [];
  subscription: Subscription = new Subscription();
  roleIdOrder = roleIdOrder;

  contractMonths: { key: number; name: string }[] = [
    { key: 1, name: 'January' },
    { key: 2, name: 'February' },
    { key: 3, name: 'March' },
    { key: 4, name: 'April' },
    { key: 5, name: 'May' },
    { key: 6, name: 'June' },
    { key: 7, name: 'July' },
    { key: 8, name: 'August' },
    { key: 9, name: 'September' },
    { key: 10, name: 'October' },
    { key: 11, name: 'November' },
    { key: 12, name: 'December' }
  ];
  selectedContractYear: number;
  selectedContractMonth: number;
  customerContractSites: CustomerContractSites[];
  customerContractSitesCopy: CustomerContractSites[];
  user: any;
  editSiteMode = false;
  selectedContractForAllSite = null;
  customerContracts: ContractSiteDDL[] = [];
  selectedSites: any[];
  selectedAllSite: any;
  siteContractUpdate: SiteContractUpdateRequest = new SiteContractUpdateRequest();
  @ViewChild('importContract') contractFileInput;
  @ViewChild('contractSiteForm') contractSiteForm: NgForm;
  contractHistories: ContractHistory[] = [];
  viewFilterSection = 'opContractsFilterSection';
  viewPage: string = 'contractsPage';
  sortOptionList = {
    PortfolioName: 'asc',
    SiteName: 'asc',
    State: 'asc',
    SubregionName: 'asc',
    RegionName: 'asc',
    ContractName: 'asc',
    SiteActive: 'asc',
    ContractActive: 'asc'
  };
  filterModel: SiteContractFilters = new SiteContractFilters();
  alertRatesChange = false;
  siteContractFilterValues: SiteContractFilterDropsValues = new SiteContractFilterDropsValues();
  contractToOpen: number;
  clearOnDestory = true;
  modalRef: BsModalRef;
  @ViewChild('contactViewModal', { static: false }) contactViewModal: TemplateRef<any>;
  contractLinkUrl: SafeResourceUrl = null;
  roleType = ROLE_TYPE;

  constructor(
    private readonly customerService: CustomerService,
    private readonly router: Router,
    private readonly contractService: ContractService,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.user = this.storageService.get('user').authorities;
    this.getCustomers();
  }

  getContractByCustomers() {
    const filter =
      this.selectedStatusFilter.length === 2 || !this.selectedStatusFilter.length
        ? undefined
        : this.selectedStatusFilter[0] === 'ACTIVE'
        ? true
        : false;
    this.loading = true;
    this.subscription.add(
      this.contractService.getContractsByCustomer(this.selectedCustomer, filter).subscribe({
        next: (contracts: ContractsDTO[]) => {
          this.contractsDTO = contracts;
          this.loading = false;
          this.setTheMinYearAndMaxYearForEachContract();
          this.getSitesByCustomer();
          this.getCustomerContracts();
          this.subscription.add(this.contractService.setSelectedCustomer(this.selectedCustomer));
          // store the filter to the storage and use it for further usecase
          const filterModel = { selectedCustomer: this.selectedCustomer, selectedStatusFilter: this.selectedStatusFilter };
          this.storageService.set(this.viewPage, filterModel);
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  setTheMinYearAndMaxYearForEachContract() {
    // numberOfTotalContractYears
    for (const [index, contract] of this.contractsDTO.entries()) {
      if (contract.contract.recalculateRequired) {
        if (!this.alertRatesChange) this.alertRatesChange = true;
      }
      const startYear = new Date(contract.contract.startDate).getFullYear();
      contract.startingMonthOfTheContract = new Date(contract.contract.startDate).getMonth() + 1;
      const endYear = new Date(contract.contract.endDate).getFullYear();
      contract.contract.numberOfTotalContractYears = [];
      for (let year = startYear; year <= endYear; year++) {
        contract.contract.numberOfTotalContractYears.push(year);
      }
      // setting up the default month and year for the rates by month table
      // if the current day is 3/5/24, it should show the february 2024.
      // if february is out of the contract range then pick last month and year of the contract period.
      // Check if the current date is within the contract range
      const today = new Date();
      const thisYear = new Date(today).getFullYear();
      const thisMonth = new Date(today).getMonth() + 1;
      let contractStartDate = new Date(contract.contract.startDate);
      let contractEndDate = new Date(contract.contract.endDate);
      // Check if the current date is within the contract range
      if (thisYear === startYear && thisMonth === contract.startingMonthOfTheContract) {
        // If yes, use the current year and month
        contract.contract.selectedYear = today.getFullYear();
        contract.contract.selectedMonth = today.getMonth() + 1;
      } else if (today >= contractStartDate && today <= contractEndDate) {
        // If yes, use the current year and month
        contract.contract.selectedYear = today.getFullYear();
        contract.contract.selectedMonth = today.getMonth();
      } else if (today < contractStartDate) {
        // If the current date is before the contract start date, set the selected year and month to the contract end date
        contract.contract.selectedYear = contractStartDate.getFullYear();
        contract.contract.selectedMonth = contractStartDate.getMonth() + 1;
      } else {
        // If the current date is after the contract end date, set the selected year and month to the contract end date
        contract.contract.selectedYear = contractEndDate.getFullYear();
        contract.contract.selectedMonth = contractEndDate.getMonth() + 1;
      }
    }
    if (this.contractService.contractToOpen) {
      this.contractsDTO.filter(x => x.contract.id === Number(this.contractService.contractToOpen))[0].expanded = true;
      setTimeout(() => {
        this.scrollTo('contract' + this.contractService.contractToOpen);
      }, 1000);
    } else {
      if (this.contractsDTO.length) this.contractsDTO[0].expanded = true;
    }
    // if there has been rates change detected we will prompt user for the same
    if (this.alertRatesChange && this.contractService.showRateChangeAlert) {
      this.ratesChangesDetected();
    }
  }

  scrollTo(elementId: string) {
    document.getElementById(elementId).scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest'
    });
  }

  getCustomers() {
    this.loading = true;
    this.customerService.getAllCustomer().subscribe(customers => {
      this.customerList = customers;
      const filter = this.storageService.get(this.viewPage);
      this.loading = false;
      if (filter && filter.selectedCustomer) {
        this.selectedCustomer = filter.selectedCustomer;
        this.selectedStatusFilter = filter.selectedStatusFilter;
        this.filterModel.customerId = this.selectedCustomer;
        this.getContractByCustomers();
      }
    });
  }

  customerChanged(event) {
    // call the api to get the contract for selected customer
    this.contractService.showRateChangeAlert = true;
    this.siteContractFilterValues.portfolios = [];
    this.alertRatesChange = false;
    if (event) {
      this.getContractByCustomers();
    } else {
      this.subscription.add(this.contractService.setSelectedCustomer(null));
      this.selectedStatusFilter = [];
      const filterModel = { selectedCustomer: null, selectedStatusFilter: null };
      this.storageService.set(this.viewPage, filterModel);
    }
  }

  getCustomerContracts() {
    this.loading = true;
    this.subscription.add(
      this.contractService.getContractListByCustomer(this.selectedCustomer).subscribe({
        next: (contracts: ContractSiteDDL[]) => {
          this.customerContracts = contracts;
          this.customerContracts.push({
            abbreviation: null,
            customerId: null,
            id: 0,
            isActive: null,
            isArchive: null,
            isAutomationSite: null,
            name: 'No Contract',
            portfolioId: null,
            siteNumber: null,
            endDate: null
          });
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  getSitesByCustomer() {
    this.loading = true;
    this.filterModel.customerId = this.selectedCustomer;
    this.subscription.add(
      this.contractService.getSitesByCustomer(this.filterModel).subscribe({
        next: (sites: CustomerContractSites[]) => {
          this.loading = false;
          this.customerContractSites = sites;
          this.customerContractSitesCopy = JSON.parse(JSON.stringify(sites));
          if (!this.siteContractFilterValues.portfolios.length) this.getSiteContractFilterDrops();
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  getSiteContractFilterDrops() {
    this.loading = true;
    this.subscription.add(
      this.contractService.siteContractFilterDrops(this.selectedCustomer).subscribe({
        next: (res: SiteContractFilterDropsValues) => {
          this.siteContractFilterValues = res;
          this.siteContractFilterValues.contracts.push({
            abbreviation: null,
            customerId: null,
            id: 0,
            isActive: null,
            isArchive: null,
            isAutomationSite: null,
            name: 'No Contract',
            portfolioId: null,
            siteNumber: null,
            endDate: null
          });
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  updateContract(contractId: number) {
    this.router.navigate([`/entities/operations/contracts/update/${contractId}`]);
    this.clearOnDestory = false;
  }

  selectAllStatus(selectAll: boolean) {
    this.selectedStatusFilter = [];
    if (selectAll) {
      this.contractStatusFilterList.forEach(status => {
        this.selectedStatusFilter.push(status.id);
      });
    } else {
      this.selectedStatusFilter = [];
    }
    this.getContractByCustomers();
  }

  // marks the provided contract as inactive
  makeContractActiveInactive(event, contract) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `Are you sure you want to ${contract.isActive ? 'Inactive ' : 'Activate '} ${contract.contractName} Contract?`
      }
    };
    const modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    modalRef.content.onClose.subscribe({
      next: result => {
        if (result) {
          this.loading = true;
          this.subscription.add(
            this.contractService.markContractActiveOrInactive(contract.id, !contract.isActive).subscribe({
              next: res => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                this.getContractByCustomers();
                this.getSiteContractFilterDrops();
              },
              error: () => {
                this.loading = false;
              }
            })
          );
        }
      },
      error: () => (this.loading = false)
    });
  }

  toggleEditMode() {
    this.editSiteMode = !this.editSiteMode;
    if (!this.editSiteMode) {
      this.selectedAllSite = false;
      this.selectedContractForAllSite = null;
      this.customerContractSites = JSON.parse(JSON.stringify(this.customerContractSitesCopy));
    }
  }

  processTheSelectedContractForSites(): Promise<void> {
    return new Promise(resolve => {
      this.siteContractUpdate.siteContract = [];
      // check if selectedAllSite is true, then the selectedContractForAllSite contract will apply to all the filtered site.
      // if not, based on the individual selection the contract to site mapping will take place.
      // new feedback changes
      for (const sites of this.customerContractSites) {
        if (sites.isSelected) {
          sites.contractId = this.selectedContractForAllSite;
          this.siteContractUpdate.siteContract.push({
            contractId: this.selectedContractForAllSite === 0 ? null : this.selectedContractForAllSite,
            siteId: sites.siteId
          });
        } else if (sites.isUpdated)
          this.siteContractUpdate.siteContract.push({
            contractId: sites.contractId === 0 ? null : sites.contractId,
            siteId: sites.siteId
          });
      }
      resolve();
    });
  }

  async applySiteContractChange() {
    await this.processTheSelectedContractForSites();
  }

  selectAllSites() {
    this.selectedAllSite = !this.selectedAllSite;
    for (const site of this.customerContractSites) {
      site.isUpdated = site.isSelected = this.selectedAllSite;
    }
  }

  siteCheckboxChanged(event, index: number) {
    this.customerContractSites[index].isSelected = event.target.checked;
    this.customerContractSites[index].isUpdated = event.target.checked;
    const notSelectedSite = this.customerContractSites.findIndex(x => !x.isSelected);
    if (notSelectedSite !== -1) {
      this.selectedAllSite = false;
    } else {
      this.selectedAllSite = true;
    }
  }

  updateSiteSelection(index) {
    this.customerContractSites[index].isUpdated = true;
  }

  saveSiteContractMapping() {
    // show a popup here, checj the ticket description
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `Are you sure you want to proceed with this action?`
      }
    };
    const modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    modalRef.content.onClose.subscribe({
      next: async result => {
        if (result) {
          await this.processTheSelectedContractForSites();
          if (!this.siteContractUpdate.siteContract.length) {
            this.alertService.showSuccessToast('We are sorry but, there is nothing to update! Please try again');
            return;
          }
          this.loading = true;
          this.subscription.add(
            this.contractService.updateSiteContract(this.siteContractUpdate).subscribe({
              next: res => {
                this.loading = false;
                this.alertService.showSuccessToast(res.message);
                this.siteContractFilterValues.portfolios = [];
                this.getSitesByCustomer();
                this.toggleEditMode();
              },
              error: () => {
                this.loading = false;
              }
            })
          );
        }
      }
    });
  }

  clickImport() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `Are you sure you want to Import a Contract?`
      }
    };
    const modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    modalRef.content.onClose.subscribe({
      next: result => {
        if (result) {
          this.contractFileInput.nativeElement.click();
        }
      }
    });
  }

  importContractFile(files, contract) {
    if (files.length > 0) {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('customerId', contract.customerId.toString());
      formData.append('contractId', contract.id.toString());
      formData.append('uploadedFile', files[0] as File);
      this.subscription.add(
        this.contractService.importContract(formData).subscribe({
          next: res => {
            this.loading = false;
            this.alertService.showSuccessToast('Contract Imported Successfully!');
            this.contractFileInput.nativeElement.value = '';
            this.getContractByCustomers();
          },
          error: () => {
            this.contractFileInput.nativeElement.value = '';
            this.loading = false;
          }
        })
      );
    }
  }

  exportContract(contract) {
    this.loading = true;
    this.subscription.add(
      this.contractService.exportContract(contract.id).subscribe({
        next: res => {
          const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
          link.download = `${contract.contractName}.xlsx`;
          link.click();
          this.alertService.showSuccessToast(`${contract.contractName}.xlsx, exported successfully.`);
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  reCalculateRates(contract) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `All rate types will be generated based on the base rates and the contract start and end dates. Any existing rate values will be overwritten. Do you wish to proceed?`
      }
    };
    const modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    modalRef.content.onClose.subscribe({
      next: result => {
        if (result) {
          this.loading = true;
          this.subscription.add(
            this.contractService.generateAllValues(contract.id).subscribe({
              next: res => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                this.alertRatesChange = false;
                this.getContractByCustomers();
              },
              error: () => {
                this.loading = false;
              }
            })
          );
        }
      }
    });
  }

  onChangeHistoryAccordian(event, contract, index) {
    contract.historyExpanded = !contract.historyExpanded;

    if (!contract.historyExpanded) return;
    this.loading = true;
    this.subscription.add(
      this.contractService.contractHistory(contract.contract.id).subscribe({
        next: history => {
          this.contractsDTO[index].contractHistories = history;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getSitesByCustomer();
  }

  filterChange() {
    this.getSitesByCustomer();
  }

  clear(item) {}

  trackByFunc(index, item) {
    return item.siteId;
  }
  trackByFuncContract(index, item) {
    return item.contract.id;
  }

  ratesChangesDetected() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `Some contract has been updated manually and for those the rates need to be re-generated for ${
          this.customerList.filter(x => x.id === this.selectedCustomer)[0].name
        }!`,
        isWarning: true,
        confirmBtnText: 'Okay',
        showCancelButton: false
      }
    };
    const modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    modalRef.content.onClose.subscribe({
      next: () => {
        this.contractService.showRateChangeAlert = false;
      }
    });
  }

  getRateByRoleId(roles: any[], roleId: number): number | null {
    const role = roles.find(r => r.roleId === roleId);
    return role?.rate ?? null;
  }
  getOvertimeByRoleId(roles: any[], roleId: number): number | null {
    const role = roles.find(r => r.roleId === roleId);
    return role?.overtimeRate ?? null;
  }
  getWeekendRateByRoleId(roles: any[], roleId: number): number | null {
    const role = roles.find(r => r.roleId === roleId);
    return role?.weekendRate ?? null;
  }
  getHolidayRateByRoleId(roles: any[], roleId: number): number | null {
    const role = roles.find(r => r.roleId === roleId);
    return role?.holidayRate ?? null;
  }

  openContractLinkReport(url: string) {
    if (!url) {
      this.alertService.showErrorToast('No contract has been linked, please link contract to view.');
      return;
    }

    this.loading = false;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        pdfUrl: url
      }
    };

    this.modalRef = this.modalService.show(ContractPdfViewerComponent, ngModalOptions);
  }

  ngOnDestroy(): void {
    this.contractService.contractToOpen = null;
    if (this.clearOnDestory) this.contractService.showRateChangeAlert = true;
  }
}
