<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-4">
        <h6>Site Check-In</h6>
      </div>
      <div class="col-8 text-end">
        <button nbButton status="basic" type="button" (click)="goBack()" size="medium" class="m-1">
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
        <button
          *ngIf="isDetail && checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER])"
          (click)="gotoEditPage()"
          nbButton
          status="primary"
          size="medium"
          type="button"
          id="siteSubmit"
          class="m-1"
        >
          <span class="d-none d-lg-inline-block">Edit</span>
          <i class="d-inline-block d-lg-none fa-solid fa-pen"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="form-control-group">
      <div class="col-12 table-responsive table-card-view">
        <table class="table table-header-rotated table-bordered" aria-describedby="Site List">
          <thead>
            <tr>
              <th class="Customer" id="Date">
                <div class="d-flex align-items-center">Date</div>
              </th>
              <th class="Portfolio" id="User">
                <div class="d-flex align-items-center">User</div>
              </th>
              <th class="Customer" id="Customer">
                <div class="d-flex align-items-center">Customer</div>
              </th>
              <th class="date" id="totaldriveTime">
                <div class="d-flex align-items-center">Portfolio</div>
              </th>
              <th class="Site" id="Site">
                <div class="d-flex align-items-center">Site</div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-title="Date">{{ date | date : 'MM/dd/yyyy' }}</td>
              <td data-title="User" class="td-custom-width">{{ userName }}</td>
              <td data-title="Customer" class="td-custom-width">{{ customerName }}</td>
              <td data-title="Portfolio" class="td-custom-width">{{ portfolioName }}</td>
              <td data-title="Site" class="td-custom-width">{{ siteName }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div *ngFor="let item of auditData | orderBy : 'order'; let i = index">
      <ng-container
        [ngTemplateOutlet]="item.status === 1 ? checkInTemplate : item.status === 2 ? checkOutTemplate : reOpenTemplate"
        [ngTemplateOutletContext]="{ item: item, index: i }"
      ></ng-container>
    </div>

    <ng-template #checkInTemplate let-item="item" let-index="index">
      <nb-accordion class="mb-3">
        <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head">
            Check-In Form (Check-In Time: {{ item?.date | date : 'M/d/yyyy, h:mm a' }})
          </nb-accordion-item-header>
          <nb-accordion-item-body>
            <form name="siteCheckInForm" #siteCheckInForm="ngForm">
              <div class="row">
                <div class="col-12 col-sm-12 col-md-6 mb-2">
                  <div class="row">
                    <div class="col-12 col-sm-5 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >Site Check-In Time<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item.date | date : 'h:mm a' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          [nbDatepicker]="SiteCheckInDateTimePicker"
                          name="siteCheckInDate"
                          [(ngModel)]="item.date"
                          fullWidth
                          class="form-control"
                          required
                          (ngModelChange)="checkInOutUtcDateChanged(item)"
                        />
                        <nb-date-timepicker [format]="dateTimeFormat" twelveHoursFormat #SiteCheckInDateTimePicker></nb-date-timepicker>
                        <sfl-error-msg [control]="date" fieldName="Site Check-In Time"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-7 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >SiteTimeZone Offset<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ offsetName }}</div>
                      <div *ngIf="isEdit">
                        <ng-select
                          name="productionLoss"
                          [items]="timeZoneList"
                          bindLabel="displayName"
                          bindValue="id"
                          #productionLoss="ngModel"
                          [(ngModel)]="item.siteTimeZoneOffset"
                          notFoundText="No TimeZone Found"
                          placeholder="Select TimeZone"
                          appendTo="body"
                          [clearable]="false"
                          id="input-timezone"
                          disabled
                        >
                        </ng-select>
                        <sfl-error-msg [control]="productionLoss" fieldName="Site TimeZone Offset"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-12 col-sm-12 col-md-6 mb-2">
                  <div class="row">
                    <div class="col-12 col-sm-7 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >UTC Check-In Time<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item?.utcTimeStamp | date : 'h:mm a' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          name="utcDate"
                          [nbDatepicker]="utcDateTimePicker"
                          [(ngModel)]="item.utcTimeStamp"
                          fullWidth
                          class="form-control"
                          disabled
                        />
                        <nb-date-timepicker [format]="dateTimeFormat" twelveHoursFormat #utcDateTimePicker></nb-date-timepicker>
                        <sfl-error-msg [control]="date" fieldName="UTC Check-In Time"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-5 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >Drive time to site - one way<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item?.driveTime || '-' }} Hours</div>
                      <div *ngIf="isEdit">
                        <input #driveTime name="driveTime" nbInput fullWidth [(ngModel)]="item.driveTime" class="form-control" required />
                        <sfl-error-msg [control]="driveTime" fieldName="Drive Time"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-6 mb-2">
                  <div class="form-control-group">
                    <label class="label detailView mb-2" for="input-latitude"
                      >Reason For Visit<span class="ms-1 text-danger">*</span></label
                    >
                    <div *ngFor="let items of item.reasonArrays; let j = index">
                      <nb-checkbox disabled name="item{{ j }}" [checked]="items.checked" [(ngModel)]="items.checked">
                        {{ items.name }}
                      </nb-checkbox>
                      <div *ngIf="isDetail">{{ items.val }}</div>
                      <div *ngIf="items.checked && items.name === 'Other' && isEdit">
                        <textarea
                          #reason
                          name="reason"
                          nbInput
                          [(ngModel)]="items.val"
                          class="textareaheight mt-1"
                          fullWidth
                          [required]="items.checked && items.name === 'Other'"
                        >
                        </textarea>
                        <sfl-error-msg [control]="reason" fieldName="Reason"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-6 mb-2">
                  <div class="form-control-group h-100">
                    <label class="label detailView mb-2" for="input-logitude">Notes</label>
                    <div class="mb-2" *ngIf="isDetail">{{ item?.notes || '-' }}</div>
                    <div *ngIf="isEdit" class="h-85">
                      <textarea #notes name="notes" nbInput [(ngModel)]="item.notes" class="textareaheight h-100" fullWidth> </textarea>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-6 mb-2">
                  <div class="row">
                    <div class="col-12 col-sm-6">
                      <label class="label detailView mb-2" for="input-city">Longitude <span class="ms-1 text-danger">*</span></label>
                      <div class="mb-2" *ngIf="isDetail">{{ item?.longitude || '-' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          #longitude
                          name="longitude"
                          fullWidth
                          [(ngModel)]="item.longitude"
                          class="form-control"
                          required
                          [maxlength]="15"
                          (change)="updateMap(0)"
                        />
                        <sfl-error-msg [control]="longitude" fieldName="Longitude"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-6">
                      <label class="label detailView mb-2" for="input-latitude">Latitude <span class="ms-1 text-danger">*</span></label>
                      <div class="mb-2" *ngIf="isDetail">{{ item?.latitude || '-' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          #latitude
                          name="latitude"
                          fullWidth
                          [(ngModel)]="item.latitude"
                          class="form-control"
                          required
                          [maxlength]="15"
                          (change)="updateMap(0)"
                        />
                        <sfl-error-msg [control]="latitude" fieldName="Latitude"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-6 mb-2">
                  <label class="label detailView mb-2" for="input-State">GPS Location<span class="ms-1 text-danger">*</span></label>
                  <div class="mb-2" *ngIf="isEdit || isDetail">
                    <ng-container
                      [ngTemplateOutlet]="mapToReload !== index ? gMap : showLoader"
                      [ngTemplateOutletContext]="{ mapNumber: 0, item: item }"
                    ></ng-container>
                  </div>
                </div>
                <div class="col-12 text-end mt-3">
                  <button
                    *ngIf="isEdit"
                    nbButton
                    status="primary"
                    size="medium"
                    type="button"
                    id="siteSubmit"
                    class="checkinbtn ms-auto"
                    (click)="submitCheckInForm(item)"
                    [disabled]="siteCheckInForm.invalid"
                  >
                    Save
                  </button>
                </div>
              </div>
            </form>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </ng-template>

    <ng-template #checkOutTemplate let-item="item" let-index="index">
      <nb-accordion class="mb-3">
        <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head">
            Check-Out Form(Check-out Time: {{ item.date | date : 'M/d/yyyy, h:mm a' }})
          </nb-accordion-item-header>
          <nb-accordion-item-body>
            <form name="siteCheckOutForm" #siteCheckOutForm="ngForm">
              <div class="row">
                <div class="col-12 col-sm-12 col-md-6 mb-2">
                  <div class="row">
                    <div class="col-12 col-sm-12 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >Site Check-Out Time<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item?.date | date : 'h:mm a' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          [nbDatepicker]="outDateTimePicker"
                          name="dates"
                          [(ngModel)]="item.date"
                          fullWidth
                          class="form-control"
                          required
                          (ngModelChange)="checkInOutUtcDateChanged(item)"
                        />
                        <nb-date-timepicker [format]="dateTimeFormat" twelveHoursFormat #outDateTimePicker></nb-date-timepicker>
                        <sfl-error-msg [control]="date" fieldName="Check Out Time"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-12 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >SiteTimeZone Offset<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ offsetName }}</div>
                      <div *ngIf="isEdit">
                        <ng-select
                          name="productionLoss"
                          [items]="timeZoneList"
                          bindLabel="displayName"
                          bindValue="id"
                          #productionLoss="ngModel"
                          [(ngModel)]="item.siteTimeZoneOffset"
                          notFoundText="No TimeZone Found"
                          placeholder="Select TimeZone"
                          appendTo="body"
                          [clearable]="false"
                          id="input-timezone"
                          disabled
                        >
                        </ng-select>
                        <sfl-error-msg [control]="productionLoss" fieldName="Site TimeZone Offset"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-6 mb-2">
                  <div class="row">
                    <div class="col-12">
                      <label class="label detailView mb-2" for="input-State"
                        >UTC Check-Out Time<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item?.utcTimeStamp | date : 'h:mm a' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          name="utcDateout"
                          [nbDatepicker]="utcDateTimePickerout"
                          [(ngModel)]="item.utcTimeStamp"
                          fullWidth
                          class="form-control"
                          disabled
                        />
                        <nb-date-timepicker [format]="dateTimeFormat" twelveHoursFormat #utcDateTimePickerout></nb-date-timepicker>
                        <sfl-error-msg [control]="date" fieldName="UTC Check-Out Time"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 mb-2">
                  <label class="label detailView mb-2" for="input-city"
                    >QE User/ Contractor User<span class="ms-1 text-danger">*</span></label
                  >
                  <div *ngFor="let users of item.reasonArrays; let j = index">
                    <nb-checkbox disabled [checked]="users.checked" name="user{{ j }}" [(ngModel)]="users.checked">{{
                      users.name
                    }}</nb-checkbox>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-6 mb-8">
                  <div class="row">
                    <div class="col-12 col-sm-12 col-md-6">
                      <label class="label detailView mb-2" for="input-logitude">Longitude<span class="ms-1 text-danger">*</span></label>
                      <div class="mb-2" *ngIf="isDetail">{{ item?.longitude || '-' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          #longitudes
                          name="longitudes"
                          fullWidth
                          [(ngModel)]="item.longitude"
                          class="form-control"
                          required
                          [maxlength]="15"
                          (change)="updateMap(1)"
                        />
                        <sfl-error-msg [control]="longitudes" fieldName="Longitude"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-12 col-md-6">
                      <label class="label detailView mb-2" for="input-State">Latitude<span class="ms-1 text-danger">*</span></label>
                      <div class="mb-2" *ngIf="isDetail">{{ item?.latitude || '-' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          #latitudes
                          name="latitudes"
                          [(ngModel)]="item.latitude"
                          fullWidth
                          class="form-control"
                          required
                          [maxlength]="15"
                          (change)="updateMap(1)"
                        />
                        <sfl-error-msg [control]="latitudes" fieldName="Latitude"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-6 mb-3">
                  <label class="label mb-2" for="input-State">GPS Location<span class="ms-1 text-danger">*</span></label>
                  <div *ngIf="isEdit || isDetail">
                    <ng-container
                      [ngTemplateOutlet]="mapToReload !== index ? gMap : showLoader"
                      [ngTemplateOutletContext]="{ mapNumber: 1, item: item }"
                    ></ng-container>
                  </div>
                </div>
                <div class="col-12 text-end mt-3">
                  <button
                    *ngIf="isEdit"
                    nbButton
                    status="primary"
                    size="medium"
                    type="button"
                    id="siteSubmit"
                    class="checkinbtn ms-auto"
                    (click)="submitCheckoutForm(item)"
                    [disabled]="siteCheckOutForm.invalid"
                  >
                    Save
                  </button>
                </div>
              </div>
            </form>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </ng-template>

    <ng-template #reOpenTemplate let-item="item" let-index="index">
      <nb-accordion class="mb-3">
        <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head">
            Reopen-Form (Check-In Time: {{ item?.date | date : 'M/d/yyyy, h:mm a' }})
          </nb-accordion-item-header>
          <nb-accordion-item-body>
            <form name="siteCheckInForm" #siteCheckInForm="ngForm">
              <div class="row">
                <div class="col-12 col-sm-12 col-md-6 mb-2">
                  <div class="row">
                    <div class="col-12 col-sm-4 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >Site Check-In Time<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item.date | date : 'h:mm a' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          [nbDatepicker]="ReopenDateTimePicker"
                          name="siteCheckInDate"
                          [(ngModel)]="item.date"
                          fullWidth
                          class="form-control"
                          required
                        />
                        <nb-date-timepicker [format]="dateTimeFormat" twelveHoursFormat #ReopenDateTimePicker></nb-date-timepicker>
                        <sfl-error-msg [control]="date" fieldName="Site Check-In Time"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-8 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >SiteTimeZone Offset<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ offsetName }}</div>
                      <div *ngIf="isEdit">
                        <ng-select
                          name="productionLoss"
                          [items]="timeZoneList"
                          bindLabel="displayName"
                          bindValue="id"
                          #productionLoss="ngModel"
                          [(ngModel)]="item.siteTimeZoneOffset"
                          notFoundText="No TimeZone Found"
                          placeholder="Select TimeZone"
                          appendTo="body"
                          [clearable]="false"
                          id="input-timezone"
                          disabled
                        >
                        </ng-select>
                        <sfl-error-msg [control]="productionLoss" fieldName="Site TimeZone Offset"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-12 col-sm-12 col-md-6 mb-2">
                  <div class="row">
                    <div class="col-12 col-sm-8 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >UTC Check-In Time<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item?.utcTimeStamp | date : 'h:mm a' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          name="utcDate"
                          [nbDatepicker]="utcDateTimePicker"
                          fullWidth
                          [(ngModel)]="item.utcTimeStamp"
                          class="form-control"
                          disabled
                        />
                        <nb-date-timepicker [format]="dateTimeFormat" twelveHoursFormat #utcDateTimePicker></nb-date-timepicker>
                        <sfl-error-msg [control]="date" fieldName="UTC Check-In Time"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-4 col-md-6">
                      <label class="label detailView mb-2" for="input-State"
                        >Drive time to site - one way<span class="ms-1 text-danger">*</span></label
                      >
                      <div class="mb-2" *ngIf="isDetail">{{ item?.driveTime || '-' }} Hours</div>
                      <div *ngIf="isEdit">
                        <input #driveTime name="driveTime" nbInput fullWidth [(ngModel)]="item.driveTime" class="form-control" required />
                        <sfl-error-msg [control]="driveTime" fieldName="Drive Time"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-12 col-sm-6 mb-2">
                  <div class="form-control-group">
                    <label class="label detailView mb-2" for="input-latitude"
                      >Reason For Visit<span class="ms-1 text-danger">*</span></label
                    >
                    <div *ngFor="let items of item.reasonArrays; let j = index">
                      <nb-checkbox disabled name="item{{ j }}" [checked]="items.checked" [(ngModel)]="items.checked">
                        {{ items.name }}
                      </nb-checkbox>
                      <div *ngIf="isDetail">{{ items.val }}</div>
                      <div *ngIf="items.checked && items.name === 'Other' && isEdit">
                        <textarea
                          #reason
                          name="reason"
                          nbInput
                          [(ngModel)]="items.val"
                          class="textareaheight mt-1"
                          fullWidth
                          [required]="items.checked && items.name === 'Other'"
                        >
                        </textarea>
                        <sfl-error-msg [control]="reason" fieldName="Reason"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-6 mb-2">
                  <div class="form-control-group h-100">
                    <label class="label detailView mb-2" for="input-logitude">Notes</label>
                    <div class="mb-2" *ngIf="isDetail">{{ item?.notes || '-' }}</div>
                    <div *ngIf="isEdit" class="h-85">
                      <textarea #notes name="notes" nbInput [(ngModel)]="item.notes" class="textareaheight h-100" fullWidth> </textarea>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-6 mb-2">
                  <div class="row">
                    <div class="col-12 col-sm-6 col-md-6">
                      <label class="label detailView mb-2" for="input-city">Longitude <span class="ms-1 text-danger">*</span></label>
                      <div class="mb-2" *ngIf="isDetail">{{ item?.longitude || '-' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          #longitude
                          name="longitude"
                          fullWidth
                          [(ngModel)]="item.longitude"
                          class="form-control"
                          required
                          [maxlength]="15"
                          (change)="updateMap(2)"
                        />
                        <sfl-error-msg [control]="longitude" fieldName="Longitude"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-6">
                      <label class="label detailView mb-2" for="input-latitude">Latitude <span class="ms-1 text-danger">*</span></label>
                      <div class="mb-2" *ngIf="isDetail">{{ item?.latitude || '-' }}</div>
                      <div *ngIf="isEdit">
                        <input
                          nbInput
                          #latitude
                          name="latitude"
                          fullWidth
                          [(ngModel)]="item.latitude"
                          class="form-control"
                          required
                          [maxlength]="15"
                          (change)="updateMap(2)"
                        />
                        <sfl-error-msg [control]="latitude" fieldName="Latitude"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-6">
                  <label class="label detailView mb-2" for="input-State">GPS Location<span class="ms-1 text-danger">*</span></label>
                  <div *ngIf="isEdit || isDetail">
                    <ng-container
                      [ngTemplateOutlet]="mapToReload !== index ? gMap : showLoader"
                      [ngTemplateOutletContext]="{ mapNumber: 2, item: item, mapIndex: index }"
                    ></ng-container>
                  </div>
                </div>
                <div class="col-12 text-end mt-3">
                  <button
                    *ngIf="isEdit"
                    nbButton
                    status="primary"
                    size="medium"
                    type="button"
                    id="siteSubmit"
                    class="checkinbtn ms-auto"
                    (click)="submitCheckInForm(item)"
                    [disabled]="siteCheckInForm.invalid"
                  >
                    Save
                  </button>
                </div>
              </div>
            </form>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </ng-template>
    <ng-template #gMap let-item="item" let-mapIndex="mapIndex" let-mapNumber="mapNumber">
      <div class="map-row position-relative">
        <p-gmap
          *ngIf="item?.mapObj"
          [options]="item?.mapObj"
          [overlays]="item?.overlays"
          [style]="{ width: '100%', height: '250px' }"
        ></p-gmap>
      </div>
    </ng-template>
    <ng-template #showLoader let-mapNumber="mapNumber" let-mapIndex="mapIndex">
      <div
        *ngIf="mapNumber === mapToReload"
        class="map-loading"
        [nbSpinner]="!showMap"
        nbSpinnerStatus="primary"
        nbSpinnerSize="large"
      ></div>
    </ng-template>
  </nb-card-body>
</nb-card>
