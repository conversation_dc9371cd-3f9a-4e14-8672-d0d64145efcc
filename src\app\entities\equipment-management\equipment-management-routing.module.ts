import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { EquipmentAddEditComponent } from './equipment-add-edit/equipment-add-edit.component';
import { EquipmentListingComponent } from './equipment-listing/equipment-listing.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: EquipmentListingComponent,
    data: { pageTitle: 'Equipments' }
  },
  {
    path: 'add',
    component: EquipmentAddEditComponent,
    canActivate: [PermissionGuard],
    data: { permittedRoles: [ROLE_TYPE.ADMIN], pageTitle: 'Add Equipment' }
  },
  {
    path: 'edit/:id',
    component: EquipmentAddEditComponent,
    canActivate: [PermissionGuard],
    data: { permittedRoles: [ROLE_TYPE.ADMIN], pageTitle: 'Update Equipment' }
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EquipmentManagementRoutingModule {}
