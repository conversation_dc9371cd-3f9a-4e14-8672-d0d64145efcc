import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { QE_MENU_MODULE_ENUM } from '../../@shared/enums/qe-menu.enum';
import { QEAnalyticsGatheringGuard } from '../qe-analytics/services/qe-analytics-gathering.guard';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: 'settings',
    loadChildren: () => import('./settings/settings.module').then(m => m.SettingsModule),
    canActivate: [PermissionGuard],
    data: { permittedRoles: [ROLE_TYPE.ADMIN] }
  },
  {
    path: 'jha',
    loadChildren: () => import('./jha/jha.module').then(m => m.JhaModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_JHA,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SAFETY
    }
  },
  {
    path: 'site-checkin',
    loadChildren: () => import('./site-checkin/site-checkin.module').then(m => m.SiteCheckInModule),
    canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SAFETY
    }
  },
  {
    path: 'site-audit-jha',
    loadChildren: () => import('./site-audit-jha/site-audit-jha.module').then(m => m.SiteAuditJHAModule),
    canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SAFETY
    }
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SafetyRoutingModule {}
