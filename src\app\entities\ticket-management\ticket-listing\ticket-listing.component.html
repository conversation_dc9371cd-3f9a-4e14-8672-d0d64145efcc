<nb-card class="ticketSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header [class.border-bottom-0]="!showFilter">
    <div class="d-flex align-items-center flex-wrap flex-md-nowrap">
      <div class="d-flex justify-content-between custom-title-tickets">
        <h6>All Tickets</h6>
        <div class="mobile-view-buttons">
          <ng-container *ngTemplateOutlet="buttons"></ng-container>
        </div>
      </div>
      <div class="ms-sm-auto d-flex align-items-center custom-div-width">
        <ng-select
          name="ColumFilter"
          class="w-180"
          [items]="ticketColumnFilter"
          [(ngModel)]="filterModel.searchBy"
          bindLabel="name"
          bindValue="id"
          notFoundText="No Column Found"
          placeholder="Select Column"
          appendTo="body"
          [clearable]="false"
        >
        </ng-select>
        <input
          class="form-control search-textbox ms-2 w-180"
          placeholder="Search"
          type="text"
          size="small"
          name="search"
          autocomplete="off"
          [(ngModel)]="filterModel.searchValue"
          id="listSearch"
        />
        <div class="tablet-view-buttons">
          <ng-container *ngTemplateOutlet="buttons"></ng-container>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 ticketFilter appFilter mb-3" *ngIf="showFilter">
        <sfl-filter
          *ngIf="showCommonFilter"
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (refreshTableHeight)="this.isFilterDisplay = $event"
          (clearParentList)="tickets = []"
          [searchValue]="filterModel?.searchValue"
          [searchBy]="filterModel?.searchBy"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Ticket List">
          <thead>
            <tr>
              <th scope="col" (click)="sort('Priority', sortOptionList['Priority'])">
                <div class="d-flex justify-content-center align-items-center">
                  Priority
                  <span
                    class="fa cursor-pointer ms-2"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Priority'] === 'desc',
                      'fa-arrow-down': sortOptionList['Priority'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Priority'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('TicketType', sortOptionList['TicketType'])">
                <div class="d-flex justify-content-center align-items-center">
                  <span class="me-2">Type</span>
                  <span
                    class="fa cursor-pointer ms-2"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['TicketType'] === 'desc',
                      'fa-arrow-down': sortOptionList['TicketType'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'TicketType'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('Number', sortOptionList['Number'])">
                <div class="d-flex align-items-center">
                  Number
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Number'] === 'desc',
                      'fa-arrow-down': sortOptionList['Number'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Number'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('CustomerPortfolio', sortOptionList['CustomerPortfolio'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Customer (Portfolio)</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['CustomerPortfolio'] === 'desc',
                      'fa-arrow-down': sortOptionList['CustomerPortfolio'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'CustomerPortfolio'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('Site', sortOptionList['Site'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Site</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Site'] === 'desc',
                      'fa-arrow-down': sortOptionList['Site'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Site'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('Device', sortOptionList['Device'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Device</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Device'] === 'desc',
                      'fa-arrow-down': sortOptionList['Device'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Device'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="mw-150">Issue</th>
              <th scope="col" class="mw-150">Recent Activity</th>
              <th (click)="sort('Open', sortOptionList['Open'])" id="date">
                <div class="d-flex align-items-center">
                  <span class="me-2">Opened</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Open'] === 'desc',
                      'fa-arrow-down': sortOptionList['Open'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Open'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('Close', sortOptionList['Close'])" id="date">
                <div class="d-flex align-items-center">
                  <span class="me-2">Closed</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Close'] === 'desc',
                      'fa-arrow-down': sortOptionList['Close'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Close'
                    }"
                  ></span>
                </div>
              </th>
              <ng-container *ngIf="userRole[0] !== 'customer'">
                <th (click)="sort('RegionName', sortOptionList['RegionName'])" id="date">
                  <div class="d-flex align-items-center">
                    <span class="me-2">Region</span>
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['RegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['RegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'RegionName'
                      }"
                    ></span>
                  </div>
                </th>
                <th (click)="sort('SubRegionName', sortOptionList['SubRegionName'])" id="date">
                  <div class="d-flex align-items-center">
                    <span class="me-2">Subregion</span>
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['SubRegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['SubRegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'SubRegionName'
                      }"
                    ></span>
                  </div>
                </th>
              </ng-container>
              <th class="text-end" scope="col">Truck Roll Count</th>
              <th scope="col">Status</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngIf="tickets?.length">
              <tr
                *ngFor="
                  let item of tickets
                    | paginate : { id: 'ticketListing', itemsPerPage: filterModel.itemsCount, currentPage: currentPage, totalItems: total }
                "
              >
                <td data-title="Priority" class="text-center">
                  <img
                    *ngIf="item?.priorityStr === 'High'"
                    nbTooltip="High"
                    nbTooltipStatus="primary"
                    src="assets/images/High.svg"
                    alt="High"
                  />
                  <img
                    alt="Medium"
                    *ngIf="item?.priorityStr === 'Medium'"
                    nbTooltip="Medium"
                    nbTooltipStatus="primary"
                    src="assets/images/Medium.svg"
                  />
                  <img
                    alt="Low"
                    *ngIf="item?.priorityStr === 'Low'"
                    nbTooltip="Low"
                    nbTooltipStatus="primary"
                    src="assets/images/Low.svg"
                  />
                  <img
                    alt="Safety"
                    *ngIf="item?.priorityStr === 'Safety'"
                    nbTooltip="Safety"
                    nbTooltipStatus="primary"
                    src="assets/images/Safety.svg"
                  />
                </td>
                <td data-title="Type" class="text-center ticket-type-icons">
                  <ng-container [ngSwitch]="true">
                    <ng-container *ngSwitchCase="item?.ticketTypeId === 1 || item?.ticketTypeName === 'CM'">
                      <img
                        src="assets/images/Ticket-Type-CM-Icon.svg"
                        alt="CM Icon"
                        class="ticket-type-icon-img"
                        nbTooltip="CM"
                        nbTooltipStatus="primary"
                      />
                      <!-- to be used -->
                      <!-- <em class="fa-solid fa-wrench fa-rotate-270 m-1 ticket-type-icon-text" style="color: #bdc1c9" nbTooltip="CM" nbTooltipStatus="primary"></em> -->
                    </ng-container>
                    <ng-container *ngSwitchCase="item?.ticketTypeId === 2 || item?.ticketTypeName === 'Snow'">
                      <img
                        src="assets/images/Ticket-Type-Snow-Icon.svg"
                        alt="Snow Icon"
                        class="ticket-type-icon-img"
                        nbTooltip="Snow"
                        nbTooltipStatus="primary"
                      />
                      <!-- to be used -->
                      <!-- <em class="fa-solid fa-snowflake ticket-type-icon-text" style="color: #5697e1" nbTooltip="Snow" nbTooltipStatus="primary"></em> -->
                    </ng-container>
                    <ng-container *ngSwitchCase="item?.ticketTypeId === 3 || item?.ticketTypeName === 'Vegetation'">
                      <img
                        src="assets/images/Ticket-Type-Vegetation-Icon.svg"
                        alt="Vegetation Icon"
                        class="ticket-type-icon-img"
                        nbTooltip="Vegetation"
                        nbTooltipStatus="primary"
                      />
                      <!-- to be used -->
                      <!-- <em class="fa-solid fa-leaf ticket-type-icon-text" style="color: #68d466" nbTooltip="Vegetation" nbTooltipStatus="primary"></em> -->
                    </ng-container>
                    <ng-container *ngSwitchCase="item?.ticketTypeId === 4 || item?.ticketTypeName === 'Non-conformance'">
                      <em
                        class="fa-solid fa-flag ticket-type-icon-text"
                        style="color: #7856da"
                        nbTooltip="Non-conformance"
                        nbTooltipStatus="primary"
                      ></em>
                    </ng-container>
                    <ng-container *ngSwitchCase="item?.ticketTypeId === 5 || item?.ticketTypeName === 'Project'">
                      <img
                        src="assets/images/Ticket-Type-Project-Icon.svg"
                        alt="Project Icon"
                        class="ticket-type-icon-img"
                        nbTooltip="Project"
                        nbTooltipStatus="primary"
                      />
                    </ng-container>
                    <ng-container *ngSwitchCase="item?.ticketTypeId === 6 || item?.ticketTypeName === 'Monitoring (DG)'">
                      <img
                        src="assets/images/Ticket-Type-Monitoring(DG)-Icon.svg"
                        alt="Monitoring (DG) Icon"
                        class="ticket-type-icon-img"
                        nbTooltip="Monitoring (DG)"
                        nbTooltipStatus="primary"
                      />
                    </ng-container>
                  </ng-container>
                </td>
                <td data-title="Number" class="pointerTicketNumberLink">
                  <a
                    [href]="'../entities/ticket/detail/view/' + item.ticketNumber"
                    [contextMenu]="actionMenu"
                    [contextMenuValue]="{ data: item.ticketNumber }"
                  >
                    {{ item?.ticketNumber }}
                  </a>
                </td>
                <td data-title="Customer (Portfolio)">{{ item?.customerPortfolio }}</td>
                <td data-title="Site">{{ item?.siteName }}</td>
                <td data-title="Device">
                  <div>
                    <ng-container
                      *ngFor="let device of item.ticketDevices | slice : 0 : (item.show ? item.ticketDevices?.length : 5); last as last"
                    >
                      <span
                        [ngStyle]="{
                          color: device?.alertStatus === 'Ticketed' ? '#f0c108' : device?.alertStatus === 'Resolved' ? '#61c98b' : ''
                        }"
                        >{{ device?.deviceName }}</span
                      >{{ last ? '' : ', ' }}
                    </ng-container>
                    <a *ngIf="item.ticketDevices?.length > 5" class="text-primary link cursor-pointer" (click)="item.show = !item.show">
                      {{ item.show ? '  ' + ' ...- ' : '  ' + ' ...+ ' }}</a
                    >
                  </div>
                </td>
                <td data-title="Issue">
                  <div *ngIf="item?.issue" nbTooltip="{{ item?.issue }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                    <sfl-read-more [content]="item?.issue"></sfl-read-more>
                  </div>
                </td>
                <td data-title="Recent Activity">
                  <span
                    *ngIf="item?.activityLog"
                    nbTooltip="{{ item?.activityDate | date : fullDateFormat }} : {{ item?.activityLog }}"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                  >
                    <sfl-read-more [content]="(item?.activityDate | date : fullDateFormat + ' : ') + item?.activityLog"></sfl-read-more>
                  </span>
                </td>
                <td data-title="Opened">
                  {{ item?.open | date : fullDateFormat }}
                </td>
                <td data-title="Closed">
                  {{ item?.close | date : fullDateFormat }}
                </td>
                <ng-container *ngIf="userRole[0] !== 'customer'">
                  <td data-title="Region">{{ item?.regionName }}</td>
                  <td data-title="Subregion">{{ item?.subRegionName }}</td>
                </ng-container>
                <td data-title="Truck Roll Count" class="text-end">
                  <a
                    [ngClass]="{ 'custom-link': item?.truckRollNumbers?.length }"
                    (click)="item?.truckRollNumbers?.length && openTruckRollModal(item, truckRollModal)"
                  >
                    <span>{{ item?.truckRoll }} </span>
                  </a>
                </td>
                <td
                  data-title="Status"
                  [ngStyle]="item.isResolve === true ? { 'background-color': 'green' } : { 'background-color': '' }"
                  class="text-center"
                >
                  {{ item?.statusStr }}
                </td>
              </tr>
            </ng-container>
            <ng-container *ngIf="!tickets?.length">
              <tr *ngIf="!tickets.length">
                <td colspan="13" class="no-record text-center">No Data Found</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="tickets?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            [(ngModel)]="pageSize"
            id="ticketListing"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSize()"
            appendTo="body"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls id="ticketListing" (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
<context-menu #actionMenu>
  <ng-template contextMenuItem (execute)="openLink($event.value.data, false)"> Open link in new tab </ng-template>
  <ng-template contextMenuItem (execute)="openLink($event.value.data, true)"> Open link in new window </ng-template>
</context-menu>

<ng-template #buttons>
  <button
    *appHasPermission="[roleType.ADMIN, roleType.MANAGER]"
    nbButton
    [nbContextMenu]="items"
    status="primary"
    size="small"
    class="linear-mode-button ms-2 button-h-100"
  >
    <em class="fa-solid fa-bars"></em>
  </button>
  <button
    *ngIf="filterModel.searchValue"
    class="linear-mode-button ms-2 button-h-100"
    nbButton
    status="primary"
    size="small"
    type="button"
    (click)="clearSearch()"
    [disabled]="loading"
  >
    <span class="d-none d-lg-inline-block">Clear Search</span>
    <i class="d-inline-block d-lg-none fa fa-times-circle"></i>
  </button>
  <button
    class="linear-mode-button ms-2 button-h-100"
    nbButton
    status="primary"
    size="small"
    type="button"
    (click)="exportData()"
    [disabled]="loading"
  >
    <span class="d-none d-lg-inline-block">Export</span>
    <i class="d-inline-block d-lg-none fa fa-file-export"></i>
  </button>
  <button
    class="linear-mode-button ms-2 button-h-100"
    [routerLink]="['../add/']"
    nbButton
    status="primary"
    size="small"
    [disabled]="loading"
    routerLink="add"
    type="button"
    *ngIf="user[0] !== 'customer'"
  >
    <span class="d-none d-lg-inline-block">Add Ticket</span>
    <i class="d-inline-block d-lg-none fa-solid fa-plus"></i>
  </button>
  <button
    class="mobile-view-buttons ms-2"
    (click)="showFilter = !showFilter"
    nbButton
    status="primary"
    size="small"
    [disabled]="loading"
    type="button"
  >
    <i class="fa-solid fa-filter-circle-xmark" *ngIf="showFilter"></i>
    <i class="fa-solid fa-filter" *ngIf="!showFilter"></i>
  </button>
</ng-template>

<ng-template #truckRollModal>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Truck Roll Numbers</h4>
    <button type="button" class="btn-close close pull-right" aria-label="Close" (click)="modalRef?.hide()">
      <span class="d-inline-block scale-2">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="container">
      <ul class="truckroll-ul">
        <li class="truckroll-li" *ngFor="let truckNumber of selectedTicket?.truckRollNumbers">
          <span (click)="redirectToTruckRollScreen(truckNumber)"> {{ truckNumber }}</span>
        </li>
      </ul>
    </div>
  </div>
</ng-template>
