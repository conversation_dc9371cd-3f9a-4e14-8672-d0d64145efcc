import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModeWiseGuard } from '../../@shared/services/mode-wise.guard';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { CustomerAddEditComponent } from './customer-add-edit/customer-add-edit.component';
import { CustomerListingComponent } from './customer-listing/customer-listing.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: CustomerListingComponent,
    data: { pageTitle: 'Customers' }
  },
  {
    path: 'add',
    component: CustomerAddEditComponent,
    canActivate: [PermissionGuard],
    data: { permittedRoles: [ROLE_TYPE.ADMIN], pageTitle: 'Add Customer' }
  },
  {
    path: ':mode/:id',
    component: CustomerAddEditComponent,
    canActivate: [ModeWiseGuard],
    data: {
      edit: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.ANALYST],
      detail: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST, ROLE_TYPE.CUSTOMER],
      view: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST, ROLE_TYPE.CUSTOMER],
      pageTitle: 'Update Customer'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomerManagementRoutingModule {}
