<nb-card class="siteSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Site Check-In</h6>
        <div class="ms-auto">
          <button
            *ngIf="siteCheckInList?.length"
            class="linear-mode-button"
            nbButton
            size="small"
            type="button"
            status="primary"
            (click)="exportData()"
            [disabled]="loading"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div class="col-12 siteFilter appFilter">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (clearParentList)="siteCheckInList = []"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive mt-3 table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Site List">
          <thead>
            <tr>
              <th class="Customer" (click)="sort('date', sortOptionList['date'])" id="Date">
                <div class="d-flex align-items-center">
                  UTC Date
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['date'] === 'desc',
                      'fa-arrow-down': sortOptionList['date'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'date'
                    }"
                  ></span>
                </div>
              </th>
              <th class="Portfolio" (click)="sort('userName', sortOptionList['userName'])" id="User">
                <div class="d-flex align-items-center">
                  User
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['userName'] === 'desc',
                      'fa-arrow-down': sortOptionList['userName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'userName'
                    }"
                  ></span>
                </div>
              </th>
              <th class="Customer" (click)="sort('siteCustomerPortfolios', sortOptionList['siteCustomerPortfolios'])" id="Customer">
                <div class="d-flex align-items-center">
                  Customer (Portfolio)
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['siteCustomerPortfolios'] === 'desc',
                      'fa-arrow-down': sortOptionList['siteCustomerPortfolios'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'siteCustomerPortfolios'
                    }"
                  ></span>
                </div>
              </th>
              <th class="Site" (click)="sort('Site', sortOptionList['Site'])" id="Site">
                <div class="d-flex align-items-center">
                  Site
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Site'] === 'desc',
                      'fa-arrow-down': sortOptionList['Site'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Site'
                    }"
                  ></span>
                </div>
              </th>
              <th class="date" (click)="sort('totalDriveTime', sortOptionList['totalDriveTime'])" id="totaldriveTime">
                <div class="d-flex align-items-center">
                  Total Drive Hours
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['totalDriveTime'] === 'desc',
                      'fa-arrow-down': sortOptionList['totalDriveTime'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'totalDriveTime'
                    }"
                  ></span>
                </div>
              </th>
              <th class="date" (click)="sort('checkInTime', sortOptionList['checkInTime'])" id="checkintime">
                <div class="d-flex align-items-center">
                  UTC Check-In Time
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['checkInTime'] === 'desc',
                      'fa-arrow-down': sortOptionList['checkInTime'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'checkInTime'
                    }"
                  ></span>
                </div>
              </th>
              <th class="date" (click)="sort('checkOutTime', sortOptionList['checkOutTime'])" id="checkouttime">
                <div class="d-flex align-items-center">
                  UTC Check-Out Time
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['checkOutTime'] === 'desc',
                      'fa-arrow-down': sortOptionList['checkOutTime'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'checkOutTime'
                    }"
                  ></span>
                </div>
              </th>
              <th class="date" (click)="sort('siteTimeZoneOffset', sortOptionList['siteTimeZoneOffset'])" id="siteTimeZoneOffset">
                <div class="d-flex align-items-center">
                  OffSet
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['siteTimeZoneOffset'] === 'desc',
                      'fa-arrow-down': sortOptionList['siteTimeZoneOffset'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'siteTimeZoneOffset'
                    }"
                  ></span>
                </div>
              </th>
              <th class="text-center action" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let data of siteCheckInList
                  | paginate
                    : {
                        itemsPerPage: filterModel.itemsCount,
                        currentPage: currentPage,
                        totalItems: total
                      };
                let i = index
              "
            >
              <td data-title="UTC Date">{{ data?.date | date : fullDateFormat }}</td>
              <td data-title="User" class="td-custom-width">{{ data?.userName }}</td>
              <td data-title="Customer (Portfolio)" class="td-custom-width">{{ data?.customerPortfolio }}</td>
              <td data-title="Site" class="td-custom-width">{{ data?.site }}</td>
              <td data-title="Total Drive Hours" class="td-custom-width text-end">{{ data?.totalDriveTime }}</td>
              <td data-title="UTC Check In Time" class="td-custom-width text-end">{{ data?.checkInTime }}</td>
              <td data-title="UTC Check Out Time" class="td-custom-width text-end">{{ data?.checkOutTime }}</td>
              <td data-title="OffSet" class="td-custom-width text-end">{{ data?.siteTimeZoneOffset }}</td>
              <td data-title="Action" class="text-center">
                <div class="d-md-flex justify-content-center">
                  <a
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER, roleType.ANALYST, roleType.FIELDTECH]"
                    class="px-2 listgrid-icon"
                  >
                    <em
                      class="fa fa-eye"
                      nbTooltip="Click to preview Site CheckIn"
                      [routerLink]="['../detail/view/' + data.id]"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                    ></em>
                  </a>
                  <a
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER, roleType.ANALYST, roleType.FIELDTECH]"
                    class="listgrid-icon px-2"
                    [routerLink]="['../detail/edit/' + data.id]"
                  >
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER, roleType.ANALYST, roleType.FIELDTECH]"
                    class="text-danger px-2 listgrid-icon"
                    (click)="onDelete(data.id)"
                  >
                    <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                  </a>
                </div>
              </td>
            </tr>
            <tr>
              <td data-title="" colspan="12" *ngIf="!siteCheckInList?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="siteCheckInList?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total Records: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
