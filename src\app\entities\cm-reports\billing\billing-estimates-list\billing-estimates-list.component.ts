import { Component, EventEmitter, Input, Output } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AppConstants } from '../../../../@shared/constants';
import { StorageService } from '../../../../@shared/services/storage.service';
import { EditRefetchEmit } from '../../../performance/data-table/data-table-model';
import { billingEstimateList } from '../../cm-reports.model';
import { CmReportsService } from '../../cm-reports.service';
import { ROLE_TYPE } from '../../../../@shared/enums';
import { checkAuthorisations } from '../../../../@shared/utils';

@Component({
  selector: 'sfl-billing-estimates-list',
  templateUrl: './billing-estimates-list.component.html',
  styleUrls: ['./billing-estimates-list.component.scss']
})
export class BillingEstimatesListComponent {
  @Input() billingEstimatesList: billingEstimateList[];
  @Input() estimateTotalRecords: number = 10;
  @Input() estimateCurrentPage = 1;
  @Output() pageChange: EventEmitter<EditRefetchEmit> = new EventEmitter<EditRefetchEmit>();
  @Output() isBulkActionRefresh: EventEmitter<boolean> = new EventEmitter();
  estimatePageSize = AppConstants.rowsPerPage;
  dateFormat = AppConstants.fullDateFormat;
  loading = true;
  isMasterSel = false;
  bulkActionName: number;
  modalRef: BsModalRef;
  userRole = this.storageService.get('user').authorities;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly modalService: BsModalService,
    private readonly cmService: CmReportsService,
    private readonly storageService: StorageService
  ) {}

  // selectDeselectAllEstimates Change
  selectDeselectAllEstimates() {
    this.billingEstimatesList.forEach(element => {
      if (
        !(
          element.estimateStatus === 'Pending Approval' ||
          element.estimateStatus === 'Declined' ||
          (element.estimateStatus === 'Approved' && !element.customerPO)
        )
      ) {
        element.isSelected = this.isMasterSel;
      }
    });
  }

  // selectDeselectEstimates Change
  selectDeselectEstimates() {
    this.isMasterSel = this.billingEstimatesList.every(function (item: any) {
      return item.isSelected === true;
    });
  }

  // applyBulkAction for MULTIPLE STATUS CHANGES
  applyBulkAction() {
    const selectedEstId = [];
    for (const obj of this.billingEstimatesList) {
      if (obj.isSelected) {
        selectedEstId.push(obj.ticketEstId);
      }
    }
    const bulkActionModel = {
      ticketEstIds: selectedEstId,
      estimateStatus: Number(this.bulkActionName)
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `Selected Estimates will be converted to ${
          Number(this.bulkActionName) === 6 ? 'Mark as Billed' : 'Mark as Billing'
        } Status `,
        isWarning: true,
        confirmBtnText: 'Proceed',
        cancelBtnText: 'Cancel'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.cmService.getBulkActionUpdateEstimateList(bulkActionModel).subscribe({
          next: (res: any) => {
            this.isBulkActionRefresh.emit(true);
          },
          error: e => {
            this.loading = false;
          }
        });
      }
    });
  }

  onChangeSize() {
    this.emitChangeData();
  }

  onPageChange(obj) {
    this.estimateCurrentPage = obj;
    this.emitChangeData();
  }

  emitChangeData() {
    const data: EditRefetchEmit = { page: this.estimateCurrentPage, itemsCount: this.estimatePageSize };
    this.pageChange.emit(data);
  }
}
