import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter, FilterList, SiteDevicePageFilterKeys, SiteDevices, SiteDevicesList } from '../site-device.model';
import { SiteDeviceService } from '../site-device.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-site-device-listing',
  templateUrl: './site-device-listing.component.html',
  styleUrls: ['./site-device-listing.component.scss']
})
export class SiteDeviceListingComponent implements OnInit {
  loading = false;
  siteDevices: SiteDevices[] = [];
  subscription: Subscription = new Subscription();
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  total = 10;
  missingDCLoadCount;
  viewPage = FILTER_PAGE_NAME.SITE_INFO_DEVICES_LISTING;
  filterList = FilterList;
  viewFilterSection = 'sitedeviceFilterSection';
  sortOptionList = {
    Site: 'asc',
    DeviceType: 'asc',
    DeviceModel: 'asc',
    Mfg: 'asc',
    Size: 'asc',
    SerialNo: 'asc',
    CustomerPortfolio: 'asc',
    DeviceName: 'asc',
    DeviceID: 'asc'
  };
  modalRef: BsModalRef;
  filterDetails: FilterDetails = new FilterDetails();
  user: string;
  themeColor: string;
  showBackButton = false;

  constructor(
    public readonly siteDeviceService: SiteDeviceService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly location: Location
  ) {}

  ngOnInit() {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection);

    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.user = this.storageService.get('user').authorities;
    this.themeColor = this.storageService.get('theme');
    this.initFilterDetails();
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = 'Customer';
    let hasUrlFilter = false;
    this.route.queryParams.subscribe(params => {
      if (Object.keys(params).length) {
        if (params['customerId'] || params['portfolioId'] || params['siteId']) {
          this.showBackButton = true;
        }

        this.filterModel.customerIds = params['customerId'] ? [Number(params['customerId'])] : [];
        this.filterModel.portfolioIds = params['portfolioId'] ? [Number(params['portfolioId'])] : [];
        this.filterModel.siteIds = params['siteId'] ? [Number(params['siteId'])] : [];
        this.filterModel.isArchive = params['isArchive'] === 'true';
        this.filterModel.isActive = params['isArchive'] === 'false' ? 'Active' : '';
        this.filterModel.automationPartnerIds = params['automationPartnerId'] ? [Number(params['automationPartnerId'])] : [];
        this.filterModel.automationSiteIds = params['automationSiteId'] ? [Number(params['automationSiteId'])] : [];
        if (
          this.filterModel.portfolioIds.length ||
          this.filterModel.customerIds.length ||
          this.filterModel.siteIds.length ||
          this.filterModel.automationPartnerIds.length ||
          this.filterModel.automationSiteIds.length
        ) {
          hasUrlFilter = true;
        }
      }
    });
    this.isFilterDisplay = filterSection ? filterSection : false;

    if (this.filterModel.direction && this.filterModel.sortBy) {
      this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
    }
    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);

    if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, SiteDevicePageFilterKeys)) {
      this.getAllSiteDeviceList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    this.filterDetails.placeholder = 'Search Device/Model/Mfg';
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.DEVICE_TYPE.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.SITE.multi = true;
    filterItem.DEVICE_TYPE.multi = true;
    filterItem.DEVICE.show = true;
    filterItem.MFG.show = true;
    filterItem.MODEL.show = true;
    filterItem.AUTOMATION_DATA_SOURCE.show = true;
    filterItem.AUTOMATION_SITE.show = true;
    filterItem.SHOW_STATUS.show = true;
    filterItem.SHOW_ARCHIVED.show = true;
    this.filterDetails.default_sort = 'DeviceName';
    this.filterDetails.filter_item = filterItem;
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllSiteDeviceList(true, filterParams);
  }

  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
  }

  gotoSite(id) {
    this.router.navigate(['entities/sites/view/' + id]);
  }

  getAllSiteDeviceList(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    const filters = JSON.parse(JSON.stringify(filterParams || this.filterModel));

    if (filterParams) {
      this.filterModel = JSON.parse(JSON.stringify(filterParams));
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }

    if (filters?.deviceModels?.length) {
      filters.deviceModels = filters.deviceModels.map(element => {
        const lastBracketIndex = element.lastIndexOf(' [');

        if (lastBracketIndex !== -1) {
          return element.substring(0, lastBracketIndex);
        } else {
          return element;
        }
      });
    }

    this.siteDeviceService.getAllSiteDeviceList(filters).subscribe({
      next: (res: SiteDevicesList) => {
        this.bindSiteDevices(res);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  bindSiteDevices(res: SiteDevicesList) {
    this.loading = false;
    this.siteDevices = res && res.siteDevices ? res.siteDevices : [];
    this.total = res && res.totalSiteDevices ? res.totalSiteDevices : 0;
    this.missingDCLoadCount = res.invDCNullCount + res.meterDCNullCount;
  }

  // Pagesize Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllSiteDeviceList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllSiteDeviceList();
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllSiteDeviceList();
  }

  exportData() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    let rows: any;
    this.subscription.add(
      this.siteDeviceService.getAllSiteDeviceList(filterModel).subscribe({
        next: (data: SiteDevicesList) => {
          const tittle = 'Site_Devices';
          if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
            rows = [
              [
                'Customer',
                'Portfolio',
                'Site',
                'API ID',
                'Device Name',
                'Type',
                'Model',
                'Size',
                'Mfg',
                'DC Load',
                'Order',
                'Serial Number',
                'Active'
              ]
            ];
            for (const i of data.siteDevices) {
              const tempData = [
                i.customer,
                i.portfolio,
                i.site,
                i.deviceId,
                i.deviceLabel,
                i.deviceType,
                i.deviceModel ? i.deviceModel : null,
                i.size,
                i.mfg,
                i.dcLoad,
                i.rank,
                i.serialNo,
                !i.IsDeleted ? 'yes' : 'no'
              ];
              rows.push(tempData);
            }
          } else {
            rows = [
              [
                'Customer',
                'Portfolio',
                'Site',
                'Device Name',
                'Type',
                'Model',
                'Size',
                'Mfg',
                'DC Load',
                'Order',
                'Serial Number',
                'Active'
              ]
            ];
            for (const i of data.siteDevices) {
              const tempData = [
                i.customer,
                i.portfolio,
                i.site,
                i.deviceLabel,
                i.deviceType,
                i.deviceModel ? i.deviceModel : null,
                i.size,
                i.mfg,
                i.dcLoad,
                i.rank,
                i.serialNo,
                !i.IsDeleted ? 'yes' : 'no'
              ];
              rows.push(tempData);
            }
          }

          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  toggleFilter() {
    this.isFilterDisplay = !this.isFilterDisplay;
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
  }

  goBack() {
    this.location.back();
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
    this.subscription.unsubscribe();
  }
}
