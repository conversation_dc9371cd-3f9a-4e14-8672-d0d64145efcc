import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AddEditContractComponent } from './add-edit-contract/add-edit-contract.component';
import { ContractsComponent } from './contracts.component';
import { ROLE_TYPE } from '../../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: ContractsComponent,
    data: { permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER], pageTitle: 'Contracts' }
  },
  {
    path: 'add',
    component: AddEditContractComponent,
    data: { permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER], pageTitle: 'Add Contracts' }
  },
  {
    path: 'update/:id',
    component: AddEditContractComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER],
      pageTitle: 'Update Contracts'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ContractsRoutingModule {}
