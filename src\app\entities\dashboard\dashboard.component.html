<nb-card class="dashboardSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex align-items-center">
      <h6>{{ dashboardViewType === 'scheduleView' ? 'PM Schedule Dashboard' : 'PM Compliance Dashboard' }}</h6>
      <div class="ms-auto d-flex align-items-center">
        <button
          nbButton
          (click)="onExport()"
          *ngIf="dashboardViewType !== 'scheduleView'"
          status="primary"
          size="small"
          id="assessmentSubmit"
          class="float-end m-1"
        >
          <span class="d-none d-lg-inline-block">Export</span>
          <i class="d-inline-block d-lg-none fa fa-file-export"></i>
        </button>
        <button
          *ngIf="dashboardViewType === 'scheduleView'"
          class="linear-mode-button"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="exportScheduleData()"
          [disabled]="loading"
        >
          <span class="d-none d-lg-inline-block">Export</span>
          <i class="d-inline-block d-lg-none fa fa-file-export"></i>
        </button>
        <div
          *ngIf="dashboardViewType === 'scheduleView' && checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER])"
          ngFileDragDrop
          (fileDropped)="importData($event)"
        >
          <input type="file" #file class="d-none" accept=".csv" (change)="importData($event.target.files)" />
          <button
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="small"
            type="button"
            (click)="file.click()"
            [disabled]="loading"
          >
            <span class="d-none d-lg-inline-block">Import</span>
            <i class="d-inline-block d-lg-none fa fa-file-import"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body id="page-top" class="dropdownOverlap">
    <div id="wrapper">
      <div id="content-wrapper" class="d-flex flex-column">
        <div id="content">
          <div class="container-fluid px-0">
            <div class="row mb-3 chart-list">
              <div class="col-12" *ngIf="doughnutUtilityChartData.length || doughnutDGChartData.length">
                <nb-accordion>
                  <nb-accordion-item class="border-bottom mb-2">
                    <nb-accordion-item-header class="accordion_head">Charts</nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div class="row">
                        <!-- DG Card -->
                        <div class="col-12 mb-3">
                          <div class="card shadow">
                            <div class="card-header fw-bold text-white">DG</div>
                            <div class="card-body">
                              <div class="row dashboard_stats mt-0">
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-success shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-success text-uppercase content_text">Report Completed</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgCompleted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-info shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-info text-uppercase content_text">Field Work Completed</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgOnSite }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-primary shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-primary text-uppercase content_text">
                                          Field Work Partially Completed
                                        </div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgPartially }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-dark shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-dark text-uppercase content_text">Pending</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgNotyet }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-started shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart_started text-uppercase content_text">REPORT STARTED</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgReportStarted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-drafted shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart_drafted text-uppercase content_text">REPORT DRAFTED</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgReportDrafted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-CannotComplete shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart_CannotComplete text-uppercase content_text">CANNOT COMPLETE</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgCannotCompleted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-not-created shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart-not-created text-uppercase content_text">NOT CREATED</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.dgNotGenerated }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="chart-item-section row justify-content-center flex-wrap">
                                <ng-container *ngFor="let chartConfig of doughnutDGChartData">
                                  <ng-container *ngIf="chartConfig.isCreateChart; else noDataMessage">
                                    <div echarts [options]="chartConfig" class="chart-height"></div>
                                  </ng-container>
                                  <ng-template #noDataMessage>
                                    <div class="no-data-massage chart-height">
                                      <p>{{ chartConfig.chartName }}</p>
                                      <p>No data found</p>
                                    </div>
                                  </ng-template>
                                </ng-container>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- Utility Card -->
                        <div class="col-12 mb-3">
                          <div class="card shadow">
                            <div class="card-header fw-bold text-white">Utility</div>
                            <div class="card-body">
                              <div class="row dashboard_stats mt-0">
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-success shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-success text-uppercase content_text">Report Completed</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityCompleted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-info shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-info text-uppercase content_text">Field Work Completed</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityOnSite }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-primary shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-primary text-uppercase content_text">
                                          Field Work Partially Completed
                                        </div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityPartially }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-dark shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold text-dark text-uppercase content_text">Pending</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityNotyet }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-started shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart_started text-uppercase content_text">REPORT STARTED</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityReportStarted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-drafted shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart_drafted text-uppercase content_text">REPORT DRAFTED</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityReportDrafted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-CannotComplete shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart_CannotComplete text-uppercase content_text">CANNOT COMPLETE</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityCannotCompleted }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-6 col-lg-3 mb-3">
                                  <div class="card border-left-not-created shadow">
                                    <div class="card-body">
                                      <div class="d-flex no-gutters align-items-center justify-content-between">
                                        <div class="text-xs fw-bold chart-not-created text-uppercase content_text">NOT CREATED</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800 content_text">
                                          {{ dashboardDetail.chartDto.utilityNotGenerated }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="chart-item-section row justify-content-center flex-wrap">
                                <ng-container *ngFor="let chartConfig of doughnutUtilityChartData">
                                  <ng-container *ngIf="chartConfig.isCreateChart; else noDataMessage">
                                    <div echarts [options]="chartConfig" class="chart-height"></div>
                                  </ng-container>
                                  <ng-template #noDataMessage>
                                    <div class="no-data-massage chart-height">
                                      <p>{{ chartConfig.chartName }}</p>
                                      <p>No data found</p>
                                    </div>
                                  </ng-template>
                                </ng-container>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
              <div class="col-12 appFilter">
                <sfl-filter
                  [filterDetails]="filterDetails"
                  (refreshList)="refreshList($event)"
                  (clearParentList)="dashboardScheduleViewDetail.assesmentDashDtos = []; dashboardDetail.assesmentDashDtos = []"
                  (refreshTableHeight)="this.isFilterDisplay = $event"
                ></sfl-filter>
              </div>
            </div>
            <ng-container *ngIf="!isFullView">
              <ng-container *ngTemplateOutlet="template"></ng-container>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>

<ng-template #template>
  <div class="row">
    <div class="col-12 mb-2 mt-2 mt-sm-0 d-flex justify-content-between align-items-center" *ngIf="userRole[0] !== 'customer'">
      <nb-button-group filled status="basic" (valueChange)="switchHandler($event)">
        <button
          nbButtonToggle
          value="complianceView"
          nbTooltip="Compliance View"
          nbTooltipPlacement="top"
          nbTooltipStatus="primary"
          [pressed]="dashboardViewType === 'complianceView'"
        >
          <em class="fa fa-th-list" aria-hidden="true"></em>
        </button>
        <button
          nbButtonToggle
          value="scheduleView"
          nbTooltip="Schedule View"
          nbTooltipPlacement="top"
          nbTooltipStatus="primary"
          [pressed]="dashboardViewType === 'scheduleView'"
        >
          <em class="fa fa-calendar" aria-hidden="true"></em>
        </button>
      </nb-button-group>
      <div class="selected-month d-flex justify-content-between align-items-center" *ngIf="selectedMonth">
        <em class="fa fa-angle-double-left me-3 mt-1 cursor-pointer" (click)="prevMonth()"></em>
        <strong class="cursor-pointer" (click)="removeUnscheduledWO()"> {{ selectedMonth }} </strong>
        <em class="fa fa-angle-double-right ms-3 mt-1 cursor-pointer" (click)="nextMonth()"></em>
      </div>
      <div class="d-flex align-items-center">
        <div class="d-flex align-items-center me-4" *ngIf="dashboardViewType === 'scheduleView'">
          <div class="form-control-group mb-0 me-4">
            <ng-select
              *ngIf="isMultiSelect && selectedWorkOrders.length"
              id="reschedule-type"
              class="custom-width"
              name="rescheduleType"
              [(ngModel)]="selectedRescheduleAction"
              notFoundText="No Action Found"
              placeholder="Select Action"
              [clearable]="true"
              #rescheduleType="ngModel"
              (change)="openBulkActionModal($event)"
            >
              <ng-option *ngFor="let action of rescheduleActionList" [value]="action.value" [disabled]="action.isDisable">
                <span [ngClass]="{ 'not-allowed d-block': action.isDisable }">{{ action.name }}</span>
              </ng-option>
            </ng-select>
          </div>
          <label class="label d-flex align-items-center mb-0 me-4" for="view-switcher">
            Scheduler
            <nb-toggle
              name="isLink"
              [(checked)]="isSchedulerMode"
              status="primary"
              class="ms-2"
              (checkedChange)="handleSchedulerToggle(isSchedulerMode, 'scheduler')"
            ></nb-toggle>
          </label>
          <label class="label d-flex align-items-center mb-0 me-4" for="view-switcher">
            Multi-Select
            <nb-toggle
              name="isLink"
              [(checked)]="isMultiSelect"
              (checkedChange)="handleSchedulerToggle(isMultiSelect, 'multiselect')"
              status="primary"
              class="ms-2"
            ></nb-toggle>
          </label>
          <label class="label d-flex align-items-center mb-0" for="view-switcher">
            Compact
            <nb-toggle
              name="isLink"
              [(checked)]="isCompactView"
              status="primary"
              class="ms-2"
              (checkedChange)="handleSchedulerToggle(isMultiSelect, 'compactView')"
            ></nb-toggle>
          </label>
        </div>
        <em
          *ngIf="isFullView"
          aria-hidden="true"
          class="fa fa-compress-alt text-primary cursor-pointer float-end pe-2"
          (click)="compressView()"
        ></em>
        <em
          *ngIf="!isFullView"
          aria-hidden="true"
          class="fa fa-expand-alt text-primary cursor-pointer float-end pe-2"
          (click)="expandView(template)"
        ></em>
      </div>
    </div>
    <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive" cdkScrollable>
      <table class="table table-hover table-bordered" aria-describedby="Dashboard List">
        <thead>
          <tr>
            <th
              scope="col"
              (click)="
                sort(
                  'CustomerPortfolio',
                  (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['CustomerPortfolio']
                )
              "
            >
              <div class="d-flex align-items-center">
                <span class="me-2">Customer (Portfolio)</span>
                <span
                  class="fa cursor-pointer ms-auto"
                  [ngClass]="{
                    'fa-arrow-up':
                      (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['CustomerPortfolio'] === 'desc',
                    'fa-arrow-down':
                      (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['CustomerPortfolio'] === 'asc',
                    'icon-selected':
                      (dashboardViewType === 'scheduleView' ? filterModelScheduledView : filterModel).sortBy === 'CustomerPortfolio'
                  }"
                ></span>
              </div>
            </th>
            <th
              (click)="sort('SiteName', (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['SiteName'])"
              id="siteName"
            >
              <div class="d-flex align-items-center">
                Site
                <span
                  class="fa px-1 cursor-pointer ms-auto"
                  [ngClass]="{
                    'fa-arrow-up':
                      (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['SiteName'] === 'desc',
                    'fa-arrow-down':
                      (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['SiteName'] === 'asc',
                    'icon-selected': (dashboardViewType === 'scheduleView' ? filterModelScheduledView : filterModel).sortBy === 'SiteName'
                  }"
                ></span>
              </div>
            </th>
            <th
              class="text-center"
              id="kWdc"
              (click)="sort('kWdc', (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['kWdc'])"
            >
              <div class="d-flex align-items-center">
                kWdc
                <span
                  class="fa cursor-pointer ms-auto"
                  [ngClass]="{
                    'fa-arrow-up': (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['kWdc'] === 'desc',
                    'fa-arrow-down':
                      (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['kWdc'] === 'asc',
                    'icon-selected': (dashboardViewType === 'scheduleView' ? filterModelScheduledView : filterModel).sortBy === 'kWdc'
                  }"
                ></span>
              </div>
            </th>
            <th
              class="stateWidth text-center"
              id="stateName"
              (click)="sort('State', (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['State'])"
            >
              <div class="d-flex align-items-center">
                State
                <span
                  class="fa cursor-pointer ms-auto"
                  [ngClass]="{
                    'fa-arrow-up':
                      (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['State'] === 'desc',
                    'fa-arrow-down':
                      (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['State'] === 'asc',
                    'icon-selected': (dashboardViewType === 'scheduleView' ? filterModelScheduledView : filterModel).sortBy === 'State'
                  }"
                ></span>
              </div>
            </th>
            <ng-container *ngIf="userRole[0] !== 'customer'">
              <th
                class="text-center"
                id="region"
                (click)="
                  sort('RegionName', (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['RegionName'])
                "
              >
                <div class="d-flex align-items-center">
                  Region
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up':
                        (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['RegionName'] === 'desc',
                      'fa-arrow-down':
                        (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['RegionName'] === 'asc',
                      'icon-selected':
                        (dashboardViewType === 'scheduleView' ? filterModelScheduledView : filterModel).sortBy === 'RegionName'
                    }"
                  ></span>
                </div>
              </th>
              <th
                class="text-center"
                id="subRegion"
                (click)="
                  sort(
                    'SubRegionName',
                    (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['SubRegionName']
                  )
                "
              >
                <div class="d-flex align-items-center">
                  Subregion
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up':
                        (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['SubRegionName'] === 'desc',
                      'fa-arrow-down':
                        (dashboardViewType === 'scheduleView' ? sortOptionListScheduledView : sortOptionList)['SubRegionName'] === 'asc',
                      'icon-selected':
                        (dashboardViewType === 'scheduleView' ? filterModelScheduledView : filterModel).sortBy === 'SubRegionName'
                    }"
                  ></span>
                </div>
              </th>
            </ng-container>
            <ng-container *ngIf="dashboardViewType === 'complianceView'">
              <th class="text-center" id="sv" *ngIf="isTableColShow(1)">
                <span nbTooltip="Site Visit" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>SV</span>
              </th>
              <th class="text-center" id="ipm" *ngIf="isTableColShow(2)">
                <span nbTooltip="Inverter PM" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>IPM</span>
              </th>
              <th class="text-center" id="mvpm" *ngIf="isTableColShow(3)">
                <span nbTooltip="Medium Voltage PM" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>MVPM</span>
              </th>
              <th class="text-center" id="mvth" *ngIf="isTableColShow(15)">
                <span nbTooltip="Medium Voltage Thermal" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton> MVTH </span>
              </th>
              <th class="text-center" id="tpm" *ngIf="isTableColShow(12)">
                <span nbTooltip="Tracker Preventative Maintenance" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton> TPM </span>
              </th>
              <th class="text-center" id="thermal" *ngIf="isTableColShow(4)">
                <span nbTooltip="BOS Thermal" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>THERMAL</span>
              </th>
              <th class="text-center" id="aerial" *ngIf="isTableColShow(5)">
                <span nbTooltip="Aerial Scan" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>AERIAL</span>
              </th>
              <th class="text-center" id="iv" *ngIf="isTableColShow(7)">
                <span nbTooltip="Electrical-IV" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>IV</span>
              </th>
              <th class="text-center" id="iv" *ngIf="isTableColShow(6)">
                <span nbTooltip="Electrical-VOC" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>VOC</span>
              </th>
              <th class="text-center" id="vgt" *ngIf="isTableColShow(8)">
                <span nbTooltip="Vegetation" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton>VGT</span>
              </th>
              <th class="text-center" id="trq" *ngIf="isTableColShow(14)">
                <span nbTooltip="Module Torque" nbTooltipPlacement="top" nbTooltipStatus="primary" nbButton> TRQ </span>
              </th>
            </ng-container>
            <th class="text-center" *ngIf="isSchedulerMode && dashboardViewType === 'scheduleView'">Unscheduled / Pending Reschedule</th>
            <ng-container *ngIf="dashboardViewType === 'scheduleView' && !selectedMonth">
              <th *ngFor="let month of months" [id]="month.value" class="text-center cursor-pointer" (click)="onMonthClick(month.name)">
                {{ month.name }}
              </th>
            </ng-container>
            <ng-container *ngIf="dashboardViewType === 'scheduleView' && selectedMonth">
              <th *ngFor="let day of days" [id]="day" class="text-center cursor-pointer">
                {{ currentMonthNumber + '/' + day }}
              </th>
            </ng-container>
          </tr>
        </thead>
        <tbody cdkDropListGroup>
          <tr
            *ngFor="
              let assementDash of (dashboardViewType === 'scheduleView' ? dashboardScheduleViewDetail : dashboardDetail).assesmentDashDtos
                | paginate
                  : {
                      itemsPerPage: (dashboardViewType === 'scheduleView' ? filterModelScheduledView : filterModel).itemsCount,
                      currentPage: dashboardViewType === 'scheduleView' ? currentPageForScheduledView : currentPage,
                      totalItems: (dashboardViewType === 'scheduleView' ? dashboardScheduleViewDetail : dashboardDetail).totalCount
                    };
              let i = index
            "
          >
            <td class="text-nowrap">{{ assementDash?.customerPortfolio }}</td>
            <td class="text-truncate" nbTooltip="{{ assementDash?.siteName }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
              {{ assementDash?.siteName }}
            </td>
            <td class="text-end">{{ assementDash?.dcSize | sflRound | sflNumberWithCommas }}</td>
            <td
              class="text-center state-min-width"
              nbTooltip="{{ assementDash?.state }}"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
            >
              {{ assementDash?.stateAbb }}
            </td>
            <ng-container *ngIf="userRole[0] !== 'customer'">
              <td nbTooltip="{{ assementDash?.regionName }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                {{ assementDash?.regionName }}
              </td>
              <td nbTooltip="{{ assementDash?.subRegionName }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                {{ assementDash?.subRegionName }}
              </td>
            </ng-container>
            <ng-container *ngIf="dashboardViewType === 'complianceView'">
              <td
                class="text-center"
                *ngIf="isTableColShow(1)"
                [ngClass]="{
                  abcd: assementDash?.siteVisitTask === 'M',
                  abcdQ: assementDash?.siteVisitTask === 'Q'
                }"
              >
                <ul class="wo-list" *ngIf="assementDash.siteVisitTask !== 'NA' && assementDash.siteVisitTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.svwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      QQ:
                        item?.assementTypestr === 'Q1' ||
                        item?.assementTypestr === 'Q2' ||
                        item?.assementTypestr === 'Q3' ||
                        item?.assementTypestr === 'Q4',
                      AQ: item?.assementTypestr === 'A',
                      HQ: item?.assementTypestr === 'H1' || item?.assementTypestr === 'H2',
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white DashboardWOStatus text-center"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(2)">
                <ul class="wo-list" *ngIf="assementDash.inverterPMTask !== 'NA' && assementDash.inverterPMTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.ipmwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      AIPM: item?.assementTypestr === 'A',
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(3)">
                <ul class="wo-list" *ngIf="assementDash.mvpmTask !== 'NA' && assementDash.mvpmTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.mvpmwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(15)">
                <ul class="wo-list" *ngIf="assementDash?.mvthTask !== 'NA' && assementDash?.mvthTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash?.mvthwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(12)">
                <ul class="wo-list" *ngIf="assementDash?.tpmTask !== 'NA' && assementDash?.tpmTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash?.tpmwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="abcdQ text-center" *ngIf="isTableColShow(4)">
                <ul class="wo-list" *ngIf="assementDash.thermalTask !== 'NA' && assementDash.thermalTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.thermalWO"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(5)">
                <ul class="wo-list" *ngIf="assementDash.aerialScanTask !== 'NA' && assementDash.aerialScanTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.aswo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(7)">
                <ul class="wo-list" *ngIf="assementDash.electricalIVTask !== 'NA' && assementDash.electricalIVTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.ivwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q-IV"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(6)">
                <ul class="wo-list" *ngIf="assementDash.electricalVOCTask !== 'NA' && assementDash.electricalVOCTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.vocwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(8)">
                <ul class="wo-list" *ngIf="assementDash.vegetationTask !== 'NA' && assementDash.vegetationTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash.vgtwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item?.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item?.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item?.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item?.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item?.isFieldTechFound
                    }"
                    class="text-white DashboardWOStatus"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
              <td class="text-center" *ngIf="isTableColShow(14)">
                <ul class="wo-list" *ngIf="assementDash?.trqTask !== 'NA' && assementDash?.trqTask !== 'Alltime'">
                  <li
                    *ngFor="let item of assementDash?.trqwo"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, item)"
                    [ngClass]="{
                      HasNoData: item.status === WoStatuses.NOT_FOUND.id,
                      ReportCompleted: item.status === WoStatuses.REPORT_COMPLETE.id,
                      WorkCompleted: item.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                      NotYet: item.status === WoStatuses.PENDING.id,
                      PartiallyComplete: item.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                      Late: item.status === WoStatuses.DRAFT.id,
                      ReportDrafted: item?.status === WoStatuses.REPORT_DRAFTED.id,
                      ReportStarted: item?.status === WoStatuses.REPORT_STARTED.id,
                      CannotComplete: item?.status === WoStatuses.CANNOT_COMPLETE.id,
                      liDisabled: isLiDisabled,
                      'border-orange': item.isFieldTechFound
                    }"
                    class="text-white Q"
                  >
                    {{ item?.assementTypestr }}
                  </li>
                </ul>
              </td>
            </ng-container>
            <td
              class="text-center box-td td-custom-width"
              *ngIf="isSchedulerMode && dashboardViewType === 'scheduleView'"
              cdkDropList
              cdkDropListSortingDisabled
              [cdkDropListData]="dashboardScheduleViewDetail.assesmentDashDtos[i].unscheduled"
              (cdkDropListDropped)="onWODropped($event, i)"
              [cdkDropListAutoScrollDisabled]="false"
              [cdkDropListAutoScrollStep]="15"
            >
              <div
                (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, unscheduledWO)"
                class="counts m-2 NotYet"
                [class.border-yellow]="
                  unscheduledWO?.status === 4 &&
                  unscheduledWO?.tentativeMonth &&
                  !(unscheduledWO?.rescheduleDate || unscheduledWO?.dateScheduled)
                "
                [class.pending-dashed-border]="unscheduledWO?.isPendingReschedule"
                *ngFor="let unscheduledWO of assementDash.unscheduled"
                (cdkDragEntered)="previousIndexOfDraggedElement = i"
                cdkDrag
              >
                <div
                  class="d-flex align-items-center"
                  [ngClass]="isMultiSelect && unscheduledWO.isCheckBox ? 'justify-content-start' : 'justify-content-center'"
                >
                  <input
                    type="checkbox"
                    class="me-1 custom-checkbox"
                    *ngIf="isMultiSelect && unscheduledWO.isCheckBox"
                    [(ngModel)]="unscheduledWO.isSelected"
                    (click)="$event.stopPropagation()"
                    [disabled]="unscheduledWO.isDisable"
                    (change)="onCheckBoxChange($event, unscheduledWO)"
                  />
                  <span class="flex-grow-1 text-center">{{ unscheduledWO?.assementTypestr }}</span>
                </div>
              </div>
            </td>
            <ng-container *ngIf="dashboardViewType === 'scheduleView'">
              <ng-container *ngIf="!selectedMonth">
                <td
                  class="text-center box-td td-custom-width"
                  *ngFor="let month of months"
                  cdkDropList
                  [cdkDropListData]="dashboardScheduleViewDetail.assesmentDashDtos[i][month.value]"
                  (cdkDropListDropped)="onWODropped($event, i, month.value)"
                  (cdkDropListEntered)="currentIndexOfDraggedElement = i"
                  [cdkDropListAutoScrollDisabled]="false"
                  [cdkDropListAutoScrollStep]="15"
                >
                  <div
                    class="count-wrapper cursor-pointer"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, assementDash, month.value)"
                    *ngIf="isCompactView"
                  >
                    <ng-container *ngFor="let status of statuses">
                      <span
                        class="counts cursor-pointer"
                        *ngIf="assementDash[month.value] | woCount : status"
                        [ngClass]="{
                          HasNoData: status === WoStatuses.NOT_FOUND.id,
                          ReportCompleted: status === WoStatuses.REPORT_COMPLETE.id,
                          WorkCompleted: status === WoStatuses.FIELD_WORK_COMPLETE.id,
                          NotYet: status === WoStatuses.PENDING.id || status === 99999,
                          PartiallyComplete: status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                          Late: status === WoStatuses.DRAFT.id,
                          ReportDrafted: status === WoStatuses.REPORT_DRAFTED.id,
                          ReportStarted: status === WoStatuses.REPORT_STARTED.id,
                          CannotComplete: status === WoStatuses.CANNOT_COMPLETE.id,
                          liDisabled: isLiDisabled,
                          'border-orange': assementDash[month.value] | hasFieldTechPipe,
                          'border-yellow': status === 99999
                        }"
                      >
                        {{ assementDash[month.value] | woCount : status }}
                      </span>
                    </ng-container>
                  </div>
                  <ng-container *ngIf="!isCompactView">
                    <div
                      *ngFor="let wo of assementDash[month.value]"
                      (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, wo)"
                      class="counts m-2"
                      [ngClass]="{
                        HasNoData: wo?.status === WoStatuses.NOT_FOUND.id,
                        ReportCompleted: wo?.status === WoStatuses.REPORT_COMPLETE.id,
                        WorkCompleted: wo?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                        NotYet: wo?.status === WoStatuses.PENDING.id,
                        PartiallyComplete: wo?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                        Late: wo?.status === WoStatuses.DRAFT.id,
                        ReportDrafted: wo?.status === WoStatuses.REPORT_DRAFTED.id,
                        ReportStarted: wo?.status === WoStatuses.REPORT_STARTED.id,
                        CannotComplete: wo?.status === WoStatuses.CANNOT_COMPLETE.id,
                        liDisabled: isLiDisabled,
                        'border-orange': wo?.isFieldTechFound,
                        'border-yellow':
                          wo?.status === WoStatuses.PENDING.id && wo?.tentativeMonth && !(wo?.rescheduleDate || wo?.dateScheduled)
                      }"
                    >
                      <div
                        class="d-flex align-items-center"
                        [ngClass]="isMultiSelect && wo.isCheckBox ? 'justify-content-start' : 'justify-content-center'"
                      >
                        <input
                          type="checkbox"
                          class="me-1 custom-checkbox"
                          *ngIf="isMultiSelect && wo.isCheckBox"
                          [(ngModel)]="wo.isSelected"
                          (click)="$event.stopPropagation()"
                          [disabled]="wo.isDisable"
                          (change)="onCheckBoxChange($event, wo)"
                        />
                        <span class="flex-grow-1 text-center">{{ wo?.assementTypestr }}</span>
                      </div>
                    </div>
                  </ng-container>
                </td>
              </ng-container>
              <ng-container *ngIf="selectedMonth">
                <td
                  class="text-center box-td"
                  *ngFor="let day of days"
                  cdkDropList
                  [cdkDropListData]="dashboardScheduleViewDetail.assesmentDashDtos[i][day]"
                  (cdkDropListDropped)="onWODropped($event, i, '', day)"
                  (cdkDropListEntered)="currentIndexOfDraggedElement = i"
                  [cdkDropListAutoScrollDisabled]="false"
                  [cdkDropListAutoScrollStep]="20"
                >
                  <div
                    class="count-wrapper cursor-pointer"
                    (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, assementDash, '', day)"
                    *ngIf="isCompactView"
                  >
                    <ng-container *ngFor="let status of statuses">
                      <span
                        class="counts cursor-pointer"
                        *ngIf="assementDash[day] | woCount : status"
                        [ngClass]="{
                          HasNoData: status === WoStatuses.NOT_FOUND.id,
                          ReportCompleted: status === WoStatuses.REPORT_COMPLETE.id,
                          WorkCompleted: status === WoStatuses.FIELD_WORK_COMPLETE.id,
                          NotYet: status === WoStatuses.PENDING.id || status === 99999,
                          PartiallyComplete: status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                          Late: status === WoStatuses.DRAFT.id,
                          ReportDrafted: status === WoStatuses.REPORT_DRAFTED.id,
                          ReportStarted: status === WoStatuses.REPORT_STARTED.id,
                          CannotComplete: status === WoStatuses.CANNOT_COMPLETE.id,
                          liDisabled: isLiDisabled,
                          'border-orange': assementDash[day] | hasFieldTechPipe,
                          'border-yellow': status === 99999
                        }"
                      >
                        {{ assementDash[day] | woCount : status }}
                      </span>
                    </ng-container>
                  </div>
                  <ng-container *ngIf="!isCompactView">
                    <div
                      *ngFor="let wo of assementDash[day]"
                      (click)="onWOClick(i, assementDash.portfolioId, assementDash.siteId, wo)"
                      [ngClass]="{
                        HasNoData: wo?.status === WoStatuses.NOT_FOUND.id,
                        ReportCompleted: wo?.status === WoStatuses.REPORT_COMPLETE.id,
                        WorkCompleted: wo?.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                        NotYet: wo?.status === WoStatuses.PENDING.id,
                        PartiallyComplete: wo?.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                        Late: wo?.status === WoStatuses.DRAFT.id,
                        ReportDrafted: wo?.status === WoStatuses.REPORT_DRAFTED.id,
                        ReportStarted: wo?.status === WoStatuses.REPORT_STARTED.id,
                        CannotComplete: wo?.status === WoStatuses.CANNOT_COMPLETE.id,
                        liDisabled: isLiDisabled,
                        'border-orange': wo?.isFieldTechFound,
                        'border-yellow':
                          wo?.status === WoStatuses.PENDING.id && wo?.tentativeMonth && !(wo?.rescheduleDate || wo?.dateScheduled)
                      }"
                      class="counts m-2"
                    >
                      <div
                        class="d-flex align-items-center"
                        [ngClass]="isMultiSelect && wo.isCheckBox ? 'justify-content-start' : 'justify-content-center'"
                      >
                        <input
                          type="checkbox"
                          class="me-1 custom-checkbox"
                          *ngIf="isMultiSelect && wo.isCheckBox"
                          [(ngModel)]="wo.isSelected"
                          (click)="$event.stopPropagation()"
                          [disabled]="wo.isDisable"
                          (change)="onCheckBoxChange($event, wo)"
                        />
                        <span class="flex-grow-1 text-center">{{ wo?.assementTypestr }}</span>
                      </div>
                    </div>
                  </ng-container>
                </td>
              </ng-container>
            </ng-container>
          </tr>
          <tr>
            <td
              colspan="18"
              *ngIf="!(dashboardViewType === 'scheduleView' ? dashboardScheduleViewDetail : dashboardDetail).assesmentDashDtos?.length"
              class="text-center"
            >
              No Data Found
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div
      class="mt-2 d-md-flex align-items-center"
      *ngIf="dashboardDetail.assesmentDashDtos?.length && dashboardViewType === 'complianceView'"
    >
      <div class="d-flex align-items-center">
        <label class="mb-0">Items per page: </label>
        <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" (change)="onChangeSize()" appendTo="body">
          <ng-option value="5">5</ng-option>
          <ng-option value="10">10</ng-option>
          <ng-option value="50">50</ng-option>
          <ng-option value="100">100</ng-option>
        </ng-select>
      </div>
      <strong class="ms-md-3">Total Sites: {{ dashboardDetail.totalCount }}</strong>
      <div class="ms-md-auto ms-sm-0">
        <pagination-controls (pageChange)="onPageChange($event, 'complianceView')" class="paginate"></pagination-controls>
      </div>
    </div>
    <div
      class="mt-2 d-md-flex align-items-center"
      *ngIf="dashboardScheduleViewDetail.assesmentDashDtos?.length && dashboardViewType === 'scheduleView'"
    >
      <div class="d-flex align-items-center">
        <label class="mb-0">Items per page: </label>
        <ng-select class="ms-2" [(ngModel)]="pageSizeScheduledView" [clearable]="false" (change)="onChangeSize()" appendTo="body">
          <ng-option value="5">5</ng-option>
          <ng-option value="10">10</ng-option>
          <ng-option value="50">50</ng-option>
          <ng-option value="100">100</ng-option>
        </ng-select>
      </div>
      <strong class="ms-md-3">Total Sites: {{ dashboardScheduleViewDetail.totalCount }}</strong>
      <div class="ms-md-auto ms-sm-0">
        <pagination-controls (pageChange)="onPageChange($event, 'scheduleView')" class="paginate"></pagination-controls>
      </div>
    </div>
  </div>
</ng-template>
