<nb-card class="reportsSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Site Audit</h6>
        <div class="ms-auto">
          <button
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="medium"
            [disabled]="loading || newSAReportLoading"
            (click)="addNewReport(startNewReportTemp)"
            *ngIf="userRoles[0] !== 'customer'"
            type="button"
          >
            <span class="d-none d-lg-inline-block">Start New Report</span>
            <i class="d-inline-block d-lg-none fa-solid fa-plus"></i>
          </button>
          <button
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="medium"
            [disabled]="loading || newSAReportLoading"
            (click)="addExport()"
            type="button"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body style="overflow: inherit !important">
    <div class="form-control-group">
      <div class="row">
        <div class="col-12 reportsFilter appFilter mb-3">
          <sfl-filter
            [filterDetails]="filterDetails"
            (refreshList)="refreshList($event)"
            (refreshTableHeight)="this.isFilterDisplay = $event"
            [addReportTypeJHA]="true"
          ></sfl-filter>
        </div>
      </div>
    </div>

    <div class="form-control-group">
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Report List">
          <thead>
            <tr>
              <th scope="col" (click)="sort('CustomerPortfolio', sortOptionList['CustomerPortfolio'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Customer (Portfolio)</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['CustomerPortfolio'] === 'desc',
                      'fa-arrow-down': sortOptionList['CustomerPortfolio'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'CustomerPortfolio'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('SiteName', sortOptionList['SiteName'])" id="siteName">
                <div class="d-flex align-items-center">
                  <span class="me-2">Site</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['SiteName'] === 'desc',
                      'fa-arrow-down': sortOptionList['SiteName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'SiteName'
                    }"
                  ></span>
                </div>
              </th>
              <th id="workOrder">Work Order</th>
              <th id="year">Year</th>
              <th id="reportStatus">Status</th>
              <th id="uploadDate" (click)="sort('Year', sortOptionList['Year'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Last Uploaded</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Year'] === 'desc',
                      'fa-arrow-down': sortOptionList['Year'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Year'
                    }"
                  ></span>
                </div>
              </th>
              <th class="text-center" id="actions">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let siteAuditReportData of reportsData
                  | paginate : { itemsPerPage: filterModel.itemsCount, currentPage: currentPage, totalItems: total }
              "
            >
              <td data-title="Customer (Portfolio)" class="td-custom-width">
                {{ siteAuditReportData?.customerPortfolio }}
              </td>
              <td
                data-title="Site"
                class="text-truncate td-custom-width"
                nbTooltip="{{ siteAuditReportData?.siteName }}"
                nbTooltipPlacement="top"
                nbTooltipStatus="primary"
              >
                {{ siteAuditReportData?.siteName }}
              </td>
              <td data-title="Work Order" class="report-workOrder">
                {{ siteAuditReportData?.workorderName }}
              </td>
              <td data-title="Year">{{ siteAuditReportData?.year }}</td>
              <td data-title="Status" style="min-width: 100px">
                {{ siteAuditReportData?.reportStatusLabel ? siteAuditReportData?.reportStatusLabel : '-' }}
              </td>
              <td data-title="Last Uploaded" style="min-width: 105px">{{ siteAuditReportData?.createdDate | date : fullDateFormat }}</td>
              <td data-title="Action" class="text-end report-action" *ngIf="!viewdeletetedbutton">
                <div class="d-md-flex justify-content-end">
                  <a class="px-2 listgrid-icon" [routerLink]="['edit/' + siteAuditReportData?.reportId + '/siteauditreport']">
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-primary"
                    [routerLink]="['/entities/site-audit-report/image-gallery/' + siteAuditReportData?.reportId]"
                  >
                    <em class="fa fa-images" nbTooltip="Image Gallery" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a class="px-2 text-primary listgrid-icon" (click)="imageJhaCopyUrl(siteAuditReportData?.reportId)">
                    <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a class="px-2 text-primary listgrid-icon" (click)="viewReport(siteAuditReportData?.reportId)">
                    <em class="fa fa-cloud" nbTooltip="Uploaded Report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a class="px-2 text-danger listgrid-icon" (click)="deleteJHAReport(siteAuditReportData?.reportId)">
                    <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                  </a>
                </div>
              </td>
            </tr>
            <tr *ngIf="!reportsData?.length">
              <td colspan="6" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="reportsData?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
<ng-template #startNewReportTemp>
  <div class="modal-header">
    <h4 class="modal-title pull-left">New Report</h4>
  </div>
  <form
    name="startNewReportForm"
    #startNewReportForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    (ngSubmit)="startNewReportForm.valid && saveNewReportDetails(startNewReportForm)"
  >
    <section [nbSpinner]="newSAReportLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
      <div class="modal-body">
        <div class="row">
          <div class="col-12">
            <div class="form-control-group">
              <label class="label" for="input-customerName">Customer<span class="ml-1 text-danger">*</span></label>
              <nb-form-field class="form-field-group">
                <input
                  nbInput
                  fullWidth
                  [(ngModel)]="siteAuditNewReportModel.customerName"
                  #customerName="ngModel"
                  name="customerName"
                  pattern=".*\S.*"
                  id="input-customerName"
                  class="form-control"
                  required
                  (ngModelChange)="onCPSModelChange($event, 'customerList')"
                  [nbAutocomplete]="cusAutoNgModel"
                />
                <div
                  nbSuffix
                  [nbSpinner]="isCusPortSiteLoading"
                  nbSpinnerStatus="primary"
                  nbSpinnerSize="small"
                  class="input-spinner appSpinner"
                ></div>
              </nb-form-field>
              <nb-autocomplete #cusAutoNgModel (selectedChange)="siteAuditNewReportModel.customerName = $event">
                <nb-option *ngFor="let item of clonedSiteAuditCPSListData.customerList" [value]="item">
                  {{ item }}
                </nb-option>
              </nb-autocomplete>
              <div class="error-msg-box" style="height: 20px">
                <sfl-error-msg
                  [control]="customerName"
                  [isFormSubmitted]="startNewReportForm?.submitted"
                  fieldName="Customer"
                ></sfl-error-msg>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-control-group">
              <label class="label" for="input-portfolioName">Portfolio<span class="ml-1 text-danger">*</span></label>
              <nb-form-field class="form-field-group">
                <input
                  nbInput
                  fullWidth
                  [(ngModel)]="siteAuditNewReportModel.portfolioName"
                  #portfolioName="ngModel"
                  name="portfolioName"
                  pattern=".*\S.*"
                  id="input-portfolioName"
                  class="form-control"
                  required
                  (ngModelChange)="onCPSModelChange($event, 'portfolioList')"
                  [nbAutocomplete]="portAutoNgModel"
                />
                <div
                  nbSuffix
                  [nbSpinner]="isCusPortSiteLoading"
                  nbSpinnerStatus="primary"
                  nbSpinnerSize="small"
                  class="input-spinner appSpinner"
                ></div>
              </nb-form-field>
              <nb-autocomplete #portAutoNgModel (selectedChange)="siteAuditNewReportModel.portfolioName = $event">
                <nb-option *ngFor="let item of clonedSiteAuditCPSListData.portfolioList" [value]="item">
                  {{ item }}
                </nb-option>
              </nb-autocomplete>
              <div class="error-msg-box" style="height: 20px">
                <sfl-error-msg
                  [control]="portfolioName"
                  [isFormSubmitted]="startNewReportForm?.submitted"
                  fieldName="Portfolio"
                ></sfl-error-msg>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-control-group">
              <label class="label" for="input-siteName">Site<span class="ml-1 text-danger">*</span></label>
              <nb-form-field class="form-field-group">
                <input
                  nbInput
                  fullWidth
                  [(ngModel)]="siteAuditNewReportModel.siteName"
                  #siteName="ngModel"
                  name="siteName"
                  pattern=".*\S.*"
                  id="input-siteName"
                  class="form-control"
                  required
                  (ngModelChange)="onCPSModelChange($event, 'siteList')"
                  [nbAutocomplete]="siteAutoNgModel"
                />
                <div
                  nbSuffix
                  [nbSpinner]="isCusPortSiteLoading"
                  nbSpinnerStatus="primary"
                  nbSpinnerSize="small"
                  class="input-spinner appSpinner"
                ></div>
              </nb-form-field>
              <nb-autocomplete #siteAutoNgModel (selectedChange)="siteAuditNewReportModel.siteName = $event">
                <nb-option *ngFor="let item of clonedSiteAuditCPSListData.siteList" [value]="item">
                  {{ item }}
                </nb-option>
              </nb-autocomplete>
              <div class="error-msg-box" style="height: 20px">
                <sfl-error-msg [control]="siteName" [isFormSubmitted]="startNewReportForm?.submitted" fieldName="Site"></sfl-error-msg>
              </div>
            </div>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-12">
            <label class="label" for="input-siteLayoutImage">Site Layout Image</label>
            <div class="dropZone" ngFileDragDr (fileDropped)="getUploadedFiles($event)">
              <input type="file" #file accept="image/*" multiple (change)="getUploadedFiles($event?.target?.files, file)" />
              <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em>
              <h5 class="fw-bold">Drop Image To Attach</h5>
              <label style="text-transform: none" class="fw-bold">OR Click to Browse </label>
            </div>
            <ul *ngIf="siteLayoutImg.length">
              <li *ngFor="let item of siteLayoutImg; let i = index">
                <span>{{ item.name }}</span>
                <em
                  (click)="deleteFile(i)"
                  *ngIf="!checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER])"
                  nbtooltip="Delete"
                  nbtooltipplacement="top"
                  nbtooltipstatus="text-danger"
                  aria-hidden="true"
                  class="fa fa-times-circle text-danger ms-2 pointer"
                ></em>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" #closeModalButton (click)="modalRef.hide()" [disabled]="newSAReportLoading">
          Close
        </button>
        <button type="submit" class="btn btn-primary" [disabled]="newSAReportLoading">Generate New Work Order</button>
      </div>
    </section>
  </form>
</ng-template>
