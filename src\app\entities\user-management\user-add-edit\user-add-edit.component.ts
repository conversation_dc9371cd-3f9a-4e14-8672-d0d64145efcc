import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { APP_ROUTES, AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { SiteDropdown, TimeZone, User, UserAuthenticationAuditLog } from '../../../@shared/models/user.model';
import { AlertService } from '../../../@shared/services';
import { StateService } from '../../../@shared/services/states.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { UserService } from '../user.service';
import { AUTHORITY_ROLE_STRING, ROLE_DISPLAY_NAME, <PERSON><PERSON><PERSON>_NAME, R<PERSON><PERSON>_TYPE } from '../../../@shared/enums';

@Component({
  selector: 'sfl-user-add-edit',
  templateUrl: './user-add-edit.component.html',
  styleUrls: ['./user-add-edit.component.scss']
})
export class UserAddEditComponent implements OnInit, OnDestroy {
  roles: Dropdown[];
  portfolioAcesss: [];
  loading = false;
  user: User = new User();
  currentValueForTwoFactorEnabled = null;
  portfolioData: any = [];
  siteList: any = [];
  isEdit = false;
  showSite = false;
  showPortfolio = false;
  readonly ROUTES = APP_ROUTES;
  subscription: Subscription = new Subscription();
  contactNoFormat = AppConstants.phoneNumberMask;
  id: number;
  loggedUser;
  states: Dropdown[];
  timeZoneList: TimeZone[] = [];
  siteDropdown: SiteDropdown = new SiteDropdown();
  filteredPortfolioIds: number[] = [];
  filteredPortfolioAssignmentIds: number[] = [];
  clonedUserPortfolioList: number[] = [];
  filteredSiteIds: number[] = [];
  userAuthenticationAuditLogs: UserAuthenticationAuditLog[] = [];
  portfolioAssignmentData: Dropdown[] = [];
  dropdownLoading = {};

  constructor(
    private readonly userService: UserService,
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly route: ActivatedRoute,
    private readonly portfolioService: PortfolioService,
    private readonly stateService: StateService,
    private readonly storageService: StorageService
  ) {
    this.loggedUser = this.storageService.get('user').authorities;
  }

  ngOnInit() {
    this.getTimeZones();
    this.getRoles();
    this.route.params.subscribe(params => {
      if (params && params.id) {
        this.id = params.id;
        this.isEdit = true;
      } else {
        this.isEdit = false;
        this.getPortfolioAccess();
      }
      this.getStates();
    });
  }

  // Get User by Id
  getUser(id) {
    this.subscription.add(
      this.userService.getById(id).subscribe({
        next: (res: User) => {
          this.user = res;
          this.currentValueForTwoFactorEnabled = res?.twoFactorEnabled;
          this.user.Role = res.roles[0].toUpperCase();
          if (res.roles[0] === AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER] || res.roles[0] === AUTHORITY_ROLE_STRING[ROLE_TYPE.CONTRACTOR]) {
            this.showSite = true;
            this.showPortfolio = false;
          } else {
            this.showSite = false;
            this.showPortfolio = true;
          }
          if (res.portfolioAccess?.length) {
            this.clonedUserPortfolioList = JSON.parse(JSON.stringify(this.user.portfolioAccess));
          }
          this.getPortfolioAccess();
          this.user.portfolioAccess.length ? this.getSite() : null;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getRoles() {
    this.loading = true;
    this.dropdownLoading['roles'] = true;
    this.subscription.add(
      this.userService.getRoles().subscribe({
        next: (res: Dropdown[]) => {
          this.roles = res;
          if (this.isEdit) {
            // this.getUser(this.id);
            this.getUserAuthenticationAuditlogs(this.id);
          } else {
            this.loading = false;
            this.user.Role = this.roles[0].name;
            this.onChange(this.user.Role);
          }
          this.dropdownLoading['roles'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['roles'] = false;
        }
      })
    );
  }

  get userLabel(): string {
    switch (this.user.Role) {
      case ROLE_NAME[ROLE_TYPE.PORTFOLIOMANAGER]:
        return ROLE_DISPLAY_NAME[ROLE_TYPE.PORTFOLIOMANAGER];
      case ROLE_NAME[ROLE_TYPE.ANALYST]:
        return ROLE_DISPLAY_NAME[ROLE_TYPE.ANALYST];
      case ROLE_NAME[ROLE_TYPE.MANAGER]:
        return ROLE_DISPLAY_NAME[ROLE_TYPE.MANAGER];
      default:
        return '';
    }
  }

  // Get User audit logs by Id
  getUserAuthenticationAuditlogs(id) {
    this.loading = true;
    this.subscription.add(
      this.userService.getUserAuthenticationAuditLogById(id).subscribe({
        next: (res: UserAuthenticationAuditLog[]) => {
          this.userAuthenticationAuditLogs = res;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getPortfolioAccess() {
    this.dropdownLoading['portfolio'] = true;
    this.subscription.add(
      this.portfolioService.getPortfolioAccess(true).subscribe({
        next: (res: Dropdown[]) => {
          this.portfolioData = res;
          this.setPortfolioAssignmentDropdown();
          if (this.isEdit && this.user.Role === ROLE_NAME[ROLE_TYPE.ADMIN] && this.user.isAllPortfolio) {
            this.user.portfolioAccess = this.portfolioData.map(item => item.id);
            this.user.isAllPortfolio = true;
          }
          this.dropdownLoading['portfolio'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['portfolio'] = false;
        }
      })
    );
  }

  getSite() {
    this.dropdownLoading['site'] = true;
    this.siteDropdown.ids = this.user.portfolioAccess;
    this.siteDropdown.isActive = this.user.isActive;
    this.loading = true;
    this.subscription.add(
      this.userService.getAllSitesByPortfolioAccess(this.siteDropdown).subscribe({
        next: async (res: Dropdown[]) => {
          this.siteList = res;
          this.user.siteAccess = await this.validateSelectedSites();
          this.loading = false;
          this.dropdownLoading['site'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['site'] = false;
        }
      })
    );
  }

  validateSelectedSites(): Promise<number[]> {
    return new Promise(resolve => {
      resolve(this.siteList.filter(o1 => this.user?.siteAccess?.some(o2 => o1.id === o2)).map(item => item.id));
    });
  }

  onPortfolioChange() {
    this.siteList = [];
    if (this.user.portfolioAccess.length) {
      this.getSite();
    } else {
      this.user.siteAccess = [];
    }
  }

  onPortfolioRemove(event: Dropdown | number, fromClear = false): void {
    const id = typeof event === 'number' ? event : event.id;
    if (this.isPortfolioAssignmentRole() && id) {
      this.user.assignment = fromClear ? [] : this.user.assignment.filter(item => item !== id);
      this.setPortfolioAssignmentDropdown();
    }
  }

  onPortfolioClear(events: (Dropdown | number)[] = this.clonedUserPortfolioList): void {
    events.forEach(event => this.onPortfolioRemove(event, true));
  }

  onPortfolioAdd(event: Dropdown | number): void {
    const id = typeof event === 'number' ? event : event.id;
    if (this.isPortfolioAssignmentRole() && id) {
      this.setPortfolioAssignmentDropdown();
      // to be used
      // this.setPortfolioAssignment(id);
    }
  }

  isPortfolioAssignmentRole(): boolean {
    return [ROLE_NAME[ROLE_TYPE.PORTFOLIOMANAGER], ROLE_NAME[ROLE_TYPE.ANALYST], ROLE_NAME[ROLE_TYPE.MANAGER]].includes(this.user.Role);
  }

  setPortfolioAssignment(id: number = null): void {
    this.setPortfolioAssignmentDropdown();
    if (this.user.portfolioAccess.length) {
      this.user.assignment = [...new Set([...this.user.assignment, ...JSON.parse(JSON.stringify(id ? [id] : this.user.portfolioAccess))])];
      this.clonedUserPortfolioList = JSON.parse(JSON.stringify(this.user.portfolioAccess));
    } else {
      this.user.assignment = [];
    }
  }

  getTimeZones() {
    this.dropdownLoading['timeZone'] = true;
    this.subscription.add(
      this.userService.getAllTimeZone().subscribe({
        next: (res: TimeZone[]) => {
          this.timeZoneList = res;
          if (this.isEdit) {
            // this.getUser(this.id);
          } else {
            this.user.userTimeZone = res.find(item => item.name.includes('America/New_York')).id;
          }
          this.dropdownLoading['timeZone'] = false;
        }
      })
    );
  }

  // get Add/Edit Title
  getComponentTitle() {
    let result = 'Add User';
    if (!this.user.id) {
      return result;
    } else {
      result = `Edit User`;
      return result;
    }
  }

  // Create  and Edit User
  createUser() {
    this.loading = true;
    this.user.roles = new Array(this.user.Role);
    this.user.email = this.user.email.trim();
    this.user.portfolioAccess = this.user.portfolioAccess ? this.user.portfolioAccess : [];
    this.user.isAllPortfolio =
      this.user.portfolioAccess.length && this.portfolioData.length === this.user.portfolioAccess.length ? true : false;
    this.user.isAllPortfolioSite = this.user?.siteAccess?.length && this.siteList.length === this.user.siteAccess.length ? true : false;
    if (!this.user.id) {
      this.user.id = 0;
      this.user.phoneNo = this.user.phoneNo ? this.user.phoneNo : null;
      this.user.profileBackgroundColor = this.userService.getRandomColor();
      this.subscription.add(
        this.userService.createUser(this.user).subscribe({
          next: res => {
            this.router.navigateByUrl(APP_ROUTES.USER);
            this.alertService.showSuccessToast(res.message);
            this.loading = false;
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    } else {
      this.subscription.add(
        this.userService.updateUser(this.user).subscribe({
          next: res => {
            if (res) {
              this.router.navigateByUrl(APP_ROUTES.USER);
              this.alertService.showSuccessToast(res.message);
              this.loading = false;
            } else {
              this.loading = false;
            }
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }
  }

  onChange(event) {
    this.user.portfolioAccess = [];
    this.user.assignment = [];
    this.portfolioAssignmentData = [];
    this.user.siteAccess = [];
    this.user.isAllPortfolioSite = false;
    this.user.isAllPortfolio = false;
    this.siteList = [];
    if (event === ROLE_NAME[ROLE_TYPE.CONTRACTOR] || event === ROLE_NAME[ROLE_TYPE.CUSTOMER]) {
      this.showSite = true;
      this.showPortfolio = false;
    } else {
      this.showSite = false;
      this.showPortfolio = true;
    }
    this.getPortfolioAccess();
  }

  getStates() {
    this.dropdownLoading['state'] = true;
    this.subscription.add(
      this.stateService.getStates().subscribe({
        next: res => {
          this.states = res;
          // this.user.state = this.states[0].name;
          if (this.isEdit) {
            this.getUser(this.id);
          }
          this.dropdownLoading['state'] = false;
        },
        error: e => {
          this.dropdownLoading['state'] = false;
          this.loading = false;
        }
      })
    );
  }

  changePortfolioAccess(event: any) {
    if (event === true) {
      this.selectUnselectAllPortfolio(true, true);
    } else {
      this.user.portfolioAccess = [];
      this.user.isAllPortfolio = false;
      this.resetPortfolioAssignmentDropdown();
    }
  }

  changSiteAccess(event: any) {
    if (event === true) {
      this.selectUnselectAllSite(true, true);
    } else {
      this.user.siteAccess = [];
      this.user.isAllPortfolioSite = false;
    }
  }

  selectUnselectAllPortfolio(allPortfolioToggle = false, isSelect = false) {
    if (isSelect) {
      this.loading = true;
      if (allPortfolioToggle || !this.filteredPortfolioIds.length) {
        this.user.portfolioAccess = [];
        for (const i of this.portfolioData) {
          this.user.portfolioAccess.push(i.id);
        }
      } else {
        this.user.portfolioAccess = [...new Set([...this.user.portfolioAccess, ...JSON.parse(JSON.stringify(this.filteredPortfolioIds))])];
      }
    } else {
      if (this.filteredPortfolioIds.length) {
        this.user.portfolioAccess = this.user.portfolioAccess.filter(x => !this.filteredPortfolioIds.includes(x));
      } else {
        this.user.portfolioAccess = [];
      }
    }

    this.onPortfolioChange();
    !allPortfolioToggle && !isSelect ? this.setPortfolioAssignment() : null;
  }

  selectUnselectAllPortfolioAssignment(allPortfolioAssignmentToggle = false, isSelect = false) {
    if (isSelect) {
      if (allPortfolioAssignmentToggle || !this.filteredPortfolioAssignmentIds.length) {
        this.user.assignment = [];
        for (const i of this.portfolioAssignmentData) {
          this.user.assignment.push(i.id);
        }
      } else {
        this.user.assignment = [...new Set([...this.user.assignment, ...JSON.parse(JSON.stringify(this.filteredPortfolioAssignmentIds))])];
      }
    } else {
      if (this.filteredPortfolioAssignmentIds.length) {
        this.user.assignment = this.user.assignment.filter(x => !this.filteredPortfolioAssignmentIds.includes(x));
      } else {
        this.user.assignment = [];
      }
    }
  }

  selectUnselectAllSite(allSiteToggle = false, isSelect = false) {
    if (isSelect) {
      if (allSiteToggle || !this.filteredSiteIds.length) {
        this.user.siteAccess = [];
        for (const i of this.siteList) {
          this.user.siteAccess.push(i.id);
        }
      } else {
        this.user.siteAccess = [...new Set([...this.user.siteAccess, ...JSON.parse(JSON.stringify(this.filteredSiteIds))])];
      }
    } else {
      if (this.filteredSiteIds.length) {
        this.user.siteAccess = this.user.siteAccess.filter(x => !this.filteredSiteIds.includes(x));
      } else {
        this.user.siteAccess = [];
      }
    }
  }

  resetPortfolioAssignmentDropdown(): void {
    this.setPortfolioAssignmentDropdown();
    this.user.assignment = [];
    this.filteredPortfolioAssignmentIds = [];
  }

  setPortfolioAssignmentDropdown(): void {
    this.portfolioAssignmentData =
      this.portfolioData && this.portfolioData.length
        ? this.portfolioData.filter((item: Dropdown) => this.user?.portfolioAccess?.includes(item.id))
        : [];
  }

  onFilter(event: any, forField: string) {
    if (event.term) {
      this[forField] = event.items?.map(element => element.id);
    } else {
      this[forField] = [];
    }
  }

  // Destroy
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
