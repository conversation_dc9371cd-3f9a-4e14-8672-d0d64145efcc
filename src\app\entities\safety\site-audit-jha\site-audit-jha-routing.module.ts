import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModeWiseGuard } from '../../../@shared/services/mode-wise.guard';
import { JhaAddEditComponent } from '../jha/jha-add-edit/jha-add-edit.component';
import { SiteAuditJhaListingComponent } from './site-audit-jha-listing/site-audit-jha-listing.component';
import { JHAAddEditOpenFromSource } from '../jha/jha-add-edit/jha-add-edit.model';
import { ROLE_TYPE } from '../../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: SiteAuditJhaListingComponent,
    data: { pageTitle: 'Site Audit JHA' }
  },
  {
    path: 'upload',
    component: JhaAddEditComponent,
    data: { pageTitle: 'Upload Site Audit JHA', jhaAddEditSource: JHAAddEditOpenFromSource.SITE_AUDIT_JHA }
  },
  {
    path: 'upload/:mode/:id',
    component: JhaAddEditComponent,
    canActivate: [ModeWiseGuard],
    data: {
      edit: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      add: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Update Site Audit JHA',
      jhaAddEditSource: JHAAddEditOpenFromSource.SITE_AUDIT_JHA
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SiteAuditJhaRoutingModule {}
