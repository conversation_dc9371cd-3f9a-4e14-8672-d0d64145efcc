import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { FilterDetails } from '../../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../../@shared/constants';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { CustomFormService } from '../custom-form.service';
import { AnalyticsListingData, AnalyticsListingResponse } from '../custom-forms.model';
import { AddEditFormTagsComponent } from './add-edit-form-tags/add-edit-form-tags.component';
import { ROLE_TYPE } from '../../../../@shared/enums';
import { checkAuthorisations } from '../../../../@shared/utils';

@Component({
  selector: 'sfl-form-analytics',
  templateUrl: './form-analytics.component.html',
  styleUrls: ['./form-analytics.component.scss']
})
export class FormAnalyticsComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  analyticsListingData: AnalyticsListingData[] = [];
  loading = false;
  filterDetails: FilterDetails = new FilterDetails();
  isFilterDisplay = false;
  filterModel: CommonFilter = new CommonFilter();
  dateFormat = AppConstants.fullDateFormat;
  pageSize = AppConstants.rowsPerPage;
  totalCount: number = 0;
  currentPage = 1;
  sortOptionList = {
    tagName: 'asc',
    controlTypeName: 'asc',
    controlDateTypeName: 'asc'
  };
  modalRef: BsModalRef;
  viewPage = 'customFormListingPage';
  viewFilterSection = 'customFormListingFilterSection';
  userRole: string;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    public readonly customFormService: CustomFormService
  ) {}

  ngOnInit(): void {
    this.userRole = this.storageService.get('user').authorities[0];
    const filter = this.storageService.get(this.viewPage),
      filterSection = this.storageService.get(this.viewFilterSection);
    this.initFilterDetails();

    this.filterModel.itemsCount = 100;
    this.filterModel.sortBy = 'updatedDate';
    this.isFilterDisplay = filterSection;

    if (filter) {
      this.filterModel = filter;

      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
    }
    const operationCustomAnalyticsFilterKeys = ['search', 'controlDataTypeIds', 'controlTypeIds'];
    if (this.storageService.shouldCallListApi(filter, {}, {}, {}, operationCustomAnalyticsFilterKeys)) {
      this.getFormsAnalyticsList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.SEARCH_BOX.show = true;
    filterItem.CONTROL_TYPE_IDS.show = true;
    filterItem.CONTROL_DATA_TYPE_IDS.show = true;
    this.filterDetails.default_sort = 'updatedDate';
    this.filterDetails.filter_item = filterItem;
  }

  getFormsAnalyticsList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;

    if (filterParams) {
      this.filterModel = filterParams;
    }

    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.subscription.add(
      this.customFormService.getAnalyticsListing(this.filterModel).subscribe({
        next: (data: AnalyticsListingResponse) => {
          this.analyticsListingData = data.listOfDataAnalyticsTag;
          this.totalCount = data.total;
          this.loading = false;
        },
        error: () => (this.loading = false)
      })
    );
  }

  changeAnalyticsStatus(analyticsId: number) {
    this.loading = true;

    this.customFormService.changeFormAnalyticsStatus(analyticsId).subscribe({
      next: response => {
        this.alertService.showSuccessToast(response.message);
        this.loading = false;
      },
      error: () => {
        const index = this.analyticsListingData.findIndex(form => form.qestDataAnalyticsId === analyticsId);

        this.analyticsListingData[index].isActive = !this.analyticsListingData[index].isActive;
        this.loading = false;
      }
    });
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }

    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;

    this.getFormsAnalyticsList();
  }

  openCreateEditModal(isEdit, editSlug) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        isEdit,
        editSlug
      }
    };

    this.modalRef = this.modalService.show(AddEditFormTagsComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe({
      next: response => {
        if (response) {
          this.getFormsAnalyticsList();
        }
      }
    });
  }
  onAnalyticsDelete(analyticsId: any) {
    if (analyticsId) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this tag?' }
      };

      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

      this.modalRef.content.onClose.subscribe({
        next: response => {
          if (response) {
            this.subscription.add(
              this.customFormService.deleteFormAnalytics(analyticsId).subscribe({
                next: res => {
                  if (res) {
                    if (this.currentPage !== 0 && this.analyticsListingData.length === 1) {
                      this.onChangeSize();
                    } else {
                      this.getFormsAnalyticsList();
                      this.alertService.showSuccessToast(res.message);
                    }
                  }
                },
                error: () => (this.loading = false)
              })
            );
          }
        },
        error: () => (this.loading = false)
      });
    }
  }

  onChangeSize() {
    this.currentPage = 0;
    this.filterModel.page = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getFormsAnalyticsList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getFormsAnalyticsList();
  }

  refreshList(filterParams: CommonFilter) {
    this.getFormsAnalyticsList(true, filterParams);
  }

  goToLandingPage() {
    this.router.navigateByUrl('/entities/operations/custom-forms');
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    this.modalRef?.hide();
  }
}
