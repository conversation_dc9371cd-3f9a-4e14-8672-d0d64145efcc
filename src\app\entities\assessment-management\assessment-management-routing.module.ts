import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { AssessmentAddEditComponent } from './assessment-add-edit/assessment-add-edit.component';
import { AssessmentListingComponent } from './assessment-listing/assessment-listing.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: AssessmentListingComponent,
    data: { pageTitle: 'Assessment Management' }
  },
  {
    path: 'add',
    component: AssessmentAddEditComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Add Assessment Management'
    }
  },
  {
    path: 'edit/:id',
    component: AssessmentAddEditComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Update Assessment Management'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AssessmentManagementRoutingModule {}
