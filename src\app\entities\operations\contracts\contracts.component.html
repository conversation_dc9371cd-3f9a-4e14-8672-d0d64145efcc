<nb-card class="dashboardSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <h6>Contracts</h6>
  </nb-card-header>
  <nb-card-body id="page-top" class="dropdownOverlap">
    <div id="wrapper">
      <div id="content-wrapper" class="d-flex flex-column">
        <div id="content">
          <div class="container-fluid px-0">
            <div class="row mb-3 align-items-center">
              <div class="col-sm-6 col-md-3 col-lg-3 col-xl-2 pe-sm-0 mb-2">
                <label class="label" for="customer">Select a Customer</label>
                <ng-select
                  id="customer-single-drop-down"
                  class="sfl-track-dropdown"
                  name="customer"
                  [items]="customerList"
                  bindLabel="name"
                  bindValue="id"
                  [(ngModel)]="selectedCustomer"
                  (change)="customerChanged($event)"
                  notFoundText="No Customer Found"
                  placeholder="Select Customer"
                  appendTo="body"
                >
                </ng-select>
              </div>
              <div class="col-sm-6 col-md-3 col-lg-3 col-xl-2 pe-sm-0 mb-2">
                <label class="label" for="customer">Contract Status</label>
                <ng-select
                  id="contract-status-drop-down"
                  class="sfl-track-dropdown"
                  name="ContractStatus"
                  [multiple]="true"
                  [items]="contractStatusFilterList"
                  bindLabel="name"
                  bindValue="id"
                  [(ngModel)]="selectedStatusFilter"
                  (change)="getContractByCustomers()"
                  notFoundText="No Status Found"
                  placeholder="Select Status"
                  [closeOnSelect]="false"
                  appendTo="body"
                  [disabled]="!selectedCustomer"
                >
                  <ng-template ng-header-tmp *ngIf="contractStatusFilterList && contractStatusFilterList.length">
                    <button class="btn btn-sm btn-primary" (click)="selectAllStatus(true)">Select all</button>
                    <button class="btn btn-sm btn-primary ms-1" (click)="selectAllStatus(false)">Unselect all</button>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                  </ng-template>
                  <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                    <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                      <span
                        class="ng-value-label text-truncate"
                        [ngClass]="{
                          'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                          'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                          'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                        }"
                        >{{ item.name }}</span
                      >
                      <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                    </div>
                    <div class="ng-value" *ngIf="items.length > 1">
                      <span class="ng-value-label">+{{ items.length - 1 }} </span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
              <div class="col-sm-12 col-md-6 col-lg-6 col-xl-8 text-end">
                <button
                  nbButton
                  status="primary"
                  size="medium"
                  type="button"
                  id="siteSubmit"
                  *ngIf="selectedCustomer"
                  [routerLink]="['../add/']"
                  class="float-end m-1"
                >
                  Add New Contract
                </button>
                <!-- <button nbButton class="linear-mode-button ms-2 button-h-100" type="button" [routerLink]="['../add/']">Add Contract</button> -->
              </div>
            </div>
            <!-- contract main accordion starts -->
            <div class="row mb-3" *ngIf="selectedCustomer">
              <!-- Contract listing -->
              <div class="col-12">
                <ng-container *ngIf="!contractsDTO.length && !loading">
                  <h6>No Contracts available for selected customer.</h6>
                </ng-container>
                <nb-accordion
                  class="mb-3"
                  *ngFor="let contract of contractsDTO; let i = index; let first = first; trackBy: trackByFuncContract"
                  [multi]="false"
                >
                  <nb-accordion-item [expanded]="contract.expanded" [id]="'contract' + contract.contract.id">
                    <nb-accordion-item-header class="bg-light" [ngClass]="{ 'regen-button': contract.contract.recalculateRequired }">
                      <div class="col-md-9 col-sm-9">
                        <h6>{{ contract.contract.contractName }} ({{ contract.contract.siteCount ?? 0 }})</h6>
                      </div>
                      <div class="col-md-3 col-sm-3 d-flex justify-content-end align-items-center">
                        <div class="me-3 d-flex align-items-center">
                          <a
                            class="listgrid-icon me-3"
                            (click)="
                              $event.stopPropagation(); $event.preventDefault(); openContractLinkReport(contract.contract.contractLinkUrl)
                            "
                          >
                            <em class="fas fa-file-alt"></em>
                          </a>

                          <label class="label mb-0 me-2" for="active-toggle">
                            {{ contract.contract.isActive ? 'Active' : 'In-active' }}
                          </label>
                          <nb-toggle
                            id="active-toggle"
                            name="active"
                            [(checked)]="contract.contract.isActive"
                            (click)="
                              $event.stopPropagation(); $event.preventDefault(); makeContractActiveInactive($event, contract.contract)
                            "
                            class="sfl-track-toggle"
                            status="primary"
                          ></nb-toggle>
                        </div>
                        <a
                          class="listgrid-icon me-3"
                          (click)="$event.stopPropagation(); $event.preventDefault(); updateContract(contract.contract.id)"
                        >
                          <em class="fa fa-edit" nbTooltip="Edit contract" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                        </a>
                      </div>
                    </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div class="import-export-section mb-3">
                        <button
                          nbButton
                          status="primary"
                          size="medium"
                          type="button"
                          id="exportContract"
                          class="me-3 mb-3"
                          (click)="exportContract(contract.contract)"
                        >
                          Export
                        </button>
                        <span ngFileDragDrop (fileDropped)="importContractFile($event)">
                          <button
                            nbButton
                            status="primary"
                            size="medium"
                            type="button"
                            id="importContract"
                            class="me-3 mb-3"
                            (click)="clickImport()"
                          >
                            Import
                          </button>
                          <input
                            type="file"
                            #importContract
                            class="d-none"
                            accept=".xlsx"
                            (change)="importContractFile($event.target.files, contract.contract)"
                          />
                        </span>
                        <button
                          nbButton
                          status="primary"
                          size="medium"
                          type="button"
                          id="siteSubmit"
                          class="me-3 mb-3 primary"
                          [ngClass]="{ 'regen-button': contract.contract.recalculateRequired }"
                          (click)="$event.stopPropagation(); $event.preventDefault(); reCalculateRates(contract.contract)"
                          [tooltipDisabled]="!contract.contract.recalculateRequired"
                          pTooltip="The contract rates has been changed, the calculations need to be re-generated!"
                          tooltipPosition="top"
                        >
                          Re-generate all rates
                        </button>
                      </div>
                      <div class="table-responsive table-card-view">
                        <table class="table two-header table-bordered table-header-rotated">
                          <thead>
                            <tr>
                              <th scope="col" class="text-start" id="empty" colspan="6"></th>
                              <th scope="col" class="text-start" id="material" colspan="4">
                                <div class="d-flex align-items-center">
                                  <span class="me-2">Material</span>
                                </div>
                              </th>
                            </tr>
                            <tr>
                              <th scope="col" class="text-start" id="startdate">
                                <div class="d-flex align-items-center"><span class="me-2">Start Date</span></div>
                              </th>
                              <th scope="col" class="text-start" id="enddate">
                                <div class="d-flex align-items-center"><span class="me-2">End Date</span></div>
                              </th>
                              <th scope="col" class="text-start" id="escalationrate">
                                <div class="d-flex align-items-center"><span class="me-2">Escalation Rate</span></div>
                              </th>
                              <th scope="col" class="text-start" id="overtimerate">
                                <div class="d-flex align-items-center"><span class="me-2">Overtime Rate</span></div>
                              </th>
                              <th scope="col" class="text-start" id="weekendrate">
                                <div class="d-flex align-items-center"><span class="me-2">Weekend Rate</span></div>
                              </th>
                              <th scope="col" class="text-start" id="holidayrate">
                                <div class="d-flex align-items-center"><span class="me-2">Holiday Rate</span></div>
                              </th>
                              <th scope="col" class="text-start" id="rate1">
                                <div class="d-flex align-items-center">
                                  <span class="me-2">Rate 1</span>
                                </div>
                              </th>
                              <th scope="col" class="text-start" id="rate2">
                                <div class="d-flex align-items-center"><span class="me-2">Rate2</span></div>
                              </th>
                              <th scope="col" class="text-start" id="threshold">
                                <div class="d-flex align-items-center"><span class="me-2">Threshold</span></div>
                              </th>
                              <th scope="col" class="text-start" id="dispatchminhours">
                                <div class="d-flex align-items-center"><span class="me-2">Dispatch Minimum Hours </span></div>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <!-- <tr>
                              <td class="text-center" colspan="8">No Reschedule Records Found</td>
                            </tr> -->
                            <tr>
                              <td data-title="Start Date" class="text-start">
                                {{ contract.contract.startDate | date : 'MM/dd/yyyy' }}
                              </td>
                              <td data-title="End Date" class="text-start">
                                {{ contract.contract.endDate | date : 'MM/dd/yyyy' }}
                              </td>
                              <td data-title="Escalation Rate" class="text-start">
                                {{ contract.contract.escalationRate ? (contract.contract.escalationRate | number : '1.2-2') + '%' : '-' }}
                              </td>
                              <td data-title="Overtime Rate" class="text-start">
                                {{ contract.contract.overtimeRate ? (contract.contract.overtimeRate | number : '1.2-2') + '%' : '-' }}
                              </td>
                              <td data-title="Weekend Rate" class="text-start">
                                {{ contract.contract.weekendRate ? (contract.contract.weekendRate | number : '1.2-2') + '%' : '-' }}
                              </td>
                              <td data-title="Holiday Rate" class="text-start">
                                {{ contract.contract.holidayRate ? (contract.contract.holidayRate | number : '1.2-2') + '%' : '-' }}
                              </td>
                              <td data-title="Rate1" class="text-start">
                                {{ contract.contract.materialRate1 ? (contract.contract.materialRate1 | number : '1.2-2') + '%' : '-' }}
                              </td>
                              <td data-title="Rate2" class="text-start">
                                {{ contract.contract.materialRate2 ? (contract.contract.materialRate2 | number : '1.2-2') + '%' : '-' }}
                              </td>
                              <td data-title="Threshold" class="text-start">
                                {{ contract.contract.materialThreshold ? (contract.contract.materialThreshold | currency) : '-' }}
                              </td>
                              <td data-title="Dispatch Minimum Hours" class="text-start">
                                {{
                                  contract.contract.dispatchMinimumHours
                                    ? (contract.contract.dispatchMinimumHours | number : '1.2-2') + 'h'
                                    : '-'
                                }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <h6>Base Rates</h6>
                      <div class="table-responsive table-card-view">
                        <table class="table table-bordered table-header-rotated">
                          <thead>
                            <tr>
                              <th scope="col" class="text-start" id="technician">
                                <div class="d-flex align-items-center"><span class="me-2">Technician</span></div>
                              </th>
                              <th scope="col" class="text-start" id="electrician">
                                <div class="d-flex align-items-center"><span class="me-2">Electrician</span></div>
                              </th>
                              <th scope="col" class="text-start" id="mvtechnician">
                                <div class="d-flex align-items-center"><span class="me-2">MV Technician</span></div>
                              </th>
                              <th scope="col" class="text-start" id="besstechnician">
                                <div class="d-flex align-items-center"><span class="me-2">BESS Technician </span></div>
                              </th>
                              <th scope="col" class="text-start" id="administrative">
                                <div class="d-flex align-items-center"><span class="me-2">Administrative</span></div>
                              </th>
                              <th scope="col" class="text-start" id="engineering">
                                <div class="d-flex align-items-center"><span class="me-2">Engineering</span></div>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <!-- <tr>
                              <td class="text-center" colspan="8">No Reschedule Records Found</td>
                            </tr> -->
                            <tr>
                              <td data-title="Technician" class="text-start">
                                {{ contract.contract.baseRates.technicianRate | currency }}
                              </td>
                              <td data-title="Electrician" class="text-start">
                                {{ contract.contract.baseRates.electricianRate | currency }}
                              </td>
                              <td data-title="MV Technician" class="text-start">
                                {{ contract.contract.baseRates.mvTechnicianRate | currency }}
                              </td>
                              <td data-title="BESS Technician" class="text-start">
                                {{ contract.contract.baseRates.bessTechnicianRate | currency }}
                              </td>
                              <td data-title="Administrative" class="text-start">
                                {{ contract.contract.baseRates.administrativeRate | currency }}
                              </td>
                              <td data-title="Engineering" class="text-start">
                                {{ contract.contract.baseRates.engineeringRate | currency }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <nb-accordion class="mb-3">
                        <nb-accordion-item [expanded]="true">
                          <nb-accordion-item-header class="bg-light">
                            <div class="col-md-12">
                              <h6>Rates</h6>
                            </div>
                            <!-- <div class="col-md-6">
                              <div class="float-end d-flex">
                                <button
                                  nbButton
                                  status="primary"
                                  size="medium"
                                  type="button"
                                  id="siteSubmit"
                                  class="me-3 primary"
                                  [ngClass]="{ 'regen-button': contract.contract.recalculateRequired }"
                                  (click)="$event.stopPropagation(); $event.preventDefault(); reCalculateRates(contract.contract)"
                                  [tooltipDisabled]="!contract.contract.recalculateRequired"
                                  pTooltip="The contract rates has been changed, the calculations need to be re-generated!"
                                  tooltipPosition="top"
                                >
                                  Re-generate all rates
                                </button>
                              </div>
                            </div> -->
                          </nb-accordion-item-header>
                          <nb-accordion-item-body>
                            <h6 class="text-center">Rates by month</h6>
                            <div class="row mb-3">
                              <div class="col-md-2">
                                <ng-select
                                  id="rates-month-year-drop-down"
                                  class="sfl-track-dropdown"
                                  name="rates-month-year"
                                  bindValue="key"
                                  bindLabel="name"
                                  [items]="contract.contract.numberOfTotalContractYears"
                                  [(ngModel)]="contract.contract.selectedYear"
                                  notFoundText="No Year Found"
                                  placeholder="Select a Year"
                                  appendTo="body"
                                  [clearable]="false"
                                >
                                </ng-select>
                              </div>
                              <div class="col-md-2">
                                <ng-select
                                  id="rates-month-drop-down"
                                  class="sfl-track-dropdown"
                                  name="rates-month"
                                  bindValue="key"
                                  bindLabel="name"
                                  [items]="contractMonths"
                                  [(ngModel)]="contract.contract.selectedMonth"
                                  notFoundText="No Months Found"
                                  placeholder="Select a Month"
                                  appendTo="body"
                                  [clearable]="false"
                                >
                                </ng-select>
                              </div>
                            </div>
                            <div class="table-responsive table-card-view">
                              <table class="table table-bordered table-header-rotated">
                                <thead>
                                  <tr>
                                    <th></th>
                                    <th scope="col" class="text-start" id="technician">
                                      <div class="d-flex align-items-center"><span class="me-2">Technician</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="electrician">
                                      <div class="d-flex align-items-center"><span class="me-2">Electrician</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="mvtechnician">
                                      <div class="d-flex align-items-center"><span class="me-2">MV Technician</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="besstechnician">
                                      <div class="d-flex align-items-center"><span class="me-2">BESS Technician </span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="administrative">
                                      <div class="d-flex align-items-center"><span class="me-2">Administrative</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="engineering">
                                      <div class="d-flex align-items-center"><span class="me-2">Engineering</span></div>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <ng-container *ngFor="let rates of contract.ratesByMonth">
                                    <ng-container *ngIf="contract.contract.selectedYear === rates.year">
                                      <ng-container *ngFor="let ratesByMonth of rates.months">
                                        <ng-container *ngIf="contract.contract.selectedMonth === ratesByMonth.month">
                                          <tr>
                                            <!-- Second column -->
                                            <td></td>
                                            <ng-container *ngIf="contract.contract.selectedMonth === ratesByMonth.month">
                                              <ng-container *ngFor="let role of roleIdOrder">
                                                <td data-title="{{ role.name }}">
                                                  {{ getRateByRoleId(ratesByMonth.roles, role.id) | currency : 'USD' }}
                                                </td>
                                              </ng-container>
                                            </ng-container>
                                          </tr>
                                          <tr>
                                            <!-- Third column -->
                                            <td>Overtime Rate</td>
                                            <ng-container *ngIf="contract.contract.selectedMonth === ratesByMonth.month">
                                              <ng-container *ngFor="let role of roleIdOrder">
                                                <td data-title="{{ role.name }}">
                                                  {{ getOvertimeByRoleId(ratesByMonth.roles, role.id) | currency : 'USD' }}
                                                </td>
                                              </ng-container>
                                              <!-- <ng-container *ngFor="let item of ratesByMonth?.roles">
                                                <td data-title="{{ item.role }}">
                                                  {{ item.overtimeRate | currency }}
                                                </td>
                                              </ng-container> -->
                                            </ng-container>
                                          </tr>
                                          <tr>
                                            <!-- Fourth column -->
                                            <td>Weekend Rate</td>
                                            <ng-container *ngIf="contract.contract.selectedMonth === ratesByMonth.month">
                                              <ng-container *ngFor="let role of roleIdOrder">
                                                <td data-title="{{ role.name }}">
                                                  {{ getWeekendRateByRoleId(ratesByMonth.roles, role.id) | currency : 'USD' }}
                                                </td>
                                              </ng-container>
                                            </ng-container>
                                          </tr>
                                          <tr>
                                            <!-- Fifth column -->
                                            <td>Holiday Rate</td>
                                            <ng-container *ngIf="contract.contract.selectedMonth === ratesByMonth.month">
                                              <ng-container *ngFor="let role of roleIdOrder">
                                                <td data-title="{{ role.name }}">
                                                  {{ getHolidayRateByRoleId(ratesByMonth.roles, role.id) | currency : 'USD' }}
                                                </td>
                                              </ng-container>
                                            </ng-container>
                                          </tr>
                                        </ng-container>
                                        <ng-template #noRecords>
                                          <!-- <tr>
                                            <td>No records!</td>
                                          </tr> -->
                                        </ng-template>
                                      </ng-container>
                                    </ng-container>
                                  </ng-container>
                                </tbody>
                              </table>
                            </div>

                            <h6 class="text-center">Contract Months Rates by Type</h6>
                            <div>
                              <div class="col-8 col-sm-6 col-md-3 col-lg-3 col-xl-3 pe-sm-0 mb-2">
                                <label class="label" for="customer">Select a Type</label>
                                <ng-select
                                  id="type-drop-down"
                                  class="sfl-track-dropdown"
                                  name="type"
                                  bindValue="key"
                                  bindLabel="name"
                                  [items]="types"
                                  [(ngModel)]="selectedType"
                                  notFoundText="No Types Found"
                                  placeholder="Select a Type"
                                  appendTo="body"
                                  [clearable]="false"
                                >
                                </ng-select>
                              </div>
                            </div>
                            <div class="fade-contract-rates site-contract-mapping" *ngIf="selectedType">
                              <table class="table table-bordered table-header-rotated">
                                <thead>
                                  <tr>
                                    <th></th>
                                    <th scope="col" class="text-start" id="january">
                                      <div class="d-flex align-items-center"><span class="me-2">January</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="february">
                                      <div class="d-flex align-items-center"><span class="me-2">February</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="march">
                                      <div class="d-flex align-items-center"><span class="me-2">March</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="april">
                                      <div class="d-flex align-items-center"><span class="me-2">April</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="may">
                                      <div class="d-flex align-items-center"><span class="me-2">May</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="june">
                                      <div class="d-flex align-items-center"><span class="me-2">June</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="july">
                                      <div class="d-flex align-items-center"><span class="me-2">July</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="august">
                                      <div class="d-flex align-items-center"><span class="me-2">August</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="september">
                                      <div class="d-flex align-items-center"><span class="me-2">September</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="october">
                                      <div class="d-flex align-items-center"><span class="me-2">October</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="november">
                                      <div class="d-flex align-items-center"><span class="me-2">November</span></div>
                                    </th>
                                    <th scope="col" class="text-start" id="december">
                                      <div class="d-flex align-items-center"><span class="me-2">December</span></div>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody class="months">
                                  <ng-container *ngFor="let monthRates of contract.monthsRatesByType">
                                    <ng-container *ngIf="monthRates.type === selectedType">
                                      <tr *ngFor="let month of monthRates.data">
                                        <td class="year">{{ month.year }}</td>
                                        <td class="jan" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 1 }">
                                          {{ month.months.january ? (month.months.january | currency) : '-' }}
                                        </td>
                                        <td class="feb" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 2 }">
                                          {{ month.months.february ? (month.months.february | currency) : '-' }}
                                        </td>
                                        <td class="march" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 3 }">
                                          {{ month.months.march ? (month.months.march | currency) : '-' }}
                                        </td>
                                        <td class="apr" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 4 }">
                                          {{ month.months.april ? (month.months.april | currency) : '-' }}
                                        </td>
                                        <td class="may" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 5 }">
                                          {{ month.months.may ? (month.months.may | currency) : '-' }}
                                        </td>
                                        <td class="jun" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 6 }">
                                          {{ month.months.june ? (month.months.june | currency) : '-' }}
                                        </td>
                                        <td class="jul" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 7 }">
                                          {{ month.months.july ? (month.months.july | currency) : '-' }}
                                        </td>
                                        <td class="aug" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 8 }">
                                          {{ month.months.august ? (month.months.august | currency) : '-' }}
                                        </td>
                                        <td class="sep" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 9 }">
                                          {{ month.months.september ? (month.months.september | currency) : '-' }}
                                        </td>
                                        <td class="oct" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 10 }">
                                          {{ month.months.october ? (month.months.october | currency) : '-' }}
                                        </td>
                                        <td class="nov" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 11 }">
                                          {{ month.months.november ? (month.months.november | currency) : '-' }}
                                        </td>
                                        <td class="dec" [ngClass]="{ 'year-border': contract.startingMonthOfTheContract === 12 }">
                                          {{ month.months.december ? (month.months.december | currency) : '-' }}
                                        </td>
                                      </tr>
                                    </ng-container>
                                  </ng-container>
                                </tbody>
                              </table>
                            </div>

                            <!-- History Accordion -->
                            <div class="row g-0">
                              <nb-accordion class="mb-2">
                                <nb-accordion-item class="border-bottom" (click)="onChangeHistoryAccordian($event, contract, i)">
                                  <nb-accordion-item-header class="accordion_head">
                                    <strong>History</strong>
                                  </nb-accordion-item-header>
                                  <nb-accordion-item-body>
                                    <div *ngIf="contract.contractHistories?.length; else noHistory">
                                      <nb-accordion class="mb-2" *ngFor="let item of contract.contractHistories">
                                        <nb-accordion-item
                                          class="border-bottom"
                                          (click)="$event.stopPropagation(); $event.preventDefault()"
                                        >
                                          <nb-accordion-item-header class="accordion_head">
                                            <strong>
                                              <strong>{{ item?.userName }}</strong>
                                              <div class="label ms-2">{{ item?.action }}</div>
                                              <span class="label ms-2"> - </span>
                                              <span class="label ms-2">{{ item?.logDate }}</span>
                                            </strong>
                                          </nb-accordion-item-header>
                                          <nb-accordion-item-body>
                                            <div class="mt-2">
                                              <div class="row">
                                                <div class="col-2"><label class="label mb-0">Field</label></div>
                                                <div class="col-5 text-center"><label class="label mb-0">Original Value</label></div>
                                                <div class="col-5 text-center"><label class="label mb-0">New Value</label></div>
                                              </div>
                                              <div class="mt-2" *ngFor="let actionSummary of item?.auditLogDetails">
                                                <div class="row">
                                                  <div class="col-2">{{ actionSummary?.fieldName }}</div>
                                                  <div class="col-5 text-center">{{ actionSummary?.oldValue || '-' }}</div>
                                                  <div class="col-5 text-center">{{ actionSummary?.newValue || '-' }}</div>
                                                </div>
                                              </div>
                                            </div>
                                          </nb-accordion-item-body>
                                          <div *ngIf="!contract.contractHistories.length" class="text-center">
                                            <label>No History Data Found</label>
                                          </div>
                                        </nb-accordion-item>
                                      </nb-accordion>
                                    </div>
                                    <ng-template #noHistory>
                                      <ng-container *ngIf="!loading">
                                        <h6>No contract history available to show.</h6>
                                      </ng-container>
                                    </ng-template>
                                  </nb-accordion-item-body>
                                </nb-accordion-item>
                              </nb-accordion>
                            </div>
                          </nb-accordion-item-body>
                        </nb-accordion-item>
                      </nb-accordion>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>

              <!-- Site listing with contract details -->
              <div *ngIf="contractsDTO.length">
                <form
                  name="contractSiteForm"
                  #contractSiteForm="ngForm"
                  aria-labelledby="title"
                  autocomplete="off"
                  (ngSubmit)="contractSiteForm?.form?.valid && saveSiteContractMapping()"
                >
                  <div class="row">
                    <div class="col-12 site-mapping">
                      <h6 class="text-center">Site & Contract Mapping</h6>
                    </div>
                  </div>
                  <nb-card class="mb-3">
                    <nb-card-body id="site-mapping-filter-card" class="dropdownOverlap">
                      <div class="row site-mapping-filter">
                        <div class="col-md-2">
                          <ng-select
                            id="portfolio-filter-drop-down"
                            class="sfl-track-dropdown me-3 w-100 mb-3 mb-md-0"
                            name="portfoliocontract"
                            bindValue="id"
                            bindLabel="name"
                            [items]="siteContractFilterValues?.portfolios"
                            [(ngModel)]="filterModel.portfolioIds"
                            (change)="filterChange()"
                            notFoundText="No Portfolio Found"
                            placeholder="Portfolio"
                            appendTo="body"
                            [multiple]="true"
                          >
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                              <input id="item-{{ index }}" type="checkbox" name="{{ item.name }}" [ngModel]="item$.selected" />
                              {{ item.name }}
                            </ng-template>
                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                <span
                                  class="ng-value-label text-truncate"
                                  [ngClass]="{
                                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                  }"
                                  >{{ item.name }}</span
                                >
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                              </div>
                              <div class="ng-value" *ngIf="items.length > 1">
                                <span class="ng-value-label">+{{ items.length - 1 }} </span>
                              </div>
                            </ng-template>
                          </ng-select>
                        </div>
                        <div class="col-md-2">
                          <ng-select
                            id="region-filter-drop-down"
                            class="sfl-track-dropdown me-3 w-100 mb-3 mb-md-0"
                            name="regioncontract"
                            bindValue="id"
                            bindLabel="name"
                            [items]="siteContractFilterValues?.regions"
                            [(ngModel)]="filterModel.regionIds"
                            (change)="filterChange()"
                            notFoundText="No Region Found"
                            placeholder="Region"
                            appendTo="body"
                            [multiple]="true"
                          >
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                              <input id="item-{{ index }}" type="checkbox" name="{{ item.name }}" [ngModel]="item$.selected" />
                              {{ item.name }}
                            </ng-template>
                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                <span
                                  class="ng-value-label text-truncate"
                                  [ngClass]="{
                                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                  }"
                                  >{{ item.name }}</span
                                >
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                              </div>
                              <div class="ng-value" *ngIf="items.length > 1">
                                <span class="ng-value-label">+{{ items.length - 1 }} </span>
                              </div>
                            </ng-template>
                          </ng-select>
                        </div>
                        <div class="col-md-2">
                          <ng-select
                            id="subregion-filter-drop-down"
                            class="sfl-track-dropdown me-3 w-100 mb-3 mb-md-0"
                            name="subregioncontract"
                            bindValue="id"
                            bindLabel="name"
                            [items]="siteContractFilterValues?.subregions"
                            [(ngModel)]="filterModel.subregionIds"
                            (change)="filterChange()"
                            notFoundText="No Sub Region Found"
                            placeholder="Sub Region"
                            appendTo="body"
                            [multiple]="true"
                          >
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                              <input id="item-{{ index }}" type="checkbox" name="{{ item.name }}" [ngModel]="item$.selected" />
                              {{ item.name }}
                            </ng-template>
                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                <span
                                  class="ng-value-label text-truncate"
                                  [ngClass]="{
                                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                  }"
                                  >{{ item.name }}</span
                                >
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                              </div>
                              <div class="ng-value" *ngIf="items.length > 1">
                                <span class="ng-value-label">+{{ items.length - 1 }} </span>
                              </div>
                            </ng-template>
                          </ng-select>
                        </div>
                        <div class="col-md-2">
                          <ng-select
                            id="state-filter-drop-down"
                            class="sfl-track-dropdown me-3 w-100 mb-3 mb-md-0"
                            name="statecontract"
                            bindValue="name"
                            bindLabel="name"
                            [items]="siteContractFilterValues?.states"
                            [(ngModel)]="filterModel.stateNames"
                            (change)="filterChange()"
                            notFoundText="No State Found"
                            placeholder="State"
                            appendTo="body"
                            [multiple]="true"
                          >
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                              <input id="item-{{ index }}" type="checkbox" name="{{ item.name }}" [ngModel]="item$.selected" />
                              {{ item.name }}
                            </ng-template>
                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                <span
                                  class="ng-value-label text-truncate"
                                  [ngClass]="{
                                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                  }"
                                  >{{ item.name }}</span
                                >
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                              </div>
                              <div class="ng-value" *ngIf="items.length > 1">
                                <span class="ng-value-label">+{{ items.length - 1 }} </span>
                              </div>
                            </ng-template>
                          </ng-select>
                        </div>
                        <div class="col-md-2">
                          <ng-select
                            id="site-filter-drop-down"
                            class="sfl-track-dropdown me-3 w-100 mb-3 mb-md-0"
                            name="sitefiltercontract"
                            bindValue="id"
                            bindLabel="name"
                            [items]="siteContractFilterValues?.sites"
                            [(ngModel)]="filterModel.siteIds"
                            (change)="filterChange()"
                            notFoundText="No Site Found"
                            placeholder="Site"
                            appendTo="body"
                            [multiple]="true"
                          >
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                              <input id="item-{{ index }}" type="checkbox" name="{{ item.name }}" [ngModel]="item$.selected" />
                              {{ item.name }}
                            </ng-template>
                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                <span
                                  class="ng-value-label text-truncate"
                                  [ngClass]="{
                                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                  }"
                                  >{{ item.name }}</span
                                >
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                              </div>
                              <div class="ng-value" *ngIf="items.length > 1">
                                <span class="ng-value-label">+{{ items.length - 1 }} </span>
                              </div>
                            </ng-template>
                          </ng-select>
                        </div>
                        <div class="col-md-2">
                          <ng-select
                            id="contract-filter-drop-down"
                            class="sfl-track-dropdown me-3 w-100"
                            name="contractfilter"
                            bindValue="id"
                            bindLabel="name"
                            [items]="siteContractFilterValues?.contracts"
                            [(ngModel)]="filterModel.contractIds"
                            (change)="filterChange()"
                            notFoundText="No Contract Found"
                            placeholder="Contract"
                            appendTo="body"
                            [multiple]="true"
                          >
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                              <input id="item-{{ index }}" type="checkbox" name="{{ item.name }}" [ngModel]="item$.selected" />
                              {{ item.name }}
                            </ng-template>
                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                <span
                                  class="ng-value-label text-truncate"
                                  [ngClass]="{
                                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                  }"
                                  >{{ item.name }}</span
                                >
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                              </div>
                              <div class="ng-value" *ngIf="items.length > 1">
                                <span class="ng-value-label">+{{ items.length - 1 }} </span>
                              </div>
                            </ng-template>
                          </ng-select>
                        </div>
                      </div>
                    </nb-card-body>
                  </nb-card>
                  <div class="row mb-3">
                    <ng-container *ngIf="editSiteMode">
                      <div class="col-md-4 col-sm-4 d-flex justify-content-start align-items-center">
                        <ng-select
                          id="contract-drop-down"
                          class="sfl-track-dropdown me-3"
                          name="selectedContractForAllSite"
                          bindValue="id"
                          bindLabel="name"
                          [items]="customerContracts"
                          [(ngModel)]="selectedContractForAllSite"
                          notFoundText="No Contract Found"
                          placeholder="Bulk Update"
                          appendTo="body"
                        >
                        </ng-select>
                        <button *ngIf="editSiteMode" (click)="applySiteContractChange()" type="button" class="btn btn-sm btn-primary">
                          Apply to All
                        </button>
                      </div>
                    </ng-container>
                    <div [ngClass]="editSiteMode ? 'col-md-8 col-sm-8 d-flex justify-content-end' : 'col-md-12'">
                      <button
                        *ngIf="editSiteMode"
                        [disabled]="selectedAllSite && !selectedContractForAllSite"
                        type="submit"
                        class="btn btn-sm btn-primary me-3"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        *appHasPermission="[roleType.ADMIN, roleType.MANAGER]"
                        (click)="toggleEditMode()"
                        class="btn btn-sm btn-primary float-end"
                      >
                        {{ editSiteMode ? 'Cancel' : 'Edit' }}
                      </button>
                    </div>
                  </div>
                  <div class="col-12 site-contract-mapping" *ngIf="selectedCustomer">
                    <table class="table table-bordered table-header-rotated">
                      <thead>
                        <tr>
                          <th *ngIf="editSiteMode" class="text-center">
                            <nb-checkbox
                              id="select-all"
                              class="sfl-track-checkbox"
                              (change)="selectAllSites()"
                              [checked]="selectedAllSite"
                              name="selectallsites"
                            >
                            </nb-checkbox>
                          </th>
                          <th
                            scope="col"
                            (click)="sort('PortfolioName', sortOptionList['PortfolioName'])"
                            class="text-start cursor-pointer"
                            id="portfolio"
                          >
                            <div class="d-flex align-items-center">
                              <span class="me-2">Portfolio</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['PortfolioName'] === 'desc',
                                  'fa-arrow-down': sortOptionList['PortfolioName'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'PortfolioName'
                                }"
                              ></span>
                            </div>
                          </th>
                          <th
                            scope="col"
                            (click)="sort('RegionName', sortOptionList['RegionName'])"
                            class="text-start cursor-pointer"
                            id="region"
                          >
                            <div class="d-flex align-items-center">
                              <span class="me-2">Region</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['RegionName'] === 'desc',
                                  'fa-arrow-down': sortOptionList['RegionName'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'RegionName'
                                }"
                              ></span>
                            </div>
                          </th>
                          <th
                            scope="col"
                            (click)="sort('SubregionName', sortOptionList['SubregionName'])"
                            class="text-start cursor-pointer"
                            id="subregion"
                          >
                            <div class="d-flex align-items-center">
                              <span class="me-2">Sub-Region</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['SubregionName'] === 'desc',
                                  'fa-arrow-down': sortOptionList['SubregionName'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'SubregionName'
                                }"
                              ></span>
                            </div>
                          </th>
                          <th scope="col" (click)="sort('State', sortOptionList['State'])" class="text-start cursor-pointer" id="state">
                            <div class="d-flex align-items-center">
                              <span class="me-2">State</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['State'] === 'desc',
                                  'fa-arrow-down': sortOptionList['State'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'State'
                                }"
                              ></span>
                            </div>
                          </th>
                          <th
                            scope="col"
                            (click)="sort('SiteName', sortOptionList['SiteName'])"
                            class="text-start cursor-pointer"
                            id="site"
                          >
                            <div class="d-flex align-items-center">
                              <span class="me-2">Site</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['SiteName'] === 'desc',
                                  'fa-arrow-down': sortOptionList['SiteName'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'SiteName'
                                }"
                              ></span>
                            </div>
                          </th>
                          <th
                            scope="col"
                            (click)="sort('SiteActive', sortOptionList['SiteActive'])"
                            class="text-start cursor-pointer"
                            id="site-active-status"
                          >
                            <div class="d-flex align-items-center">
                              <span class="me-2">Site Active Status</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['SiteActive'] === 'desc',
                                  'fa-arrow-down': sortOptionList['SiteActive'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'SiteActive'
                                }"
                              ></span>
                            </div>
                          </th>
                          <th
                            scope="col"
                            (click)="sort('ContractActive', sortOptionList['ContractActive'])"
                            class="text-start cursor-pointer"
                            id="contract-active-status"
                          >
                            <div class="d-flex align-items-center">
                              <span class="me-2">Contract Active Status</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['ContractActive'] === 'desc',
                                  'fa-arrow-down': sortOptionList['ContractActive'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'ContractActive'
                                }"
                              ></span>
                            </div>
                          </th>
                          <th
                            scope="col"
                            (click)="sort('ContractName', sortOptionList['ContractName'])"
                            class="text-start cursor-pointer"
                            id="contract"
                          >
                            <div class="d-flex align-items-center">
                              <span class="me-2">Contract</span>
                              <span
                                class="fa cursor-pointer ms-auto"
                                [ngClass]="{
                                  'fa-arrow-up': sortOptionList['ContractName'] === 'desc',
                                  'fa-arrow-down': sortOptionList['ContractName'] === 'asc',
                                  'icon-selected': filterModel.sortBy === 'ContractName'
                                }"
                              ></span>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let sites of customerContractSites; let i = index; trackBy: trackByFunc">
                          <td *ngIf="editSiteMode" data-title="Action" class="text-center">
                            <nb-checkbox
                              id="select-site"
                              class="sfl-track-checkbox"
                              name="selectsite"
                              (change)="siteCheckboxChanged($event, i)"
                              [checked]="sites.isSelected"
                              [(ngModel)]="sites.isSelected"
                            >
                            </nb-checkbox>
                          </td>
                          <td data-title="Portfolio" class="text-start">
                            {{ sites.portfolioName }}
                          </td>
                          <td data-title="Region" class="text-start">
                            {{ sites.regionName ?? '-' }}
                          </td>
                          <td data-title="Sub-Region" class="text-start">
                            {{ sites.subregionName ?? '-' }}
                          </td>
                          <td data-title="State" class="text-start">
                            {{ sites.state ?? '-' }}
                          </td>
                          <td data-title="Site" class="text-start">
                            {{ sites.siteName }}
                          </td>
                          <td data-title="Site Active Status" class="text-start">
                            {{ sites.isSiteActive ? 'Yes' : 'No' }}
                          </td>
                          <td data-title="Contract Active Status" class="text-start">
                            {{ sites.isContractActive ? 'Yes' : 'No' }}
                          </td>
                          <td
                            *ngIf="!editSiteMode; else editContractMode"
                            data-title="Contract"
                            [ngClass]="sites.contractName ? 'text-start' : 'text-center'"
                          >
                            {{ sites.contractName ?? '-' }}
                          </td>
                          <ng-template #editContractMode>
                            <td data-title="Contract" class="text-center">
                              <ng-select
                                [id]="'sitecontract' + i + sites.contractId"
                                class="sfl-track-dropdown"
                                [name]="'sitecontract' + i + sites.contractId"
                                bindValue="id"
                                bindLabel="name"
                                [items]="customerContracts"
                                [(ngModel)]="sites.contractId"
                                notFoundText="No Contract Found"
                                placeholder="Select a Contract"
                                appendTo="body"
                                (change)="updateSiteSelection(i)"
                                [clearable]="false"
                              >
                              </ng-select>
                            </td>
                          </ng-template>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>

<ng-template #contactViewModal>
  <div class="alert-box">
    <div class="modal-header">
      <div class="top-info">
        <h6>Preview</h6>
      </div>
      <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body" [nbSpinner]="loading" (contextmenu)="$event.preventDefault()">
      <iframe [src]="contractLinkUrl" width="100%" height="600px" style="border: none"></iframe>
    </div>
  </div>
</ng-template>
