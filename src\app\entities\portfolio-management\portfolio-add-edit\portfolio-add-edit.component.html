<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex align-items-center">
      <h6>{{ getComponentTitle() }}</h6>
      <div class="d-flex ms-auto">
        <div class="d-flex ml-auto" *ngIf="portfolio.isArchive === false && !isCreate">
          <label class="label d-flex mt-2 me-2" for="portfolio">Active </label>
          <div class="d-flex">
            <nb-toggle
              [(checked)]="portfolio.isActive"
              [disabled]="isDetail || !portfolio.isParentActive"
              (checkedChange)="activeToggleChange(portfolio.isActive)"
              status="primary"
              class="me-3"
            ></nb-toggle>
          </div>
        </div>
        <div class="d-flex ml-auto" *ngIf="userRoles[0] !== 'customer' && !isCreate">
          <label class="label d-flex mt-2 me-2" for="customer">Archive </label>
          <div class="d-flex">
            <nb-toggle
              (checkedChange)="(!!portfolio.isArchive)"
              status="primary"
              [disabled]="isDetail"
              [(checked)]="portfolio.isArchive"
              class="me-3"
            ></nb-toggle>
          </div>
        </div>

        <button
          *ngIf="!isDetail"
          nbButton
          status="primary"
          size="small"
          type="submit"
          id="portfolioSubmit"
          (click)="createForm.onSubmit()"
          class="float-end me-2"
        >
          <span class="d-none d-lg-inline-block">Save</span>
          <i class="d-inline-block d-lg-none fa-solid fa-save"></i>
        </button>
        <button
          nbButton
          status="danger"
          size="small"
          type="button"
          id="portfolioDelete"
          class="float-end me-2"
          *ngIf="userRoles[0] === 'admin' && !isCreate"
          (click)="onDelete(portfolio?.id)"
        >
          <span class="d-none d-lg-inline-block">Delete</span>
          <i class="d-inline-block d-lg-none fa-solid fa-trash"></i>
        </button>
        <button
          *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER, roleType.FIELDTECH]) && !portfolio.isArchive"
          (click)="isEdit = true; isDetail = false"
          nbButton
          status="primary"
          size="small"
          type="button"
          id="siteSubmit"
          class="float-end me-2"
        >
          <span class="d-none d-lg-inline-block">Edit</span>
          <i class="d-inline-block d-lg-none fa-solid fa-pen"></i>
        </button>
        <button nbButton status="basic" type="button" (click)="goBack()" size="small" class="float-end">
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body style="overflow: inherit !important">
    <form
      name="createForm"
      #createForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="createForm?.form?.valid && createPortfolio()"
    >
      <div class="form-group row">
        <div class="col-12 col-md-6 mb-2">
          <label class="label detailView" for="input-Customer">
            Customer
            <span class="ms-1 text-danger">*</span>
          </label>
          <span *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER, roleType.FIELDTECH])">
            <a [routerLink]="['/entities/customers/edit/' + portfolio?.customerId]">
              {{ portfolio.customerName }}
            </a>
          </span>
          <span *ngIf="(isDetail && checkAuthorisationsFn([roleType.CUSTOMER])) || checkAuthorisationsFn([roleType.FIELDTECH])">{{
            portfolio.customerName
          }}</span>
          <div *ngIf="isEdit || isCreate">
            <ng-select
              name="Customer"
              [items]="customerdata"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="portfolio.customerId"
              notFoundText="No Customer Found"
              placeholder="Select Customer"
              [clearable]="false"
              (ngModelChange)="onCustomerSelect($event)"
              appendTo="body"
            >
            </ng-select>
          </div>
        </div>
        <div class="col-12 col-md-6 mb-2">
          <div class="form-control-group">
            <label class="label detailView" for="input-name">
              Portfolio Name
              <span class="ms-1 text-danger">*</span>
            </label>
            <span *ngIf="isDetail">{{ portfolio.name }}</span>
            <div *ngIf="isEdit || isCreate">
              <input
                nbInput
                fullWidth
                [(ngModel)]="portfolio.name"
                #name="ngModel"
                id="name"
                name="name"
                class="form-control"
                spellcheck="true"
                contenteditable="true"
                pattern=".*\S.*"
                maxlength="32"
                [status]="name.dirty ? (name.invalid ? 'danger' : 'success') : 'basic'"
                required
                [attr.aria-invalid]="name.invalid && name.touched ? true : null"
              />
              <sfl-error-msg [control]="name" [isFormSubmitted]="createForm?.submitted" fieldName="Portfolio Name"></sfl-error-msg>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 mb-2">
          <label class="label detailView" for="input-PortfolioManger">Portfolio Lead</label>
          <span *ngIf="isDetail">{{ portfolio.portfolioManagerName ? portfolio.portfolioManagerName : '-' }}</span>
          <div *ngIf="isEdit || isCreate">
            <ng-select
              name="PortfolioManger"
              [multiple]="true"
              [items]="qeUsersList"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="portfolio.portfolioManager"
              notFoundText="No Portfolio Lead Found"
              placeholder="Select Portfolio Lead"
              [closeOnSelect]="false"
              [disabled]="disabled"
              appendTo="body"
              (search)="onDropdownSearchFilter($event, 'filteredPortfolioManagerIds')"
              (close)="filteredPortfolioManagerIds = []"
            >
              <ng-template ng-header-tmp *ngIf="qeUsersList && qeUsersList.length">
                <button
                  type="button"
                  (click)="selectAndDeselectAll(true, 'portfolioManager', 'filteredPortfolioManagerIds', 'qeUsersList')"
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  type="button"
                  (click)="selectAndDeselectAll(false, 'portfolioManager', 'filteredPortfolioManagerIds', 'qeUsersList')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="col-12 col-md-6 mb-2">
          <label class="label detailView" for="input-Site">Analyst</label>
          <span *ngIf="isDetail">{{ portfolio.analystUserName ? portfolio.analystUserName : '-' }}</span>
          <div *ngIf="isEdit || isCreate">
            <ng-select
              name="Site"
              [multiple]="true"
              [items]="qeUsersList"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="portfolio.analystUser"
              notFoundText="No Analyst Found"
              placeholder="Select Analyst"
              [closeOnSelect]="false"
              [disabled]="disabled"
              appendTo="body"
              (search)="onDropdownSearchFilter($event, 'filteredAnalystUserIds')"
              (close)="filteredAnalystUserIds = []"
            >
              <ng-template ng-header-tmp *ngIf="qeUsersList && qeUsersList.length">
                <button
                  type="button"
                  (click)="selectAndDeselectAll(true, 'analystUser', 'filteredAnalystUserIds', 'qeUsersList')"
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  type="button"
                  (click)="selectAndDeselectAll(false, 'analystUser', 'filteredAnalystUserIds', 'qeUsersList')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="col-12 col-md-6 mb-2">
          <label class="label detailView" for="input-PortfolioManger">Commercial Assets Manager</label>
          <span *ngIf="isDetail">{{ portfolio.commercialAssetsManagerName ? portfolio.commercialAssetsManagerName : '-' }}</span>
          <div *ngIf="isEdit || isCreate">
            <ng-select
              name="CommercialAssetsManagerName"
              [multiple]="true"
              [items]="qeCommercialAssetsManagerList"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="portfolio.commercialAssetsManager"
              notFoundText="No Commercial Assets Manager Found"
              placeholder="Select Commercial Assets Manager"
              [closeOnSelect]="false"
              [disabled]="disabled"
              appendTo="body"
              (search)="onDropdownSearchFilter($event, 'filteredCommercialAssetsManagerIds')"
              (close)="filteredCommercialAssetsManagerIds = []"
            >
              <ng-template ng-header-tmp *ngIf="qeCommercialAssetsManagerList && qeCommercialAssetsManagerList.length">
                <button
                  type="button"
                  (click)="
                    selectAndDeselectAll(
                      true,
                      'commercialAssetsManager',
                      'filteredCommercialAssetsManagerIds',
                      'qeCommercialAssetsManagerList'
                    )
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  type="button"
                  (click)="
                    selectAndDeselectAll(
                      false,
                      'commercialAssetsManager',
                      'filteredCommercialAssetsManagerIds',
                      'qeCommercialAssetsManagerList'
                    )
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-12 mb-2">
          <div class="form-control-group">
            <div>
              <label class="label detailView" for="input-customerTitle">Portfolio Contact Detail</label>
            </div>
            <div class="col-12">
              <div class="custom-x-scroll">
                <div style="min-width: 550px">
                  <div class="contact-header row mb-2" *ngIf="portfolio.customerEmails.length">
                    <div class="col-2 pe-0 text-center"><label class="label detailView">Use In Ticket</label></div>
                    <div class="col-2">
                      <label class="label detailView">Contact Name<span class="ms-1 text-danger">*</span></label>
                    </div>
                    <div class="col-3">
                      <label class="label detailView">Email<span class="ms-1 text-danger">*</span></label>
                    </div>
                    <div class="col-2">
                      <label class="label detailView">Phone</label>
                    </div>
                    <div class="col-2">
                      <label class="label detailView">Contact Title</label>
                    </div>
                    <div *ngIf="!isDetail" class="col-1 text-center"><label class="label detailView">Action</label></div>
                  </div>
                  <div class="contact-section row mb-2" *ngFor="let item of portfolio.customerEmails; let i = index; trackBy: trackByIndex">
                    <div class="col-2 pe-0 d-flex justify-content-center align-items-center">
                      <nb-checkbox
                        name="{{ 'useAsTicketContact-' + i }}"
                        [checked]="item.useAsTicketContact"
                        [(ngModel)]="item.useAsTicketContact"
                        [disabled]="isDetail"
                      ></nb-checkbox>
                    </div>
                    <div class="col-2 pe-0">
                      <span *ngIf="isDetail">{{ item.contactName }}</span>
                      <div *ngIf="isEdit || isCreate">
                        <input
                          nbInput
                          fullWidth
                          name="{{ 'customerName-' + i }}"
                          id="{{ 'input-customerName-' + i }}"
                          [(ngModel)]="item.contactName"
                          #contactName="ngModel"
                          class="form-control"
                          required
                        />
                        <sfl-error-msg
                          [control]="contactName"
                          [isFormSubmitted]="createForm?.submitted"
                          fieldName="Contact Name"
                        ></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-3 pe-0">
                      <div *ngIf="isDetail" class="email">{{ item.email }}</div>
                      <div *ngIf="isEdit || isCreate">
                        <input
                          nbInput
                          fullWidth
                          name="{{ 'email-' + i }}"
                          id="{{ 'input-email-' + i }}"
                          type="email"
                          [(ngModel)]="item.email"
                          #email="ngModel"
                          class="form-control"
                          appEmailValidate
                          required
                        />
                        <sfl-error-msg [control]="email" [isFormSubmitted]="createForm?.submitted" fieldName="Email"></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-2 pe-0">
                      <span *ngIf="isDetail">{{ item.phoneNumber ? item.phoneNumber : '-' }}</span>
                      <div *ngIf="isEdit || isCreate">
                        <input
                          nbInput
                          fullWidth
                          name="{{ 'phone-' + i }}"
                          id="{{ 'input-phone-' + i }}"
                          [(ngModel)]="item.phoneNumber"
                          #phoneNumber="ngModel"
                          class="form-control"
                          [mask]="contactNoFormat"
                        />
                        <sfl-error-msg
                          [control]="phoneNumber"
                          [isFormSubmitted]="createForm?.submitted"
                          fieldName="Phone Number"
                        ></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-2 pe-0">
                      <span *ngIf="isDetail">{{ item.customerTitle ? item.customerTitle : '-' }}</span>
                      <div *ngIf="isEdit || isCreate">
                        <input
                          nbInput
                          fullWidth
                          name="{{ 'customerTitle-' + i }}"
                          id="{{ 'input-customerTitle-' + i }}"
                          [(ngModel)]="item.customerTitle"
                          #customerTitle="ngModel"
                          class="form-control"
                        />
                      </div>
                    </div>
                    <div
                      *ngIf="!isDetail && userRoles[0] !== 'portfolioManager' && userRoles[0] !== 'analyst'"
                      class="col-1 d-flex justify-content-center align-items-center"
                    >
                      <em class="fa fa-trash text-danger px-2 pointerReportLink" (click)="deleteContact(item.id, i)"></em>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="!isDetail">
                <span class="text-primary pointerReportLink" (click)="addContact()">+ Add Contact</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-6 mb-2">
          <div class="form-control-group">
            <label class="label detailView" for="input-noOfSite">QE Alerts Email Account</label>
            <span *ngIf="isDetail">{{ portfolio.emailAccount ? portfolio.emailAccount : '-' }}</span>
            <div *ngIf="isEdit || isCreate">
              <input
                nbInput
                fullWidth
                [(ngModel)]="portfolio.emailAccount"
                #email="ngModel"
                name="email"
                id="input-email"
                class="form-control"
                appEmailValidate
              />
              <sfl-error-msg [control]="email" [isFormSubmitted]="createForm?.submitted" fieldName="Email"></sfl-error-msg>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-12 col-sm-6 col-md-4 mb-2" *ngIf="isEdit || isDetail">
          <div class="form-control-group">
            <label class="label detailView" for="input-noOfSite"> Number of Sites</label>
            <ng-container>
              <label>{{ portfolio?.noOfSite ? portfolio.noOfSite : '-' }}</label>
            </ng-container>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 mb-2" *ngIf="isEdit || isDetail">
          <div class="form-control-group">
            <label class="label detailView" for="input-noOfSite">kWdc</label>
            <ng-container>
              <label>{{ portfolio?.sumDcSize | sflRound | sflNumberWithCommas }}</label>
            </ng-container>
          </div>
        </div>
      </div>
      <nb-accordion class="mb-2" [nbSpinner]="attachmentsLoading">
        <nb-accordion-item [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head">Portfolio Files </nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="file-attachments">
              <div class="d-flex align-items-start justify-content-end mb-3">
                <input
                  nbInput
                  name="fileSearch"
                  id="input-fileSearch"
                  [(ngModel)]="fileSearchText"
                  #fileSearchModel="ngModel"
                  class="form-control me-2"
                  placeholder="Search Files"
                  (ngModelChange)="fileSearchChanged()"
                />

                <button
                  *ngIf="userRoles[0] !== 'customer' && isEdit"
                  nbButton
                  status="primary"
                  size="small"
                  type="button"
                  id="addFiles"
                  class="me-2"
                  [disabled]="!allSelectedFiles.length"
                  (click)="openEditTagsModal(addRemoveFilesTagsModal)"
                >
                  <span class="d-flex"><em class="pi pi-tag me-2"></em>Manage tags</span>
                </button>
                <button
                  *ngIf="userRoles[0] !== 'customer' && isEdit"
                  nbButton
                  status="primary"
                  size="small"
                  type="button"
                  id="addFiles"
                  class="me-2"
                  (click)="openFileUploadSidePanel(false, null)"
                >
                  <span class="d-flex"><em class="pi pi-plus me-2"></em>Add Files</span>
                </button>
              </div>
              <div class="main-image-gallery">
                <div id="fixed-table" setTableHeight [isFilterDisplay]="true" class="col-12 table-responsive table-card-view">
                  <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                    <thead *ngIf="createFileUploadList.length || fileAttachments?.fileGallery?.length">
                      <tr>
                        <th *ngIf="userRoles[0] !== 'customer' && isEdit" id="select-all" class="text-center">
                          <nb-checkbox
                            id="select-all"
                            class="sfl-track-checkbox"
                            [(ngModel)]="isSelectedAllFiles"
                            (change)="selectAllFiles()"
                            name="selectAllFiles"
                          >
                          </nb-checkbox>
                        </th>
                        <th id="FileName" (click)="sortFiles('fileName', sortOptionList['fileName'])">
                          File Name
                          <span
                            class="fa cursor-pointer ms-auto"
                            [ngClass]="{
                              'fa-arrow-up': sortOptionList['fileName'] === 'desc',
                              'fa-arrow-down': sortOptionList['fileName'] === 'asc',
                              'icon-selected': sortBy === 'fileName'
                            }"
                          ></span>
                        </th>
                        <th id="Tags">Tags</th>
                        <th id="Note">Note</th>
                        <th id="UploadedBy">Uploaded By</th>
                        <th id="Date" (click)="sortFiles('createdDate', sortOptionList['createdDate'])">
                          Date
                          <span
                            class="fa cursor-pointer ms-auto"
                            [ngClass]="{
                              'fa-arrow-up': sortOptionList['createdDate'] === 'desc',
                              'fa-arrow-down': sortOptionList['createdDate'] === 'asc',
                              'icon-selected': sortBy === 'createdDate'
                            }"
                          ></span>
                        </th>
                        <th id="Action" class="text-center">Action</th>
                      </tr>
                    </thead>
                    <tbody *ngIf="!isCreate">
                      <ng-container *ngIf="fileAttachments?.fileGallery?.length">
                        <tr
                          *ngFor="
                            let document of fileAttachments?.fileGallery
                              | paginate
                                : {
                                    id: 'documents',
                                    itemsPerPage: filesPaginationParams.itemsCount,
                                    currentPage: filesPaginationParams.currentPage,
                                    totalItems: fileAttachments?.totalCount
                                  }
                          "
                        >
                          <td data-title="Select Files" class="text-center" *ngIf="userRoles[0] !== 'customer' && isEdit">
                            <nb-checkbox
                              id="select-file+{{ document.id }}"
                              class="sfl-track-checkbox"
                              name="selectSingleFiles+{{ document.id }}"
                              (change)="singleFilesCheckChanged(document)"
                              [(ngModel)]="document.isSelectedForPreview"
                              [checked]="document.isSelectedForPreview"
                            >
                            </nb-checkbox>
                          </td>
                          <td data-title="File Name">
                            <div class="d-flex align-items-center">
                              <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                              <a [href]="document.fileUrl" target="_blank">
                                {{ document.fileName }}
                              </a>
                            </div>
                          </td>
                          <td data-title="tags">
                            <ng-container *ngIf="document?.fileTagTxt?.length">
                              <span class="tag-info-badge fw-bold" *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5">
                                <span class="px-2">
                                  {{ tagName }}
                                </span>
                              </span>
                              {{ document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : '' }}
                            </ng-container>
                            <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                          </td>
                          <td data-title="Note">
                            <div
                              *ngIf="document.notes"
                              nbTooltip="{{ document.notes.length > 600 ? (document.notes | slice : 0 : 600) + '...' : document.notes }}"
                              nbTooltipPlacement="top"
                              nbTooltipStatus="primary"
                            >
                              <sfl-read-more [content]="document.notes"></sfl-read-more>
                            </div>
                            <span *ngIf="!document.notes">N/A</span>
                          </td>
                          <td data-title="Uploaded By">{{ document.createdBy }}</td>
                          <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                          <td data-title="Action" class="text-center">
                            <div class="d-flex align-items-center justify-content-center">
                              <em
                                *ngIf="isEdit"
                                class="fa fa-edit text-primary cursor-pointer me-3"
                                (click)="openFileUploadSidePanel(true, document)"
                              ></em>
                              <em
                                class="fa fa-download text-primary cursor-pointer me-3"
                                (click)="downloadDropBoxFile(document.id, document.fileName)"
                              ></em>
                              <em
                                *ngIf="userRoles[0] !== 'customer'"
                                class="fa fa-trash text-danger cursor-pointer"
                                (click)="deleteDropBoxFile(document.id)"
                              ></em>
                            </div>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                    <tbody *ngIf="isCreate">
                      <ng-container *ngFor="let document of createFileUploadList">
                        <tr *ngIf="document.fileType === 'document'">
                          <td data-title="File Name">
                            <div class="d-flex align-items-center">
                              <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                              <span>
                                {{ document.fileName }}
                              </span>
                            </div>
                          </td>
                          <td data-title="tags">
                            <ng-container *ngIf="document?.fileTagTxt?.length">
                              <span class="tag-info-badge fw-bold" *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5">
                                <span class="px-2">
                                  {{ tagName }}
                                </span>
                              </span>
                              {{ document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : '' }}
                            </ng-container>
                            <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                          </td>
                          <td data-title="Note">
                            <div *ngIf="document.notes" nbTooltip="{{ document.notes }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                              <sfl-read-more [content]="document.notes"></sfl-read-more>
                            </div>
                            <span *ngIf="!document.notes">N/A</span>
                          </td>
                          <td data-title="Uploaded By">{{ document.createdBy }}</td>
                          <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                          <td data-title="Action" class="text-center">
                            <div class="d-flex align-items-center justify-content-center" *ngIf="userRoles[0] !== 'customer'">
                              <em class="fa fa-trash text-danger cursor-pointer" (click)="deleteDropBoxFile(document.id, true)"></em>
                            </div>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </table>
                  <ng-container *ngIf="(!fileAttachments?.fileGallery?.length && !isCreate) || (!createFileUploadList?.length && isCreate)">
                    <p class="no-record text-center">No Data Found</p>
                  </ng-container>
                </div>
                <div class="mt-2 d-md-flex align-items-center" *ngIf="fileAttachments?.fileGallery?.length && !isCreate">
                  <div class="d-flex align-items-center">
                    <label class="mb-0">Items per page: </label>
                    <ng-select
                      class="ms-2"
                      [(ngModel)]="filesPaginationParams.pageSize"
                      [clearable]="false"
                      [searchable]="false"
                      (change)="onChangeSize()"
                      name="documentsPageSize"
                      #documentsPageSize="ngModel"
                      appendTo="body"
                    >
                      <ng-option value="5">5</ng-option>
                      <ng-option value="10">10</ng-option>
                      <ng-option value="50">50</ng-option>
                      <ng-option value="100">100</ng-option>
                    </ng-select>
                  </div>
                  <strong class="ms-md-3">Total: {{ fileAttachments?.totalCount }}</strong>
                  <div class="ms-md-auto ms-sm-0">
                    <pagination-controls
                      id="documents"
                      (pageChange)="onPageChange($event)"
                      class="paginate ticket-attachment"
                    ></pagination-controls>
                  </div>
                </div>
              </div>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <nb-accordion class="mb-2 portfolio-note" [nbSpinner]="portfolioNotesLoading" *ngIf="userRoles[0] !== 'customer'">
        <nb-accordion-item [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head">Portfolio Notes </nb-accordion-item-header>
          <nb-accordion-item-body>
            <sfl-notes-listing
              [entityId]="id"
              [entityTypeId]="entityTypeId"
              [entityTypeName]="entityTypeName"
              [isEntityEditMode]="isEdit"
              [isEntityViewMode]="isDetail"
              [isEntityCreateMode]="isCreate"
              (notesListingLoadingEvent)="portfolioNotesLoading = $event"
            ></sfl-notes-listing>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <ng-container *ngIf="isOutageAlert && userRoles[0] === 'admin'">
        <nb-accordion>
          <nb-accordion-item [expanded]="true" class="border-bottom mb-2">
            <nb-accordion-item-header class="accordion_head">Alerts</nb-accordion-item-header>
            <nb-accordion-item-body>
              <div class="row">
                <div class="col-12 col-lg-6 mb-2 mb-lg-0">
                  <div class="row">
                    <div class="col-12 col-md-6 mb-2">
                      <nb-toggle
                        status="primary"
                        [disabled]="isDetail"
                        [(ngModel)]="portfolioOutage.zeroGeneration"
                        name="isZeroGeneration"
                        labelPosition="start"
                      >
                        <label class="label">Zero Generation</label>
                      </nb-toggle>
                    </div>
                    <div class="col-12 col-md-6 mb-2">
                      <nb-toggle
                        status="primary"
                        [disabled]="isDetail || !portfolioOutage.zeroGeneration"
                        [(ngModel)]="portfolioOutage.isParentSetting"
                        name="customerSetting"
                        labelPosition="start"
                        (ngModelChange)="useParentSetting($event)"
                      >
                        <label class="label">Use Customer Settings</label>
                      </nb-toggle>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12 col-md-6 mb-2">
                      <div class="form-control-group">
                        <label class="label" for="input-alertpowerThreshold">Power Threshold (kW)</label>
                        <span *ngIf="isDetail" class="ms-2">{{
                          portfolioOutage?.powerThreshold ? portfolioOutage?.powerThreshold : '-'
                        }}</span>
                        <div *ngIf="isEdit || isCreate">
                          <input
                            nbInput
                            fullWidth
                            [(ngModel)]="portfolioOutage.powerThreshold"
                            #alertPowerThreshold="ngModel"
                            name="alertPowerThreshold"
                            id="input-alertpowerThreshold"
                            class="form-control"
                            [disabled]="!portfolioOutage.zeroGeneration || portfolioOutage.isParentSetting"
                            sflNumbersOnly
                          />
                        </div>
                      </div>
                    </div>
                    <div class="col-12 col-md-6 mb-2">
                      <div class="form-control-group">
                        <label class="label" for="input-triggerCount">Trigger Count</label>
                        <span *ngIf="isDetail" class="ms-2">{{ portfolioOutage?.triggerCount ? portfolioOutage?.triggerCount : '-' }}</span>
                        <div *ngIf="isEdit || isCreate">
                          <input
                            nbInput
                            fullWidth
                            [(ngModel)]="portfolioOutage.triggerCount"
                            #triggerCount="ngModel"
                            [disabled]="!portfolioOutage.zeroGeneration || portfolioOutage.isParentSetting"
                            name="triggerCount"
                            id="input-triggerCount"
                            class="form-control"
                            sflNumbersOnly
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="label">Daytime Alerting Window </label>
                    <table class="table table-hover table-bordered mb-2" aria-describedby="Site Performance List">
                      <thead>
                        <tr>
                          <th scope="col">Month</th>
                          <th scope="col">Local Start Time</th>
                          <th scope="col">Local End Time</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let Daytime of portfolioOutage.timeSetting; let i = index">
                          <td>
                            <span>{{ getMonthName(Daytime.month) }}</span>
                          </td>
                          <td>
                            <span *ngIf="isDetail">{{ Daytime.startTime }}</span>
                            <input
                              *ngIf="isCreate || isEdit"
                              name="expectedStartTime-{{ i }}"
                              id="input-name"
                              [(ngModel)]="portfolioOutage?.timeSetting[i].startTime"
                              class="form-control"
                              [disabled]="!portfolioOutage.zeroGeneration || portfolioOutage.isParentSetting"
                              spellcheck="true"
                              contenteditable="true"
                              nbInput
                              (ngModelChange)="formatTime($event, i, 'startTime')"
                              maxlength="5"
                              placeholder="hh:mm"
                              class="form-control"
                            />
                          </td>
                          <td>
                            <span *ngIf="isDetail">{{ Daytime.endTime }}</span>
                            <input
                              *ngIf="isCreate || isEdit"
                              name="expectedEndTime-{{ i }}"
                              id="input-production"
                              [(ngModel)]="portfolioOutage?.timeSetting[i].endTime"
                              class="form-control"
                              [disabled]="!portfolioOutage.zeroGeneration || portfolioOutage.isParentSetting"
                              spellcheck="true"
                              contenteditable="true"
                              nbInput
                              maxlength="5"
                              placeholder="hh:mm"
                              (ngModelChange)="formatTime($event, i, 'endTime')"
                              class="form-control"
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div id="fixed-table" class="col-12 col-lg-6" [ngClass]="{ 'min-height-0': !portfolioOutage?.sites?.length }">
                  <table class="table table-hover table-bordered" aria-describedby="Site Performance List">
                    <thead>
                      <tr>
                        <th scope="col">Sites</th>
                        <th scope="col">Zero Generation</th>
                        <th scope="col">Use Portfolio Settings</th>
                      </tr>
                    </thead>
                    <tbody *ngIf="portfolioOutage?.sites?.length">
                      <tr *ngFor="let sites of portfolioOutage.sites; let siteIndex = index">
                        <td class="text-end">
                          <span>{{ sites.siteName }}</span>
                        </td>
                        <td>
                          <nb-toggle
                            status="primary"
                            [disabled]="isDetail"
                            [(ngModel)]="sites.zeroGeneration"
                            name="zeroGenerationSite-{{ siteIndex }}"
                            labelPosition="start"
                          >
                          </nb-toggle>
                        </td>
                        <td>
                          <nb-toggle
                            status="primary"
                            [disabled]="isDetail || !sites.zeroGeneration"
                            [(ngModel)]="sites.isParentSetting"
                            name="usePortfolioSetting-{{ siteIndex }}"
                            labelPosition="start"
                          ></nb-toggle>
                        </td>
                      </tr>
                    </tbody>
                    <tbody *ngIf="!portfolioOutage?.sites?.length">
                      <tr>
                        <td colspan="3" class="no-record text-center">No sites found</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </nb-accordion-item-body>
          </nb-accordion-item>
        </nb-accordion>
      </ng-container>
    </form>
  </nb-card-body>
</nb-card>

<ng-template #addRemoveFilesTagsModal>
  <div class="selcet-inverter">
    <div class="modal-header align-items-center">
      <h6 class="modal-title">Manage file tags</h6>
      <button type="button" class="close" aria-label="Close" (click)="addRemoveFilesTagsModalRef.hide(); searchText = ''">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="row align-items-center">
        <!-- <p><strong>Selected files :</strong> {{ selectedFilesNames }}</p> -->
        <div class="file-attachments">
          <strong>Selected files :</strong>
          <ng-container *ngIf="allSelectedFiles?.length">
            <span class="tag-info-badge fw-bold" *ngFor="let file of allSelectedFiles | slice : 0 : 3">
              <span class="px-2">
                {{ file?.fileName }}
              </span>
            </span>
            <span nbTooltip="{{ selectedFilesNamesString }}" nbtooltipplacement="top" nbTooltipStatus="primary">
              <span class="tag-info-badge fw-bold" *ngIf="allSelectedFiles?.length > 3">{{ '+' + (allSelectedFiles?.length - 3) }}</span>
            </span>
          </ng-container>
        </div>
        <div class="col-12 mt-2 file-tag-dd">
          <label class="label" for="fileTagsApply">Select file tags </label>
          <ng-select
            name="fileTagsApply"
            id="region-drop-down"
            class="sfl-track-dropdown"
            [multiple]="true"
            [items]="filesTagList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="fileTagIds"
            #fileTagsApply="ngModel"
            notFoundText="No File Tag Found"
            placeholder="Select File Tag"
            [closeOnSelect]="false"
            (search)="onFilter($event)"
            (ngModelChange)="reorderTags()"
            (close)="filteredAppliedTags = []"
          >
            <ng-template ng-header-tmp *ngIf="filesTagList && filesTagList.length">
              <button type="button" (click)="toggleSelectUnselectAllTags(true)" class="btn btn-sm btn-primary me-2">Select all</button>
              <button type="button" (click)="toggleSelectUnselectAllTags(false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
              {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div class="d-flex justify-content-end align-items-center">
        <button
          class="linear-mode-button me-3"
          nbButton
          status="secondary"
          size="small"
          (click)="addRemoveMultipleFilesTags(false)"
          type="button"
        >
          Remove Tags
        </button>
        <button
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          (click)="addRemoveMultipleFilesTags(true)"
          type="button"
        >
          Apply Tags
        </button>
      </div>
    </div>
  </div>
</ng-template>
