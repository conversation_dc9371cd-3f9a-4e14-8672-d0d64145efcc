import { TitleCasePipe } from '@angular/common';
import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import * as pdfjsLib from 'pdfjs-dist';
import pdfWorker from 'pdfjs-dist/build/pdf.worker.entry';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { FILTER_SECTION_ENUM, FilterDetails } from '../../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../../@shared/constants';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { CustomFormService } from '../custom-form.service';
import { TemplateList, TemplateListingResponse } from '../custom-forms.model';
import { HowToUseVideoModalComponent } from '../how-to-use-video-modal/how-to-use-video-modal.component';
import { ROLE_TYPE } from '../../../../@shared/enums';

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorker;
@Component({
  selector: 'sfl-template-listing',
  templateUrl: './template-listing.component.html',
  styleUrls: ['./template-listing.component.scss'],
  providers: [TitleCasePipe]
})
export class TemplateListingComponent implements OnInit {
  @ViewChild('pdfUploadInput') pdfUploadInput: ElementRef;
  subscription: Subscription = new Subscription();
  templateList: TemplateList[] = [];
  loading = false;
  filterDetails: FilterDetails = new FilterDetails();
  isFilterDisplay = false;
  filterModel: CommonFilter = new CommonFilter();
  pageSize = AppConstants.rowsPerPage;
  totalCount: number = 0;
  currentPage = 1;
  sortOptionList = {
    templateTypeName: 'asc',
    templateName: 'asc',
    updatedDate: 'asc'
  };
  modalRef: BsModalRef;
  viewPage = 'customFormTemplatePage';
  viewFilterSection = 'customFormTemplateFilterSection';
  templateTypesList = [];
  userRole: string;
  isNew = false;
  roleType = ROLE_TYPE;

  constructor(
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    public readonly customFormService: CustomFormService
  ) {}

  ngOnInit() {
    this.userRole = this.storageService.get('user')?.authorities[0];
    const filter = this.storageService.get(this.viewPage),
      filterSection = this.storageService.get(this.viewFilterSection);

    if (filter) {
      this.filterModel = filter;
      this.pageSize = this.filterModel.itemsCount;
      this.currentPage = this.filterModel.page + 1;
      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
    }

    this.initFilterDetails();

    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.page = this.currentPage - 1;
    this.filterModel.sortBy = 'updatedDate';
    this.isFilterDisplay = filterSection;

    this.getTemplateTypeDropdown();
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.filterSectionEnum = FILTER_SECTION_ENUM.OPERATIONS_CUSTOM_FORMS_TEMPLATE_LISTING;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.SEARCH_BOX.show = true;
    filterItem.TEMPLATE_TYPE_IDS.show = true;
    this.filterDetails.default_sort = 'updatedDate';
    this.filterDetails.filter_item = filterItem;
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }

    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;

    this.getAllTemplateList();
  }

  getTemplateTypeDropdown() {
    this.loading = true;

    this.customFormService.getTemplateTypeDropdown().subscribe({
      next: (data: any) => {
        this.templateTypesList = data;
        const filter = this.storageService.get(this.viewPage);
        const operationCustomTemplateFilterKeys = ['search', 'templateTypeIds'];
        if (this.storageService.shouldCallListApi(filter, {}, {}, {}, operationCustomTemplateFilterKeys)) {
          this.getAllTemplateList();
        } else {
          this.loading = false;
        }
      },
      error: () => (this.loading = false)
    });
  }

  getAllTemplateList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;

    if (filterParams) {
      this.filterModel = filterParams;
    }

    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.subscription.add(
      this.customFormService.getTemplateListing(this.filterModel).subscribe({
        next: (data: TemplateListingResponse) => {
          this.templateList = data.listOfQESTTemplate;
          if (!this.templateList.length && !this.filterModel.search && !this.filterModel.templateTypeIds.length) {
            this.isNew = true;
          }
          this.totalCount = data.totalQESTTemplate;
          this.loading = false;
        },
        error: () => (this.loading = false)
      })
    );
  }

  onChangeSize() {
    this.currentPage = 0;
    this.filterModel.page = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllTemplateList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllTemplateList();
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = 1;
    this.getAllTemplateList(true, filterParams);
  }

  goToLandingPage() {
    this.router.navigateByUrl('/entities/operations/custom-forms');
  }

  openModal(template: TemplateRef<any>, styleClass: string) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: styleClass
    };

    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  expandView() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog'
    };

    this.modalRef = this.modalService.show(HowToUseVideoModalComponent, ngModalOptions);
  }

  getUpload(event: any) {
    this.customFormService.selectedFileForTemplate = event.target.files[0];

    this.customFormService.templateName = event.target.files[0].name.split('.').slice(0, -1).join('.') + ' Template';

    this.pdfUploadInput?.nativeElement ? (this.pdfUploadInput.nativeElement.value = '') : '';
  }

  createTemplate(form: NgForm) {
    if (this.customFormService.selectedFileForTemplate && form.valid) {
      this.router.navigateByUrl('/entities/operations/custom-forms/add-template');
      this.modalRef.hide();
    } else {
      form.control.markAllAsTouched();
    }
  }

  onCancel() {
    this.customFormService.selectedFileForTemplate = null;

    this.pdfUploadInput?.nativeElement ? (this.pdfUploadInput.nativeElement.value = '') : '';

    this.customFormService.templateName = '';
    this.customFormService.templateType = null;

    this.modalRef.hide();
  }

  onTemplateDelete(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this template?' }
      };

      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

      this.modalRef.content.onClose.subscribe({
        next: response => {
          if (response) {
            this.subscription.add(
              this.customFormService.deleteTemplate(event).subscribe({
                next: res => {
                  if (res) {
                    if (this.currentPage !== 0 && this.templateList.length === 1) {
                      this.onChangeSize();
                      this.alertService.showSuccessToast(res.message);
                    } else {
                      this.getAllTemplateList();
                      this.alertService.showSuccessToast(res.message);
                    }
                  }
                },
                error: () => (this.loading = false)
              })
            );
          }
        },
        error: () => (this.loading = false)
      });
    }
  }

  cloneQESTTemplate(id: number) {
    this.loading = true;

    this.customFormService.cloneQESTTemplate(id).subscribe({
      next: res => {
        this.alertService.showSuccessToast(res.message);
        this.getAllTemplateList();
      },
      error: () => (this.loading = false)
    });
  }

  goToEditTemplatePage(templateId: number) {
    this.router.navigateByUrl(`/entities/operations/custom-forms/edit-template/${templateId}`);
  }

  gotoForms() {
    this.router.navigateByUrl(`/entities/operations/custom-forms/edit-form/forms`);
  }
}
