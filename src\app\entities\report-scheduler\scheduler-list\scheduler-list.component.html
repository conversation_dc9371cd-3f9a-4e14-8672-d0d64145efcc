<nb-card class="dataSourceSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Report Scheduler</h6>
        <button
          *ngIf="userRoles[0] === 'admin'"
          class="linear-mode-button ms-auto"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="openSchedulerModel('create')"
          type="button"
        >
          <span class="d-none d-md-inline-block">Add Task</span>
          <i class="d-inline-block d-md-none fa-solid fa-calendar-plus-o"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="data source">
          <thead>
            <tr>
              <th class="text-start" id="name">Name</th>
              <th class="text-start" id="status">Status</th>
              <th class="text-start" id="frequencyType">Trigger</th>
              <th class="text-start" id="nextRun">Next Run Time</th>
              <th class="text-start" id="lastRun">Last Run Time</th>
              <th class="text-start" id="lastRunResult">Last Run Result</th>
              <th class="text-start" id="createdBy">Created by</th>
              <th class="text-start" id="updatedBy">Updated by</th>
              <th class="text-start" id="updatedOn">Updated Date/Time</th>
              <th class="text-center" id="action">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let scheduler of schedulerLists" [ngClass]="scheduler.status ? 'active' : 'inActive'">
              <td data-title="Name">{{ scheduler?.name }}</td>
              <td data-title="Status">
                <nb-toggle
                  [(checked)]="scheduler.status"
                  (checkedChange)="onInactiveSchedule(scheduler?.schedulerReportId, scheduler)"
                ></nb-toggle>
              </td>
              <td data-title="Trigger">{{ scheduler?.frequencyType }}</td>
              <td data-title="Next Run Time">{{ scheduler?.nextRun | date : 'short' }}</td>
              <td data-title="Last Run Time">{{ scheduler?.lastRun | date : 'short' }}</td>
              <td data-title="Last Run Result">{{ scheduler?.lastRunResult }}</td>
              <td data-title="Created by">{{ scheduler?.createdBy }}</td>
              <td data-title="Updated by">{{ scheduler?.updatedBy }}</td>
              <td data-title="Updated Date/Time">{{ scheduler?.updatedOn | date : 'short' }}</td>
              <td data-title="Actions">
                <div class="d-md-flex justify-content-center">
                  <a class="listgrid-icon text-primary" (click)="openSchedulerModel('Edit', scheduler?.schedulerReportId)">
                    <em class="fa fa-edit" nbTooltip="Edit schedule" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a class="px-2 listgrid-icon" (click)="onRunSchedule(scheduler?.schedulerReportId)">
                    <em class="fa fa-paper-plane-o" nbTooltip="Run Schedule Now" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-danger listgrid-icon"
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER]"
                    (click)="onDelete(scheduler?.schedulerReportId)"
                  >
                    <em class="fa fa-trash" nbTooltip="Delete schedule" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                </div>
              </td>
            </tr>
            <tr *ngIf="!schedulerLists.length">
              <td colspan="10" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </nb-card-body>
</nb-card>
