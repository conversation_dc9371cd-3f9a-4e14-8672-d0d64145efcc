import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModeWiseGuard } from '../../../@shared/services/mode-wise.guard';
import { PermissionGuard } from '../../../@shared/services/permission.guard';
import { JhaAddEditComponent } from './jha-add-edit/jha-add-edit.component';
import { JhaLinkingComponent } from './jha-linking/jha-linking.component';
import { JhaListingComponent } from './jha-listing/jha-listing.component';
import { ROLE_TYPE } from '../../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: JhaListingComponent,
    data: { pageTitle: 'Safety JHA' }
  },
  {
    path: 'detail/:id',
    component: JhaLinkingComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Safety JHA Details'
    }
  },
  {
    path: 'upload',
    component: JhaAddEditComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Add JHA'
    }
  },
  {
    path: 'upload/:mode/:id',
    component: JhaAddEditComponent,
    canActivate: [ModeWiseGuard],
    data: {
      edit: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Update JHA'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class JhaRoutingModule {}
