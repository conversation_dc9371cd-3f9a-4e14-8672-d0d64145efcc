import { CdkDragDrop, transferArrayItem } from '@angular/cdk/drag-drop';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NbThemeService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../@shared/components/filter/filter.model';
import { AppConstants } from '../../@shared/constants';
import { Dashboard, DashboardFilterList, ExportDashboard, ExportScheduleDashboard } from '../../@shared/models/dashboard.model';
import { AlertService } from '../../@shared/services';
import { CommonService } from '../../@shared/services/common.service';
import { StorageService } from '../../@shared/services/storage.service';
import { ReportService } from '../report/report.service';
import { AppliedFilter } from '../site-device/site-device.model';
import { WO_STATUSES } from '../workorder-management/workorder.model';
import { GroupName, PmDashboardPageFilterKeys } from './dashboard.model';
import { DashboardService } from './dashboard.service';
import { HasFieldTechPipe } from './has-field-tech.pipe';
import { ModalDashboardComponent } from './modal-dashboard/modal-dashboard.component';
import { PmScheduleRescheduleBulkActionComponent } from './pm-schedule-reschedule-bulk-action/pm-schedule-reschedule-bulk-action.component';
import { WOCountPipe } from './wo-count.pipe';
import { environment } from '../../../environments/environment';
import { ShowMessageComponent } from '../../@shared/components/Show-Message/show-message/show-message.component';
import { ROLE_TYPE } from '../../@shared/enums';
import { checkAuthorisations } from '../../@shared/utils';

@Component({
  selector: 'QESolar-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  providers: [WOCountPipe, HasFieldTechPipe, CurrencyPipe]
})
export class DashboardComponent implements OnInit, OnDestroy {
  isLoadingFirstTime = true;
  loading = false;
  currentPage = 1;
  currentPageForScheduledView = 1;
  pageSize = AppConstants.rowsPerPage;
  pageSizeScheduledView = AppConstants.rowsPerPage;
  subscription: Subscription = new Subscription();
  total: number;
  modalRef: BsModalRef;
  modalRef2: BsModalRef;
  WorkOrderDashboard: any;
  eventId: number;
  ellipsisSite = false;
  viewPage = FILTER_PAGE_NAME.PM_DASHBOARD;
  dashboardViewMode = 'complianceView';
  viewPageScheduledView = 'dashboardPageScheduleView';
  filterModel: CommonFilter = new CommonFilter();
  filterModelScheduledView: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  filterList = DashboardFilterList;
  viewFilterSection = 'dashboardFilterSection';
  sortOptionList = {
    SiteName: 'asc',
    State: 'asc',
    kWdc: 'asc',
    CustomerPortfolio: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc'
  };
  sortOptionListScheduledView = {
    SiteName: 'asc',
    State: 'asc',
    kWdc: 'asc',
    CustomerPortfolio: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc'
  };
  isLiDisabled: boolean = false;
  filterDetails: FilterDetails = new FilterDetails();
  dashboardDetail: Dashboard = new Dashboard();
  dashboardScheduleViewDetail: Dashboard = new Dashboard();
  doughnutDGChartData = [];
  doughnutUtilityChartData = [];
  currentTheme = 'dark';
  chartDGDto = [];
  chartUtilityDto = [];
  isFullView = false;
  isCompactView = false;
  dashboardViewType = 'complianceView';
  statuses = [1, 2, 3, 4, 5, 9, 99999]; // 99999 element is for unscheduled wo with tentative month
  months = [
    {
      name: 'January',
      value: 'jan'
    },
    {
      name: 'February',
      value: 'feb'
    },
    {
      name: 'March',
      value: 'mar'
    },
    {
      name: 'April',
      value: 'apr'
    },
    {
      name: 'May',
      value: 'may'
    },
    {
      name: 'June',
      value: 'jun'
    },
    {
      name: 'July',
      value: 'jul'
    },
    {
      name: 'August',
      value: 'aug'
    },
    {
      name: 'September',
      value: 'sept'
    },
    {
      name: 'October',
      value: 'oct'
    },
    {
      name: 'November',
      value: 'nov'
    },
    {
      name: 'December',
      value: 'dec'
    }
  ];
  rescheduleActionList = [
    {
      name: 'Tentative Schedule',
      value: 1,
      isDisable: false
    },
    {
      name: 'Schedule',
      value: 2,
      isDisable: false
    },
    {
      name: 'Reschedule',
      value: 3,
      isDisable: false
    },
    {
      name: 'Pending Reschedule',
      value: 4,
      isDisable: false
    },
    {
      name: 'Schedule Pending',
      value: 5,
      isDisable: false
    }
  ];
  selectedRescheduleAction = null;
  days = [];
  selectedMonth = '';
  isSchedulerMode = false;
  isMultiSelect = false;
  selectedWorkOrders = [];
  currentMonthNumber = 0;
  currentYear;
  @ViewChild('file') fileInput;
  userRoll: string[];
  previousIndexOfDraggedElement: number;
  currentIndexOfDraggedElement: number;
  userRole = this.storageService.get('user').authorities;
  WoStatuses = WO_STATUSES;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly datePipe: DatePipe,
    private readonly alertService: AlertService,
    private readonly commonService: CommonService,
    private readonly modalService: BsModalService,
    private readonly themeService: NbThemeService,
    private readonly reportService: ReportService,
    private readonly storageService: StorageService,
    private readonly dashboardService: DashboardService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly currencyPipe: CurrencyPipe
  ) {}

  ngOnInit() {
    this.userRoll = this.storageService.get('user').authorities;
    this.themeService.onThemeChange().subscribe(themeName => {
      this.currentTheme = themeName.name;
      this.doughnutDGChartData = [];
      if (this.chartDGDto) {
        this.createDGChart();
      }
      this.doughnutUtilityChartData = [];
      if (this.chartUtilityDto) {
        this.createUtilityChart();
      }
    });

    const filter = this.storageService.get(this.viewPage),
      filterScheduledView = this.storageService.get(this.viewPageScheduledView),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection);
    if (filter) {
      this.filterModel = JSON.parse(JSON.stringify(filter));
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModelScheduledView.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModelScheduledView.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModelScheduledView.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.filterModelScheduledView.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.filterModel.states = (localFilterData || defaultFilterData).states;
      this.filterModelScheduledView.states = (localFilterData || defaultFilterData).states;
      this.filterModel.sortBy = 'DefaultSorting';
    }

    if (filterScheduledView) {
      this.filterModelScheduledView = JSON.parse(JSON.stringify(filterScheduledView));
    }

    this.initFilterDetails();
    this.reportService.setData(true);
    this.isFilterDisplay = filterSection;

    if (this.filterModel.direction && this.filterModel.sortBy) {
      this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
    }
    if (this.filterModelScheduledView.direction && this.filterModelScheduledView.sortBy) {
      this.sortOptionListScheduledView[this.filterModelScheduledView.sortBy] = this.filterModelScheduledView.direction;
    }
    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModelScheduledView.page) {
      this.currentPageForScheduledView = this.filterModelScheduledView.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }
    if (this.filterModelScheduledView.itemsCount) {
      this.pageSizeScheduledView = this.filterModelScheduledView.itemsCount;
    }

    if (!this.filterModel.year) {
      this.filterModel.year = this.commonService.getCurrentYear();
    }
    if (!this.filterModelScheduledView.year) {
      this.filterModelScheduledView.year = this.commonService.getCurrentYear();
    }
    if (this.filterModel.isUnScheduled) {
      this.isSchedulerMode = true;
      this.isCompactView = false;
    }
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
    if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, PmDashboardPageFilterKeys)) {
      this.getAllDashboardList();
    }

    this.subscription.add(
      this.dashboardService.reloadWorkOrder$.subscribe(woId => {
        if (woId !== 0) {
          this.refreshWorkOrderDetails();
          this.dashboardService.reloadWorkOrder$.next(0);
        }
      })
    );
    this.subscription.add(
      this.dashboardService.refreshWoList$.subscribe(res => {
        if (res) {
          this.dashboardService.refreshWoList$.next(false);
          this.refreshList(this.dashboardViewType === 'scheduleView' ? this.filterModelScheduledView : this.filterModel);
        }
      })
    );
    // preserve the dashboard mode
    if (this.storageService.get('dashboardViewType')) {
      this.dashboardViewType = this.storageService.get('dashboardViewType');
    }
  }

  createDGChart(): void {
    this.chartDGDto.forEach(chart => {
      chart.data = chart.data.map(item => {
        if (item.name === 'Not Created or Deleted') {
          return {
            ...item,
            itemStyle: {
              color: 'transparent',
              borderColor: '#161b2f ',
              borderWidth: 2
            }
          };
        }
        return item;
      });

      const chartConfig: any = this.chartConfig(chart);
      const hasValueNotZeroOrNull = chart.data.some(item => item.value !== null && item.value !== 0);
      chartConfig.isCreateChart = hasValueNotZeroOrNull; // Set showNoDataMessage property on chart object
      this.doughnutDGChartData.push(chartConfig);
    });
  }

  createUtilityChart(): void {
    this.chartUtilityDto.forEach(chart => {
      chart.data = chart.data.map(item => {
        if (item.name === 'Not Created or Deleted') {
          return {
            ...item,
            itemStyle: {
              color: 'transparent',
              borderColor: '#161b2f ',
              borderWidth: 2
            }
          };
        }
        return item;
      });

      const chartConfig: any = this.chartConfig(chart);
      const hasValueNotZeroOrNull = chart.data.some(item => item.value !== null && item.value !== 0);
      chartConfig.isCreateChart = hasValueNotZeroOrNull; // Set showNoDataMessage property on chart object
      this.doughnutUtilityChartData.push(chartConfig);
    });
  }

  chartConfig(chart: any): any {
    return {
      tooltip: {
        trigger: 'item',
        position: 'top',
        backgroundColor: '#000000',
        borderWidth: 0,
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#000000'
          }
        },
        textStyle: {
          color: '#FFFFFF',
          fontSize: 11
        }
      },
      color: ['#1cc88a', '#cad728', '#e97816', '#36b9cc', '#4e73df', '#fe7070', '#5a5c69'],
      grid: {
        y: 100
      },
      series: [
        {
          type: 'pie',
          radius: ['75%', '95%'],
          avoidLabelOverlap: true,
          label: {
            color: this.currentTheme === 'dark' ? '#FFFFFF' : '#000000',
            fontSize: '16',
            position: 'center',
            formatter: () => {
              return chart.name;
            }
          },
          tooltip: {
            valueFormatter: function (value: any) {
              return (
                (value as number).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + `%`
              );
            }
          },
          labelLine: {
            show: false
          },
          data: chart.data
        }
      ],
      isCreateChart: true,
      chartName: chart.name
    };
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.START_YEAR.show = true;
    filterItem.STATE.show = true;
    filterItem.ASSESSMENT_TYPE.show = true;
    filterItem.WORKORDER_STATUS.show = true;
    filterItem.FREQUENCY_TYPE.show = true;
    filterItem.ARRAY_TYPE.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.FIELD_TECH_IDS.show = true;
    filterItem.IS_RESCHEDULED.show = true;
    filterItem.IS_UNSCHEDULED.show = true;
    filterItem.IS_TENTATIVE_MONTH.show = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.default_sort = 'DefaultSorting';
    this.filterDetails.filter_item = filterItem;
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    (this.dashboardViewType === 'scheduleView' ? this.sortOptionListScheduledView : this.sortOptionList)[sortBy] = changeSort;
    (this.dashboardViewType === 'scheduleView' ? this.filterModelScheduledView : this.filterModel).sortBy = sortBy;
    (this.dashboardViewType === 'scheduleView' ? this.filterModelScheduledView : this.filterModel).direction = changeSort;
    this.sortOptionList[sortBy] = changeSort;
    this.sortOptionListScheduledView[sortBy] = changeSort;

    this.filterModelScheduledView.sortBy = sortBy;
    this.filterModelScheduledView.direction = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllDashboardList();
  }

  getAllDashboardList(saveFilter = true, dashboardView = '', filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = JSON.parse(JSON.stringify(filterParams));
      this.filterModelScheduledView = JSON.parse(JSON.stringify(filterParams));
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
      this.storageService.set(this.viewPageScheduledView, this.filterModelScheduledView);
    }
    this.currentYear = this.filterModel.year;
    this.subscription.add(
      this.dashboardService.getDashboard(this.filterModel).subscribe({
        next: (data: Dashboard) => {
          this.dashboardList(data);
          this.dashboardScheduledList(data);
        },
        error: () => (this.loading = false)
      })
    );

    // if (!dashboardView) {
    //   this.subscription.add(
    //     this.dashboardService.getDashboard(this.filterModel).subscribe({
    //       next: (data: Dashboard) => {
    //         this.dashboardList(data);
    //         this.dashboardScheduledList(data);
    //       },
    //       error: () => (this.loading = false)
    //     })
    //   );
    // } else {
    //   if (dashboardView === 'complianceView') {
    //     this.subscription.add(
    //       this.dashboardService.getDashboard(this.filterModel).subscribe({
    //         next: (data: Dashboard) => {
    //           this.dashboardList(data);
    //           this.dashboardScheduledList(data);
    //         },
    //         error: () => (this.loading = false)
    //       })
    //     );
    //   }
    // else {
    //   this.subscription.add(
    //     this.dashboardService.getDashboardScheduledViewData(this.filterModelScheduledView).subscribe({
    //       next: (data: Dashboard) => {
    //         this.dashboardScheduledList(data);
    //       },
    //       error: () => (this.loading = false)
    //     })
    //   );
    // }
    // }
  }

  dashboardList(data: Dashboard) {
    this.dashboardDetail = data;
    this.chartDGDto = data.chartDto.dgChartDto;
    this.chartUtilityDto = data.chartDto.utilityChartDto;
    this.isLoadingFirstTime = false;
    this.doughnutDGChartData = [];
    this.doughnutUtilityChartData = [];
    this.createDGChart();
    this.createUtilityChart();
    this.filterModel.woStatus = this.filterModel.woStatus === 0 ? null : this.filterModel.woStatus;
    this.loading = false;
  }

  updateRescheduleActionList(groupName: string) {
    this.rescheduleActionList.forEach(action => {
      if (groupName === GroupName.TentativeOrSchedule) {
        action.isDisable = action.value === 3 || action.value === 4 || action.value === 5;
      } else if (groupName === GroupName.PendingAndReschedule) {
        action.isDisable = action.value === 1 || action.value === 2 || action.value === 5;
      } else if (groupName === GroupName.OnlyReschedule) {
        action.isDisable = action.value === 1 || action.value === 2 || action.value === 3 || action.value === 4;
      }
    });
  }

  dashboardScheduledList(data: Dashboard) {
    this.loading = true;
    this.dashboardScheduleViewDetail = data;

    // Process each assessment
    this.dashboardScheduleViewDetail.assesmentDashDtos?.forEach((element: any) => {
      // Initialize the unscheduled list and month arrays
      element.unscheduled = [];
      this.months.forEach(month => (element[month.value] = []));

      // Helper function to determine group
      const assignGroup = (woElement: any) => {
        let status = '';
        if (!woElement.dateScheduled) {
          // && (woElement.status === 4 || woElement.status === 3)
          status = GroupName.TentativeOrSchedule;
        }
        if (
          // &&
          // (woElement.status === 4 ||
          //   woElement.status === 3 ||
          //   woElement.status === 2 ||
          //   woElement.status === 6 ||
          //   woElement.status === 7) &&
          woElement.dateScheduled &&
          !woElement.reportCompleteDate &&
          !woElement.isPendingReschedule
        ) {
          status = GroupName.PendingAndReschedule;
        }
        if (woElement.dateScheduled && !woElement.reportCompleteDate && woElement.isPendingReschedule) {
          status = GroupName.OnlyReschedule;
        }
        return status;
      };

      // Helper function to push data to the correct month or unscheduled
      const processWO = (woElement: any, taskType: string, typeStrPrefix: string) => {
        const data = {
          ...woElement,
          assementTypestr: typeStrPrefix + woElement.assementTypestr
        };

        // Determine the correct bucket (unscheduled or month)
        if (woElement.isReschedule && !woElement.rescheduleDate && woElement.status !== 2) {
          element.unscheduled.push(data);
        } else if (this.getDateForWO(woElement)) {
          element[this.months[new Date(this.getDateForWO(woElement)).getMonth()].value].push(data);
        } else if (woElement.status === 4 && woElement.tentativeMonth && !(woElement.rescheduleDate || woElement.dateScheduled)) {
          element[this.months[woElement.tentativeMonth - 1].value].push(data);
        } else if (woElement.status === 4 && !(woElement.rescheduleDate && woElement.dateScheduled)) {
          element.unscheduled.push(data);
        }
      };

      const addOtherKeys = woElement => {
        if (this.selectedWorkOrders?.length > 0) {
          const selectedWO = this.selectedWorkOrders.find(selectedWO => selectedWO.id === woElement.id);
          if (selectedWO) {
            // If it exists, copy properties from selectedWO
            woElement.isSelected = true;
            woElement.isDisable = selectedWO.isDisable;
            woElement.isCheckBox = selectedWO.isCheckBox;
            woElement.groupName = selectedWO.groupName;
          } else {
            woElement.isDisable = false;
            woElement.isCheckBox = woElement.status !== 1 && woElement.status !== 2;
            woElement.isSelected = false;
            woElement.groupName = assignGroup(woElement);
          }
        } else {
          woElement.isDisable = false;
          woElement.isCheckBox = woElement.status !== 1 && woElement.status !== 2;
          woElement.isSelected = false;
          woElement.groupName = assignGroup(woElement);
        }
      };

      // Process Aerial Scan Work Orders
      element.aswo.forEach((aswoElement: any) => {
        aswoElement.woType = 5;
        addOtherKeys(aswoElement);
        if (element.aerialScanTask !== 'NA' && element.aerialScanTask !== 'Alltime') {
          processWO(aswoElement, element.aerialScanTask, 'AERIAL-');
        }
      });

      // Process Inverter PM Work Orders
      element.ipmwo.forEach((ipmwoElement: any) => {
        ipmwoElement.woType = 2;
        addOtherKeys(ipmwoElement);
        if (element.inverterPMTask !== 'NA' && element.inverterPMTask !== 'Alltime') {
          processWO(ipmwoElement, element.inverterPMTask, 'IPM-');
        }
      });

      // Process Electrical IV Work Orders
      element.ivwo.forEach((ivwoElement: any) => {
        ivwoElement.woType = 7;
        addOtherKeys(ivwoElement);
        if (element.electricalIVTask !== 'NA' && element.electricalIVTask !== 'Alltime') {
          processWO(ivwoElement, element.electricalIVTask, 'IV-');
        }
      });

      // Process MVPM Work Orders
      element.mvpmwo.forEach((mvpmwoElement: any) => {
        mvpmwoElement.woType = 3;
        addOtherKeys(mvpmwoElement);
        if (element.mvpmTask !== 'NA' && element.mvpmTask !== 'Alltime') {
          processWO(mvpmwoElement, element.mvpmTask, 'MVPM-');
        }
      });

      // Process MVTH Work Orders
      element.mvthwo.forEach((mvthwoElement: any) => {
        mvthwoElement.woType = 14;
        addOtherKeys(mvthwoElement);
        if (element.mvpmTask !== 'NA' && element.mvpmTask !== 'Alltime') {
          processWO(mvthwoElement, element.mvpmTask, 'MVTH-');
        }
      });

      // Process Site Visit Work Orders
      element.svwo.forEach((svwoElement: any) => {
        svwoElement.woType = 1;
        addOtherKeys(svwoElement);
        if (element.siteVisitTask !== 'NA' && element.siteVisitTask !== 'Alltime') {
          processWO(svwoElement, element.siteVisitTask, 'SV-');
        }
      });

      // Process Thermal Work Orders
      element.thermalWO.forEach((thermalWOElement: any) => {
        thermalWOElement.woType = 4;
        addOtherKeys(thermalWOElement);
        if (element.thermalTask !== 'NA' && element.thermalTask !== 'Alltime') {
          processWO(thermalWOElement, element.thermalTask, 'THERMAL-');
        }
      });

      // Process TPM Work Orders
      element.tpmwo.forEach((tpmwoElement: any) => {
        tpmwoElement.woType = 12;
        addOtherKeys(tpmwoElement);
        if (element.tpmTask !== 'NA' && element.tpmTask !== 'Alltime') {
          processWO(tpmwoElement, element.tpmTask, 'TPM-');
        }
      });

      // Process Vegetation Work Orders
      element.vgtwo.forEach((vgtwoElement: any) => {
        vgtwoElement.woType = 8;
        addOtherKeys(vgtwoElement);
        if (element.vegetationTask !== 'NA' && element.vegetationTask !== 'Alltime') {
          processWO(vgtwoElement, element.vegetationTask, 'VGT-');
        }
      });

      // Process Electrical VOC Work Orders
      element.vocwo.forEach((vocwoElement: any) => {
        vocwoElement.woType = 6;
        addOtherKeys(vocwoElement);
        if (element.electricalVOCTask !== 'NA' && element.electricalVOCTask !== 'Alltime') {
          processWO(vocwoElement, element.electricalVOCTask, 'VOC-');
        }
      });

      // Process TRQ Work Orders
      element.trqwo.forEach((trqwoElement: any) => {
        trqwoElement.woType = 13;
        addOtherKeys(trqwoElement);
        if (element.trqTask !== 'NA' && element.trqTask !== 'Alltime') {
          processWO(trqwoElement, element.trqTask, 'TRQ-');
        }
      });
    });

    // Handle month click after data processing if selectedMonth exists
    if (this.selectedMonth) {
      this.onMonthClick(this.selectedMonth);
    }

    // Check if there are selected work orders
    if (this.selectedWorkOrders.length) {
      // Disable all work orders not in the same group as the selected one
      // this.dashboardScheduleViewDetail.assesmentDashDtos?.forEach((element: any) => {

      for (const element of this.dashboardScheduleViewDetail.assesmentDashDtos) {
        for (const unscheduleWO of element['unscheduled']) {
          unscheduleWO['isDisable'] =
            unscheduleWO.groupName !== this.selectedWorkOrders[0].groupName || !this.selectedWorkOrders[0].groupName;
        }
        for (const monthName of this.months) {
          for (const mon of element[monthName.value]) {
            mon['isDisable'] = mon.groupName !== this.selectedWorkOrders[0].groupName || !this.selectedWorkOrders[0].groupName;
          }
        }
      }
      this.updateRescheduleActionList(this.selectedWorkOrders[0].groupName);
      this.changeDetectorRef.detectChanges();
    }

    // Filter woStatus appropriately and stop loading
    this.filterModelScheduledView.woStatus = this.filterModelScheduledView.woStatus === 0 ? null : this.filterModelScheduledView.woStatus;
    this.filterModel.woStatus = this.filterModel.woStatus === 0 ? null : this.filterModel.woStatus;

    this.loading = false;
    this.changeDetectorRef.detectChanges();
  }

  getDateForWO(woDetails) {
    return woDetails.status === 1
      ? woDetails.reportCompleteDate
      : woDetails.status === 2
      ? woDetails.workCompleteDate
      : woDetails.status === 3
      ? woDetails.rescheduleDate ?? woDetails.datePerformed
      : woDetails.rescheduleDate ?? woDetails.dateScheduled;
  }

  onPageChange(obj, dashboardView: string) {
    if (dashboardView === 'complianceView') {
      this.currentPage = obj;
      this.filterModel.page = this.currentPage - 1;
    } else {
      this.currentPage = obj;
      this.filterModel.page = this.currentPage - 1;
      this.currentPageForScheduledView = obj;
      this.filterModelScheduledView.page = this.currentPageForScheduledView - 1;
    }
    this.getAllDashboardList(true, dashboardView);
  }

  onChangeSize() {
    if (this.dashboardViewType === 'complianceView') {
      this.filterModel.page = 0;
      this.currentPage = 0;
      this.filterModel.itemsCount = Number(this.pageSize);
    } else {
      this.filterModel.page = 0;
      this.currentPage = 0;
      this.filterModelScheduledView.page = 0;
      this.currentPageForScheduledView = 0;
      this.filterModelScheduledView.itemsCount = Number(this.pageSizeScheduledView);
    }
    this.getAllDashboardList(true, this.dashboardViewType);
  }

  onWOClick(index, portfolioId: number, siteId: number, event: any, month = '', day = '') {
    this.previousIndexOfDraggedElement = index;

    if (!this.isCompactView) {
      if (event?.id) {
        this.loading = true;
        this.isLiDisabled = true;
        this.subscription.add(
          this.dashboardService.getWODetailsById(event.id).subscribe({
            next: res => {
              if (this.isLiDisabled) {
                this.isLiDisabled = false;
              }
              this.eventId = event.id;
              this.WorkOrderDashboard = res;
              this.showWorkOrderData(portfolioId, siteId, event.id);
              this.loading = false;
            },
            error: e => {
              this.isLiDisabled = false;
              this.loading = false;
            }
          })
        );
      }
    } else {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-lg',
        initialState: {
          assessmentDTO: event,
          monthViseWO: event[month],
          isCompactView: this.isCompactView,
          month: month
            ? new Date(Date.parse(month + ' 1, ' + this.currentYear)).toLocaleString('en-US', {
                month: 'long'
              })
            : day
            ? this.selectedMonth
            : '',
          day: day ? new Date(Date.parse(`${this.selectedMonth} 1, ${this.currentYear}`)).getMonth() + 1 + '/' + day : '',
          year: this.currentYear,
          eventId: this.eventId,
          portfolioId: portfolioId,
          siteId: siteId,
          workorderId: event.id
        }
      };
      // using the dashboard service to open the modal as we have multiple modal to show and need to maintain the state.
      this.dashboardService.openDashboardModal(ModalDashboardComponent, ngModalOptions);
    }
  }

  showWorkOrderData(portfolioId, siteId, workorderId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      id: this.eventId,
      class: 'modal-lg',
      initialState: {
        message: { workorders: this.WorkOrderDashboard },
        eventId: this.eventId,
        portfolioId: portfolioId,
        siteId,
        workorderId: workorderId
      }
    };
    // using the dashboard service to open the modal as we have multiple modal to show and need to maintain the state.
    this.dashboardService.openDashboardModal(ModalDashboardComponent, ngModalOptions);
  }

  isTableColShow(col: number): boolean {
    if (this.filterModel.reportTypeIds?.length) {
      for (const i of this.filterModel.reportTypeIds) {
        let i1 = i;
        if (i1 === 5) {
          i1 = 5;
        }
        if (i1 === col) {
          return true;
        }
      }
      return false;
    }
    return true;
  }

  onExport() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    this.subscription.add(
      this.dashboardService.exportDashboard(filterModel).subscribe({
        next: (data: ExportDashboard[]) => {
          const title = 'Dashboard';
          const rows: any = [
            [
              'Customer (Portfolio)',
              'Site',
              'Kwdc',
              'State',
              'SV',
              'IPM',
              'MVPM',
              'TPM',
              'Thermal',
              'Aerial',
              'IV',
              'VOC',
              'VGT',
              'PR',
              'TRQ',
              'MVTH'
            ]
          ];
          for (const i of data) {
            const tempData = [
              i.customerPortfolio ? i.customerPortfolio : '',
              i.siteName ? i.siteName : '',
              i.kWdc ? i.kWdc : '',
              i.state ? i.state : '',
              i.svWoCount,
              i.ipmWoCount,
              i.mvpmWoCount,
              i.tpmWoCount,
              i.thermWoCount,
              i.asWoCount,
              i.ivWoCount,
              i.vocWoCount,
              i.vgtWoCount,
              i.prWoCount,
              i.trqWoCount,
              i.mvthWoCount
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, title);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.currentPageForScheduledView = filterParams.page;
    this.dashboardDetail.assesmentDashDtos = [];
    this.dashboardScheduleViewDetail.assesmentDashDtos = [];
    this.selectedWorkOrders = [];
    if (filterParams.isUnScheduled) {
      this.isSchedulerMode = true;
      this.isCompactView = false;
    }
    this.getAllDashboardList(true, '', filterParams);
  }

  expandView(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog'
    };
    this.isFullView = true;
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  compressView() {
    this.isFullView = false;
    this.modalRef.hide();
  }

  exportScheduleData() {
    const setWORedirectionLink = (assessmentId: number, assessmentType: string, workOrderNumber: string, id: number) =>
      `${environment.baseUrl}/entities/workorders/add?id=${assessmentId}&assementType=${assessmentType}&workOrderNumber=${workOrderNumber}&workOrderId=${id}&backToDashboard=true`;

    const getWORedirectionLink = (i: ExportScheduleDashboard) =>
      isRedirectionUrlEmbeddable(i)
        ? `"=HYPERLINK(""${setWORedirectionLink(i.assessmentId, i.assessmentType, i.workOrderNumber, i.id)}"",""${i.poNumber}"")",`
        : i.poNumber;

    const isRedirectionUrlEmbeddable = (i: ExportScheduleDashboard): boolean =>
      !!(i.poNumber && i.assessmentId && i.assessmentType && i.workOrderNumber && i.id);

    this.loading = true;
    this.subscription.add(
      this.dashboardService.exportScheduleDashboard(this.filterModel).subscribe({
        next: (data: ExportScheduleDashboard[]) => {
          const title = 'Schedule Dashboard';
          const rows: any = [
            [
              'CustomerName',
              'PortfolioName',
              'SiteName',
              'AssessmentType',
              'WorkOrderNumber',
              'WOStatus',
              'TentativeMonth',
              'ScheduledDate',
              'DueDate',
              'DatePerformed',
              'FieldTech',
              'Cost',
              'Cost Adjustment',
              'Notes',
              'PO Number',
              'Invoice Number',
              'SiteId',
              'IsActive',
              'StartDate',
              'Address',
              'City',
              'State',
              'ZipCode',
              'Lat',
              'Long',
              'DCSize',
              'ACSize',
              'NumInvs',
              'NumXFMRs',
              'UtilityOwnedXFMR',
              'NumberOfCombiners',
              'NumberOfModules',
              'NumberOfPanelboards',
              'LadderRequired',
              'LiftRequired',
              'NoticeRequired',
              'PMRequiresTwoTechnicians',
              'WeekendWorkAllowed',
              'IsRoofSite',
              'IsCarportSite',
              'IsGroundFixedSite',
              'IsSingleTrackerSite',
              'IsDualTrackerSite',
              'IsFloatingSite',
              'IsCentralInvSite',
              'IsStringInvSite',
              'IsMicroInvSite',
              'PrimaryInvMFG',
              'PrimaryInvCount',
              'SecondaryInvMFG',
              'SecondaryInvCount',
              'TertiaryInvMFG',
              'TertiaryInvCount'
            ]
          ];
          for (const i of data) {
            const tempData = [
              i.customerName ? i.customerName : '',
              i.portfolioName ? i.portfolioName : '',
              i.siteName ? i.siteName : '',
              i.assessmentType ? i.assessmentType : '',
              i.workOrderNumber ? i.workOrderNumber : '',
              i.woStatus ? i.woStatus : '',
              i.tentativeMonth ? i.tentativeMonth : '',
              i.scheduledDate ? `${this.formateDate(i.scheduledDate)}` : '',
              i.dueDate ? `${this.formateDate(i.dueDate)}` : '',
              i.datePerformed ? `${this.formateDate(i.datePerformed)}` : '',
              i.fieldTech ? i.fieldTech : '',
              i.cost ? this.currencyPipe.transform(i.cost, 'USD') : '',
              i.addOnCost
                ? i.addOnCost < 0
                  ? `(${this.currencyPipe.transform(Math.abs(i.addOnCost), 'USD')})`
                  : this.currencyPipe.transform(i.addOnCost, 'USD')
                : '',
              i.notes ? i.notes : '',
              i.poNumber ? getWORedirectionLink(i) : '',
              i.invoiceNumber ? i.invoiceNumber : '',
              i.siteId ? i.siteId : '',
              i.isActive ? i.isActive : '',
              i.startDate ? `${this.formateDate(i.startDate)}` : '',
              i.address ? i.address : '',
              i.city ? i.city : '',
              i.state ? i.state : '',
              i.zipCode ? i.zipCode : '',
              i.latitude ? i.latitude : '',
              i.logitude ? i.logitude : '',
              i.dcSize ? i.dcSize : 0,
              i.acSize ? i.acSize : 0,
              i.numInvs ? i.numInvs : 0,
              i.numXFMRs ? i.numXFMRs : 0,
              i.utilityOwnedXFMR ? i.utilityOwnedXFMR : '',
              i.numberofCombiners ? i.numberofCombiners : '',
              i.numberofModules ? i.numberofModules : '',
              i.numberofPanelboards ? i.numberofPanelboards : '',
              i.ladderRequired,
              i.liftRequired,
              i.noticeRequired,
              i.pmRequiresTwoTechnicians,
              i.weekendWorkAllowed,
              i.isRoofSite,
              i.isCarportSite,
              i.isGroundFixedSite,
              i.isSingleTrackerSite,
              i.isDualTrackerSite,
              i.isFloatingSite,
              i.isCentralInvSite,
              i.isStringInvSite,
              i.isMicroInvSite,
              i.primaryInvMFG ? i.primaryInvMFG : '',
              i.primaryInvCount ? i.primaryInvCount : '',
              i.secondaryInvMFG ? i.secondaryInvMFG : '',
              i.secondaryInvCount ? i.secondaryInvCount : '',
              i.tertiaryInvMFG ? i.tertiaryInvMFG : '',
              i.tertiaryInvCount ? i.tertiaryInvCount : ''
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, title);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  formateDate(date: Date | string) {
    return this.datePipe.transform(date, AppConstants.fullDateFormat);
  }

  selectFile() {
    this.fileInput.nativeElement.click();
  }

  importData(files) {
    if (files.length > 0) {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('uploadedFile', files[0] as File);
      this.subscription.add(
        this.dashboardService.importDashBoard(formData).subscribe({
          next: (res: any) => {
            const ngModalOptions: ModalOptions = {
              backdrop: 'static',
              keyboard: false,
              animated: true,
              initialState: { message: res.message, title: 'Notice' }
            };
            this.modalRef = this.modalService.show(ShowMessageComponent, ngModalOptions);
            this.loading = false;
            this.modalRef.content.onClose.subscribe(result => {
              this.fileInput.nativeElement.value = '';
              this.loading = false;
            });
          },
          error: (e: any) => {
            this.loading = false;
            this.fileInput.nativeElement.value = '';
            this.alertService.showWarningToast(e.message);
          }
        })
      );
    }
  }

  switchHandler(value: string[]) {
    if (this.dashboardViewType !== value[0]) {
      if (this.filterModel.isUnScheduled) {
        this.isSchedulerMode = true;
        this.isCompactView = false;
      } else {
        this.isCompactView = false;
        this.isSchedulerMode = false;
        this.isMultiSelect = false;
      }
      this.selectedMonth = '';
    }

    this.dashboardViewType = value[0];
    this.storageService.set('dashboardViewType', this.dashboardViewType);
  }

  onMonthClick(clickedMonth: string) {
    const monthValue = this.months.find(element => element.name === clickedMonth).value;
    this.selectedMonth = clickedMonth;

    if (this.days.length) {
      this.dashboardScheduleViewDetail.assesmentDashDtos?.forEach((element: any) => {
        this.days.forEach(day => {
          if (element.hasOwnProperty(day)) {
            delete element.day;
          }
        });
      });
    }

    this.days = [...Array(this.getDaysInMonth(this.months.find(element => element.name === clickedMonth).name)).keys()].map(i => i + 1);

    this.dashboardScheduleViewDetail.assesmentDashDtos?.forEach((element: any) => {
      this.days.forEach(day => (element[day] = []));
      element.unscheduled = element.unscheduled.filter(unscheduledElement => !unscheduledElement.tentativeMonth);

      if (element[monthValue].length) {
        element[monthValue].forEach((subElement: any) => {
          if (subElement.status === 4 && subElement.tentativeMonth && !(subElement.rescheduleDate || subElement.dateScheduled)) {
            element.unscheduled.push(subElement);
          } else {
            element[this.days[new Date(this.getDateForWO(subElement)).getDate() - 1]].push(subElement);
          }
        });
      }
    });
  }

  removeUnscheduledWO() {
    this.selectedMonth = '';
    // Helper function to push data to the correct month or unscheduled
    this.dashboardScheduleViewDetail.assesmentDashDtos?.forEach((element: any) => {
      const processWO = (woElement: any, typeStrPrefix: string) => {
        const data = {
          ...woElement,
          assementTypestr: typeStrPrefix + woElement.assementTypestr
        };

        // Determine the correct bucket (unscheduled or month)
        if (woElement.isReschedule && !woElement.rescheduleDate) {
          // Only push if 'data' does not already exist in 'unscheduled'
          if (!element.unscheduled.some(e => e.id === data.id)) {
            element.unscheduled.push(data);
          }
        } else if (this.getDateForWO(woElement)) {
          const targetMonthArray = element[this.months[new Date(this.getDateForWO(woElement)).getMonth()].value];
          // Only push if 'data' does not already exist in the respective month's array
          if (!targetMonthArray.some(e => e.id === data.id)) {
            targetMonthArray.push(data);
          }
        } else if (woElement.status === 4 && woElement.tentativeMonth && !(woElement.rescheduleDate || woElement.dateScheduled)) {
          const targetMonthArray = element[this.months[woElement.tentativeMonth - 1].value];
          // Only push if 'data' does not already exist in the respective tentative month's array
          if (!targetMonthArray.some(e => e.id === data.id)) {
            targetMonthArray.push(data);
          }
        } else if (woElement.status === 4 && !(woElement.rescheduleDate && woElement.dateScheduled)) {
          // Only push if 'data' does not already exist in 'unscheduled'
          if (!element.unscheduled.some(e => e.id === data.id)) {
            element.unscheduled.push(data);
          }
        }
      };

      // Process Aerial Scan Work Orders
      element.aswo.forEach((aswoElement: any) => {
        aswoElement.woType = 5;
        if (element.aerialScanTask !== 'NA' && element.aerialScanTask !== 'Alltime') {
          processWO(aswoElement, 'AERIAL-');
        }
      });

      // Process Inverter PM Work Orders
      element.ipmwo.forEach((ipmwoElement: any) => {
        ipmwoElement.woType = 2;
        if (element.inverterPMTask !== 'NA' && element.inverterPMTask !== 'Alltime') {
          processWO(ipmwoElement, 'IPM-');
        }
      });

      // Process Electrical IV Work Orders
      element.ivwo.forEach((ivwoElement: any) => {
        ivwoElement.woType = 7;
        if (element.electricalIVTask !== 'NA' && element.electricalIVTask !== 'Alltime') {
          processWO(ivwoElement, 'IV-');
        }
      });

      // Process MVPM Work Orders
      element.mvpmwo.forEach((mvpmwoElement: any) => {
        mvpmwoElement.woType = 3;
        if (element.mvpmTask !== 'NA' && element.mvpmTask !== 'Alltime') {
          processWO(mvpmwoElement, 'MVPM-');
        }
      });

      // Process MVTH Work Orders
      element.mvthwo.forEach((mvthwoElement: any) => {
        mvthwoElement.woType = 14;
        if (element.mvpmTask !== 'NA' && element.mvpmTask !== 'Alltime') {
          processWO(mvthwoElement, 'MVTH-');
        }
      });

      // Process Site Visit Work Orders
      element.svwo.forEach((svwoElement: any) => {
        svwoElement.woType = 1;
        if (element.siteVisitTask !== 'NA' && element.siteVisitTask !== 'Alltime') {
          processWO(svwoElement, 'SV-');
        }
      });

      // Process Thermal Work Orders
      element.thermalWO.forEach((thermalWOElement: any) => {
        thermalWOElement.woType = 4;
        if (element.thermalTask !== 'NA' && element.thermalTask !== 'Alltime') {
          processWO(thermalWOElement, 'THERMAL-');
        }
      });

      // Process TPM Work Orders
      element.tpmwo.forEach((tpmwoElement: any) => {
        tpmwoElement.woType = 12;
        if (element.tpmTask !== 'NA' && element.tpmTask !== 'Alltime') {
          processWO(tpmwoElement, 'TPM-');
        }
      });

      // Process Vegetation Work Orders
      element.vgtwo.forEach((vgtwoElement: any) => {
        vgtwoElement.woType = 8;
        if (element.vegetationTask !== 'NA' && element.vegetationTask !== 'Alltime') {
          processWO(vgtwoElement, 'VGT-');
        }
      });

      // Process Electrical VOC Work Orders
      element.vocwo.forEach((vocwoElement: any) => {
        vocwoElement.woType = 6;
        if (element.electricalVOCTask !== 'NA' && element.electricalVOCTask !== 'Alltime') {
          processWO(vocwoElement, 'VOC-');
        }
      });

      // Process TRQ Work Orders
      element.trqwo.forEach((trqwoElement: any) => {
        trqwoElement.woType = 13;
        if (element.trqTask !== 'NA' && element.trqTask !== 'Alltime') {
          processWO(trqwoElement, 'TRQ-');
        }
      });
    });
  }

  getDaysInMonth(monthName) {
    this.currentMonthNumber = new Date(`${monthName} 1, ${this.currentYear}`).getMonth() + 1;

    return new Date(this.currentYear, this.currentMonthNumber, 0).getDate();
  }

  prevMonth() {
    const index = this.months.findIndex(element => element.name === this.selectedMonth);
    if (index === 0) {
      this.selectedMonth = this.months[this.months.length - 1].name;
    } else {
      this.selectedMonth = this.months[index - 1].name;
    }

    this.onMonthClick(this.selectedMonth);
  }

  nextMonth() {
    const index = this.months.findIndex(element => element.name === this.selectedMonth);
    if (index === this.months.length - 1) {
      this.selectedMonth = this.months[0].name;
    } else {
      this.selectedMonth = this.months[index + 1].name;
    }

    this.onMonthClick(this.selectedMonth);
  }

  onWODropped(event: CdkDragDrop<string[]>, index, month = '', day = '') {
    if (this.currentIndexOfDraggedElement !== this.previousIndexOfDraggedElement) {
      return;
    }

    if (event.previousContainer !== event.container) {
      this.loading = true;

      const data = {
          workOrderId: event.previousContainer.data[event.previousIndex]['id'],
          tentativeMonth: day
            ? null
            : month
            ? new Date(Date.parse(`${month} 1, ${this.currentYear}`)).getMonth() + 1
            : event.previousContainer.data[event.previousIndex]['tentativeMonth'] || this.currentMonthNumber,
          scheduledDate: month
            ? ''
            : this.datePipe.transform(
                new Date(Date.parse(`${this.currentMonthNumber} ${day}, ${this.currentYear}`)),
                AppConstants.fullDateFormat
              )
        },
        previousTentativeMonth = event.previousContainer.data[event.previousIndex]['tentativeMonth'],
        previousScheduledDate = event.previousContainer.data[event.previousIndex]['dateScheduled'];

      event.previousContainer.data[event.previousIndex]['tentativeMonth'] = data.tentativeMonth;
      event.previousContainer.data[event.previousIndex]['dateScheduled'] = data.scheduledDate;

      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);

      this.dashboardService.updateWOScheduleDetail(data).subscribe({
        next: (response: string) => {
          if (day) {
            this.dashboardScheduleViewDetail.assesmentDashDtos[index][
              this.months.find(element => element.name === this.selectedMonth).value
            ].push(event.container.data[event.currentIndex]);
          }

          for (let key in this.dashboardScheduleViewDetail.assesmentDashDtos[index]) {
            if (Array.isArray(this.dashboardScheduleViewDetail.assesmentDashDtos[index][key])) {
              let subIndex = this.dashboardScheduleViewDetail.assesmentDashDtos[index][key].findIndex(
                element => element.id === data.workOrderId
              );
              if (subIndex !== -1) {
                this.dashboardScheduleViewDetail.assesmentDashDtos[index][key][subIndex].dateScheduled = data.scheduledDate;
                this.dashboardScheduleViewDetail.assesmentDashDtos[index][key][subIndex].tentativeMonth = data.tentativeMonth;
              }
            }
          }

          this.alertService.showSuccessToast(response);
          this.loading = false;
        },
        error: () => {
          event.container.data[event.currentIndex]['tentativeMonth'] = previousTentativeMonth;
          event.container.data[event.currentIndex]['dateScheduled'] = previousScheduledDate;

          transferArrayItem(event.container.data, event.previousContainer.data, event.currentIndex, event.previousIndex);

          this.loading = false;
        }
      });
    }
  }

  refreshWorkOrderDetails() {
    const fields = ['dashboardScheduleViewDetail', 'dashboardDetail'];

    const data = this.dashboardService.updatedWODetails;

    // if (new Date(data.scheduledDate).getFullYear() !== this.filterModel.year) {
    //   const index = this.dashboardScheduleViewDetail.assesmentDashDtos.findIndex(element => element.siteId === data.siteId);

    //   for (let key in this.dashboardScheduleViewDetail.assesmentDashDtos[index]) {
    //     if (Array.isArray(this.dashboardScheduleViewDetail.assesmentDashDtos[index][key])) {
    //       let subIndex = this.dashboardScheduleViewDetail.assesmentDashDtos[index][key].findIndex(
    //         element => element.id === data.workOrderId
    //       );
    //       if (subIndex > -1) {
    //         this.dashboardScheduleViewDetail.assesmentDashDtos[index][key].splice(subIndex, 1);
    //       }
    //     }
    //   }

    //   return;
    // }

    fields.forEach(field => {
      const index = this[field].assesmentDashDtos.findIndex(element => element.siteId === data.siteId);

      for (let key in this[field].assesmentDashDtos[index]) {
        if (Array.isArray(this[field].assesmentDashDtos[index][key])) {
          let subIndex = this[field].assesmentDashDtos[index][key].findIndex(element => element.id === data.workOrderId);

          if (subIndex > -1) {
            this[field].assesmentDashDtos[index][key][subIndex].rescheduleDate = data.scheduledDate;
            this[field].assesmentDashDtos[index][key][subIndex].tentativeMonth = data.tentativeMonth === '-' ? null : data.tentativeMonth;

            var woData = { ...this[field].assesmentDashDtos[index][key][subIndex] };

            if (
              field === 'dashboardScheduleViewDetail' &&
              this.months.some(month => month.value === key) &&
              key !== this.months[new Date(data.scheduledDate).getMonth()].value
            ) {
              this[field].assesmentDashDtos[index][key].splice(subIndex, 1);
            }
          } else {
            if (
              field === 'dashboardScheduleViewDetail' &&
              this.months.some(month => month.value === key) &&
              key === this.months[new Date(data.scheduledDate).getMonth()].value
            ) {
              this[field].assesmentDashDtos[index][this.months[new Date(data.scheduledDate).getMonth()].value].push(woData);
            }
          }
        }
      }

      if (this.selectedMonth) {
        this.onMonthClick(this.selectedMonth);
      }
    });
  }

  onCheckBoxChange(event, woDatas: any) {
    // Add selected work order if it's not already in the list
    this.selectedRescheduleAction = null;
    if (event.target.checked && !this.selectedWorkOrders.some(wo => wo.id === woDatas.id)) {
      this.selectedWorkOrders.push(woDatas);
    } else {
      this.selectedWorkOrders = this.selectedWorkOrders.filter(wo => wo.id !== woDatas.id);
    }

    // Check if there are selected work orders
    if (this.selectedWorkOrders.length) {
      // Disable all work orders not in the same group as the selected one
      // this.dashboardScheduleViewDetail.assesmentDashDtos?.forEach((element: any) => {

      for (const element of this.dashboardScheduleViewDetail.assesmentDashDtos) {
        for (const unscheduleWO of element['unscheduled']) {
          unscheduleWO['isDisable'] = unscheduleWO.groupName !== woDatas.groupName || !woDatas.groupName;
        }
        for (const monthName of this.months) {
          for (const mon of element[monthName.value]) {
            mon['isDisable'] = mon.groupName !== woDatas.groupName || !woDatas.groupName;
          }
        }
      }
      this.updateRescheduleActionList(woDatas.groupName);
      this.changeDetectorRef.detectChanges();
    } else {
      // If no work orders are selected, re-enable all work orders
      for (const element of this.dashboardScheduleViewDetail.assesmentDashDtos) {
        for (const unscheduleWO of element['unscheduled']) {
          unscheduleWO['isDisable'] = false;
        }
        for (const monthName of this.months) {
          for (const mon of element[monthName.value]) {
            mon['isDisable'] = false;
          }
        }
      }
      this.rescheduleActionList.forEach(action => {
        action.isDisable = false;
      });
    }
  }

  openBulkActionModal(selectedRescheduleAction) {
    if (selectedRescheduleAction) {
      const workOrderIds: number[] = [];
      for (const workOrder of this.selectedWorkOrders) {
        workOrderIds.push(workOrder.id); // Push the id into the array
      }
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        ignoreBackdropClick: true,
        initialState: {
          actionStatusId: selectedRescheduleAction,
          selectedWorkOrders: this.selectedWorkOrders
        },
        class: 'rescheduler-modal'
      };

      this.modalRef = this.modalService.show(PmScheduleRescheduleBulkActionComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(async res => {
        if (res) {
          if (res.status === 1) this.getAllDashboardList();
          this.selectedWorkOrders = [];
          if (res.status === 0) this.dashboardScheduledList(this.dashboardScheduleViewDetail);
        }
        this.selectedRescheduleAction = null;
      });
    }
  }

  handleSchedulerToggle(toggle, toggleName: string) {
    if (toggleName === 'scheduler') {
      if (!toggle) {
        this.isMultiSelect = false;
      } else {
        this.isCompactView = false;
      }
    } else if (toggleName === 'multiselect') {
      if (toggle) {
        this.isSchedulerMode = true;
        this.isCompactView = false;
      }
    } else if (toggleName === 'compactView') {
      if (toggle) {
        this.isMultiSelect = false;
        this.isSchedulerMode = false;
      }
    }
    this.dashboardScheduledList(this.dashboardScheduleViewDetail);
  }

  ngOnDestroy() {
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
    this.subscription.unsubscribe();
  }
}
