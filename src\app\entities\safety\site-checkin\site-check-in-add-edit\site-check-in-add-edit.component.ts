import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment-timezone';
import { Subscription } from 'rxjs';
import { APP_ROUTES, AppConstants } from '../../../../@shared/constants';
import { Dropdown } from '../../../../@shared/models/dropdown.model';
import { TimeZone } from '../../../../@shared/models/user.model';
import { DateToUsersTimezonePipe } from '../../../../@shared/pipes/date-to-users-timezone.pipe';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { UserService } from '../../../user-management/user.service';
import { AuditData, ListingInfo, SiteCheckInRequestData } from '../site-checkin.model';
import { SiteCheckInService } from '../site-checkin.service';
import { checkAuthorisations } from '../../../../@shared/utils';
import { ROLE_TYPE } from '../../../../@shared/enums';
declare var google: any;

@Component({
  selector: 'sfl-site-check-in-add-edit',
  templateUrl: './site-check-in-add-edit.component.html',
  styleUrls: ['./site-check-in-add-edit.component.scss']
})
export class SiteCheckInAddEditComponent implements OnInit {
  loading = false;
  siteCheckInRequestData: SiteCheckInRequestData = new SiteCheckInRequestData();
  customerData: any = [];
  portData: any = [];
  siteData: any = [];
  userId: number;
  readonly ROUTES = APP_ROUTES;
  subscription: Subscription = new Subscription();
  portfolioList: Array<Dropdown> = new Array<Dropdown>();
  siteList: Array<Dropdown> = new Array<Dropdown>();
  isEdit = false;
  isDetail = false;
  isCreate = false;
  Id: number;
  auditData: AuditData[] = [];
  userName: string;
  siteName: string;
  date: Date;
  customerName: string;
  portfolioName: string;
  dateTimeFormat = AppConstants.dateTimeFormat;
  siteCheck: any;
  user: string[];
  timeZoneList: TimeZone[] = [];
  userMomentDateTimeFormat = AppConstants.momentDateTimeFormat;
  listingInfo: ListingInfo = new ListingInfo();
  offsetName: string;
  options: any;
  showMap: boolean = true;
  mapToReload: number = null;
  googleMapScriptTag = window.document.createElement('script');
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly _location: Location,
    private readonly route: ActivatedRoute,
    private readonly alertService: AlertService,
    private readonly siteCheckInService: SiteCheckInService,
    private readonly storageService: StorageService,
    private readonly userService: UserService,
    private readonly dateToUsersTimezonePipe: DateToUsersTimezonePipe
  ) {}

  ngOnInit(): void {
    this.getTimeZones();
    this.user = this.storageService.get('user').authorities;
    this.route.params.subscribe(params => {
      if (params && params.id && params.mode) {
        this.Id = params.id;
        if (params.mode === 'edit') {
          this.isEdit = true;
          this.isDetail = false;
        } else {
          this.isDetail = true;
          this.isEdit = false;
        }
      } else {
        this.isDetail = false;
        this.isEdit = false;
      }
      this.getAudit();
    });
    this.googleMapScriptTag.src =
      'https://maps.googleapis.com/maps/api/js?key=AIzaSyAObJC1CaYEzUMUBnRUd8yLJlwNF78W5Uo&callback=Function.prototype';
    window.document.body.appendChild(this.googleMapScriptTag);
  }

  goBack() {
    this._location.back();
  }

  getAudit() {
    this.loading = true;
    this.siteCheckInService.getAudit(this.Id).subscribe({
      next: (res: any) => {
        if (res && res.siteCheckInCheckOutAuditDtos.length) {
          res.siteCheckInCheckOutAuditDtos.forEach(e => {
            e.date = new Date(e.date);
            e.utcTimeStamp = new Date(e.utcTimeStamp);
            e['mapObj'] = {
              center: { lat: e?.latitude, lng: e?.longitude },
              zoom: 12
            };
            e['overlays'] = [new google.maps.Marker({ position: { lat: e?.latitude, lng: e?.longitude }, title: 'Konyaalti' })];
            setTimeout(() => {
              this.offsetName = this.timeZoneList.filter(x => x.id === e.siteTimeZoneOffset)[0].name;
            }, 500);
          });
        }
        this.auditData = res.siteCheckInCheckOutAuditDtos;
        this.userName = res.userName;
        this.siteName = res.siteName;
        this.customerName = res.customer;
        this.portfolioName = res.portfolio;
        this.date = new Date(res.date);
        this.userId = res.userId;
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  gotoEditPage() {
    this.isEdit = true;
    this.isDetail = false;
  }

  accordionChange(e, val: string) {
    this[val] = e ? false : true;
  }

  checkInOutUtcDateChanged(event) {
    this.offsetName = this.timeZoneList.filter(x => x.id === event.siteTimeZoneOffset)[0].name;
    const date = this.setDateToTimeZone(event.date, this.offsetName);
    const utcDate = this.getUTCTime(date);
    event.utcTimeStamp = new Date(utcDate.year(), utcDate.month(), utcDate.date(), utcDate.hours(), utcDate.minute());
  }

  setDateToTimeZone(date, offset) {
    return moment(date).tz(offset, true);
  }

  getUTCTime(date) {
    return date.utc();
  }

  submitCheckInForm(siteCheckInForm) {
    this.loading = true;
    const model = { ...siteCheckInForm };
    const date = new Date(model.date);
    model.date = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), 0, 0));
    const utcTimeStamp = new Date(model.utcTimeStamp);
    model.utcTimeStamp = new Date(
      Date.UTC(
        utcTimeStamp.getFullYear(),
        utcTimeStamp.getMonth(),
        utcTimeStamp.getDate(),
        utcTimeStamp.getHours(),
        utcTimeStamp.getMinutes(),
        0,
        0
      )
    );
    // updated the request object based on the API contract - QES-2678
    const requestObject = {
      id: model.id,
      userId: model.userId,
      userName: model.userName,
      siteId: model.siteId,
      customerId: model.customerId,
      customer: model.customer,
      portfolioId: model.portfolioId,
      portfolio: model.portfolio,
      siteName: model.siteName,
      driveTime: Number(model.driveTime),
      reason: model.reason,
      reasonArrays: model.reasonArrays,
      notes: model.notes,
      date: model.date,
      latitude: model.latitude,
      longitude: model.longitude,
      status: model.status,
      statusStr: model.statusStr,
      isDeleted: model.isDeleted,
      utcTimeStamp: model.utcTimeStamp,
      siteTimeZoneOffset: model.siteTimeZoneOffset,
      order: model.order
    };
    this.siteCheckInService.updateAudit(requestObject).subscribe({
      next: (res: any) => {
        this.alertService.showSuccessToast(res.message);
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  submitCheckoutForm(siteCheckOutForm) {
    this.loading = true;
    const model = { ...siteCheckOutForm };
    const date = new Date(model.date);
    model.date = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), 0, 0));
    const utcTimeStamp = new Date(model.utcTimeStamp);
    model.utcTimeStamp = new Date(
      Date.UTC(
        utcTimeStamp.getFullYear(),
        utcTimeStamp.getMonth(),
        utcTimeStamp.getDate(),
        utcTimeStamp.getHours(),
        utcTimeStamp.getMinutes(),
        0,
        0
      )
    );
    // updated the request object based on the API contract - QES-2678
    const requestObject = {
      id: model.id,
      userId: model.userId,
      userName: model.userName,
      siteId: model.siteId,
      customerId: model.customerId,
      customer: model.customer,
      portfolioId: model.portfolioId,
      portfolio: model.portfolio,
      siteName: model.siteName,
      driveTime: Number(model.driveTime),
      reason: model.reason,
      reasonArrays: model.reasonArrays,
      notes: model.notes,
      date: model.date,
      latitude: model.latitude,
      longitude: model.longitude,
      status: model.status,
      statusStr: model.statusStr,
      isDeleted: model.isDeleted,
      utcTimeStamp: model.utcTimeStamp,
      siteTimeZoneOffset: model.siteTimeZoneOffset,
      order: model.order
    };
    this.siteCheckInService.updateAudit(requestObject).subscribe({
      next: (res: any) => {
        this.alertService.showSuccessToast(res.message);
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getTimeZones() {
    this.subscription.add(
      this.userService.getAllTimeZone().subscribe({
        next: (res: TimeZone[]) => {
          this.timeZoneList = res;
        }
      })
    );
  }

  updateMap(mapNumber: number) {
    this.mapToReload = mapNumber;
    if (this.auditData.length) {
      this.showMap = false;
      for (const [index, map] of this.auditData.entries()) {
        if (index === mapNumber) {
          map.latitude = Number(Number(map.latitude).toFixed(10));
          map.longitude = Number(Number(map.longitude).toFixed(10));
          map.date = new Date(map.date);
          map.utcTimeStamp = new Date(map.utcTimeStamp);
          map['mapObj'] = {
            center: { lat: Number(map?.latitude), lng: Number(map?.longitude) },
            zoom: 12
          };
          map['overlays'] = [
            new google.maps.Marker({ position: { lat: Number(map?.latitude), lng: Number(map?.longitude) }, title: 'Konyaalti' })
          ];
          setTimeout(() => {
            this.offsetName = this.timeZoneList.filter(x => x.id === map.siteTimeZoneOffset)[0].name;
            this.showMap = true;
            this.mapToReload = null;
          }, 500);
        }
      }
      this.auditData.forEach(e => {
        e.date = new Date(e.date);
        e.utcTimeStamp = new Date(e.utcTimeStamp);
        e['mapObj'] = {
          center: { lat: Number(e?.latitude), lng: Number(e?.longitude) },
          zoom: 12
        };
        e['overlays'] = [new google.maps.Marker({ position: { lat: Number(e?.latitude), lng: Number(e?.longitude) }, title: 'Konyaalti' })];
        setTimeout(() => {
          this.offsetName = this.timeZoneList.filter(x => x.id === e.siteTimeZoneOffset)[0].name;
          this.showMap = true;
        }, 500);
      });
    }
  }
}
