<nb-card class="performanceDashboardSpinner appSpinner" [nbSpinner]="downloadReportLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex align-items-center">
      <h6>Dashboard</h6>
      <div class="d-flex ms-auto">
        <button
          nbButton
          status="primary"
          size="small"
          *ngIf="
            filterModel.qeSiteId &&
            !systemLoading &&
            !siteProductionLoading &&
            !siteLoading &&
            !insolationLoading &&
            !energyLoading &&
            !inverterLoading &&
            showDownloadButton
          "
          (click)="downloadPerformanceReport()"
        >
          <span class="d-none d-lg-inline-block">Download Report</span>
          <i class="d-inline-block d-lg-none fa-solid fa-download"></i>
        </button>
        <button
          class="ms-2"
          (click)="exportData()"
          nbButton
          status="primary"
          size="small"
          type="button"
          [disabled]="loading"
          *ngIf="chartsData?.tabularData?.length > 0 && selectedList?.site?.id === null"
        >
          <span class="d-none d-md-inline-block">Export</span>
          <i class="d-inline-block d-md-none fa fa-file-export"></i>
        </button>
        <button
          nbButton
          class="ms-2"
          status="primary"
          size="small"
          *ngIf="
            filterModel.qeSiteId &&
            !systemLoading &&
            !siteProductionLoading &&
            !siteLoading &&
            !insolationLoading &&
            !energyLoading &&
            !inverterLoading &&
            showDownloadButton &&
            checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.ANALYST, roleType.PORTFOLIOMANAGER])
          "
          (click)="openRecalculateModal(recalculateTemplate)"
        >
          <span class="d-none d-lg-inline-block">Recalculate</span>
          <i class="d-inline-block d-lg-none fa-solid fa-calculator"></i>
        </button>
        <button
          nbButton
          class="ms-2"
          status="primary"
          size="small"
          *ngIf="filterModel.qeSiteId && userRole[0] !== 'customer'"
          (click)="goToDataTable()"
        >
          <span class="d-none d-lg-inline-block">Data Table</span>
          <i class="d-inline-block d-lg-none fa-solid fa-table"></i>
        </button>
        <button
          nbButton
          status="primary"
          size="small"
          (click)="slideShow()"
          *ngIf="userRole[0] === 'admin'"
          class="d-none d-xl-inline-block ms-2"
        >
          <span class="d-none d-lg-inline-block">Slide Show</span>
          <i class="d-inline-block d-lg-none fa-solid fa-play"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 performanceFilter">
        <div class="form-control-group mb-2">
          <div class="row align-items-center">
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="customer">Customer</label>
              <ng-select
                id="performance-customer-drop-down"
                name="customer"
                [items]="customerList"
                (change)="showBreadcrumb = false; onCustomerDeSelect(); onCustomerSelect($event, false); showDownloadButton = false"
                (clear)="showBreadcrumb = false; onCustomerDeSelect(); showDownloadButton = false"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.customerId"
                notFoundText="No Customer Found"
                placeholder="Select Customer"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="portfolio">Portfolio</label>
              <ng-select
                id="performance-portfolio-drop-down"
                name="portfolio"
                [items]="portfolioList"
                (change)="showBreadcrumb = false; onPortfolioDeSelect(false); onPortfolioSelect($event, false); showDownloadButton = false"
                (clear)="showBreadcrumb = false; onPortfolioDeSelect(); showDownloadButton = false"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.portfolioId"
                [loading]="portfolioLoading"
                notFoundText="No Portfolio Found"
                placeholder="Select Portfolio"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="site">Site</label>
              <ng-select
                id="performance-site-drop-down"
                name="site"
                [items]="siteList"
                (change)="showBreadcrumb = false; onSiteSelectOrDeSelect($event, false); showDownloadButton = false"
                (clear)="showBreadcrumb = false; onSiteSelectOrDeSelect(); showDownloadButton = false"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.qeSiteId"
                [loading]="siteListLoading"
                notFoundText="No Site Found"
                placeholder="Select Site"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="month">Duration</label>
              <ng-select
                id="duration"
                name="duration"
                [items]="durationList"
                bindLabel="name"
                bindValue="name"
                [(ngModel)]="filterModel.duration"
                notFoundText="No Month Found"
                placeholder="Select Month"
                [clearable]="false"
                (change)="onDurationSelect(); showDownloadButton = false"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <div class="row">
                <div class="col-6 pe-1">
                  <label class="label" for="year">Start</label>
                  <input
                    class="form-control search-textbox"
                    [nbDatepicker]="startDate"
                    name="availabilityDate"
                    placeholder="Select Date"
                    id="input-availabilityDate"
                    autocomplete="off"
                    [(ngModel)]="filterModel.startDate"
                    (ngModelChange)="filterModel.duration = 'Custom Date'; setCustomMaxEndDate(); showDownloadButton = false"
                  />
                  <nb-datepicker #startDate></nb-datepicker>
                </div>
                <div class="col-6 ps-1">
                  <label class="label" for="year">End</label>
                  <input
                    class="form-control search-textbox"
                    [nbDatepicker]="endDate"
                    name="availabilityDate"
                    placeholder="Select Date"
                    id="input-availabilityDate"
                    autocomplete="off"
                    [(ngModel)]="filterModel.endDate"
                    (ngModelChange)="filterModel.duration = 'Custom Date'; showDownloadButton = false"
                  />
                  <nb-datepicker #endDate [max]="maxEndDate" [min]="minEndDate"></nb-datepicker>
                </div>
              </div>
            </div>
            <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-2 mt-2 mt-sm-4">
              <div class="row">
                <div class="col-7 col-xxl-5 pe-1">
                  <button
                    nbButton
                    id="performance-view-data-btn"
                    class="w-100"
                    status="primary"
                    size="small"
                    type="button"
                    (click)="viewData()"
                  >
                    View Data
                  </button>
                </div>
                <div class="col-5 col-xxl-4 ps-1">
                  <button
                    nbButton
                    id="performance-clear-filter-btn"
                    class="w-100"
                    status="primary"
                    size="small"
                    type="button"
                    [disabled]="loading"
                    (click)="ClearFilter()"
                  >
                    clear
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12" *ngIf="!isFullView">
        <ng-container *ngTemplateOutlet="template"></ng-container>
      </div>
    </div>
  </nb-card-body>
</nb-card>
<ng-template #template>
  <div class="row" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <div class="col-12 col-sm-11">
      <div class="breadcrumb align-items-center" *ngIf="showBreadcrumb && selectedList?.customer?.id">
        <a class="cursor-pointer" *ngIf="selectedList?.customer?.id" (click)="selectValue(null)"> Home </a>
        <em class="fa fa-angle-right ms-2 mt-1" *ngIf="selectedList?.customer?.id"></em>
        <span class="ms-2" *ngIf="selectedList?.customer?.id && !selectedList?.portfolio?.id">
          <a
            class="me-2"
            [ngClass]="{ pointerDisable: disableBreadcrumbs('previous') }"
            *ngIf="(filterModel.portfolioId || filterModel.qeSiteId || filterModel.customerId) && previousButton.id"
            (click)="!hidePreviousButton && selectValue(previousButton.id, previousButton.for, 'previous')"
          >
            <em class="fa fa-angle-double-left me-1 mt-1"></em>{{ previousButton.text }}
          </a>
          {{ selectedList.customer.name }}
          <a
            class="ms-2"
            [ngClass]="{ pointerDisable: disableBreadcrumbs('next') }"
            *ngIf="(filterModel.portfolioId || filterModel.qeSiteId || filterModel.customerId) && nextButton.id"
            (click)="!hideNextButton && selectValue(nextButton.id, nextButton.for, 'next')"
          >
            {{ nextButton.text }}<em class="fa fa-angle-double-right ms-1 mt-1"></em>
          </a>
        </span>
        <a
          class="ms-2 cursor-pointer"
          *ngIf="selectedList?.customer?.id && selectedList?.portfolio?.id"
          (click)="selectValue(selectedList?.customer?.id, 'customer')"
        >
          {{ selectedList.customer.name }}
        </a>
        <em class="fa fa-angle-right ms-2 mt-1" *ngIf="selectedList?.portfolio?.id"></em>
        <span class="ms-2" *ngIf="selectedList?.portfolio?.id && !selectedList?.site?.id">
          <a
            class="me-2"
            [ngClass]="{ pointerDisable: disableBreadcrumbs('previous') }"
            *ngIf="(filterModel.portfolioId || filterModel.qeSiteId || filterModel.customerId) && previousButton.id"
            (click)="!hidePreviousButton && selectValue(previousButton.id, previousButton.for, 'previous')"
          >
            <em class="fa fa-angle-double-left me-1 mt-1"></em>{{ previousButton.text }}
          </a>
          {{ selectedList.portfolio.name }}
          <a
            class="ms-2"
            [ngClass]="{ pointerDisable: disableBreadcrumbs('next') }"
            *ngIf="(filterModel.portfolioId || filterModel.qeSiteId || filterModel.customerId) && nextButton.id"
            (click)="!hideNextButton && selectValue(nextButton.id, nextButton.for, 'next')"
          >
            {{ nextButton.text }}<em class="fa fa-angle-double-right ms-1 mt-1"></em>
          </a>
        </span>
        <a
          class="ms-2 cursor-pointer"
          *ngIf="selectedList?.portfolio?.id && selectedList?.site?.id"
          (click)="selectValue(selectedList?.portfolio?.id, 'portfolio')"
        >
          {{ selectedList.portfolio.name }}
        </a>
        <em class="fa fa-angle-right ms-2 mt-1" *ngIf="selectedList?.site?.id"></em>
        <span class="ms-2 cursor-pointer" *ngIf="selectedList?.site?.id">
          <a
            class="me-2"
            [ngClass]="{ pointerDisable: disableBreadcrumbs('previous') }"
            *ngIf="(filterModel.portfolioId || filterModel.qeSiteId || filterModel.customerId) && previousButton.id"
            (click)="!hidePreviousButton && selectValue(previousButton.id, previousButton.for, 'previous')"
          >
            <em class="fa fa-angle-double-left me-1 mt-1"></em>{{ previousButton.text }}
          </a>
          {{ selectedList.site.name }}
          <a
            class="ms-2"
            [ngClass]="{ pointerDisable: disableBreadcrumbs('next') }"
            *ngIf="(filterModel.portfolioId || filterModel.qeSiteId || filterModel.customerId) && nextButton.id"
            (click)="!hideNextButton && selectValue(nextButton.id, nextButton.for, 'next')"
          >
            {{ nextButton.text }}<em class="fa fa-angle-double-right ms-1 mt-1"></em>
          </a>
        </span>
      </div>
    </div>
    <div class="col-12 col-sm-1 mb-2 mt-2 mt-sm-0">
      <em
        *ngIf="isFullView"
        aria-hidden="true"
        class="fa fa-compress-alt text-primary cursor-pointer float-end pe-2"
        (click)="compressView()"
      ></em>
      <em
        *ngIf="!isFullView"
        aria-hidden="true"
        class="fa fa-expand-alt text-primary cursor-pointer float-end pe-2"
        (click)="expandView(template)"
      ></em>
    </div>
    <div
      id="fixed-table"
      setTableHeight
      class="col-12 table-responsive mb-2"
      [ngClass]="{
        'min-table-height': !copyFilterModel.qeSiteId && chartsData.tabularData?.length,
        'min-max-table-height': copyFilterModel.qeSiteId && chartsData.tabularData?.length
      }"
    >
      <p-table
        [value]="chartsData.tabularData | sort : tableShortBy : tableShortDirection"
        dataKey="id"
        [globalFilterFields]="[
          'name',
          'actualEnergy',
          'waEnergy',
          'expectedEnergy',
          'insolationActual',
          'insolationMod',
          'insolationVar',
          'estLoss',
          'actWA'
        ]"
        [tableStyle]="{ 'border-collapse': 'separate', 'border-spacing': '0px' }"
        tableStyleClass="primeng-table"
        responsiveLayout="scroll"
        scrollHeight="flex"
      >
        <ng-template pTemplate="header">
          <tr [ngClass]="{ 'table-no-data': !chartsData.tabularData?.length, 'no-sorting': chartsData.tabularData?.length <= 1 }">
            <th
              pSortableColumn="name"
              class="b-top b-left"
              id="Customer"
              *ngIf="!copyFilterModel.customerId && !copyFilterModel.portfolioId && !copyFilterModel.qeSiteId"
            >
              <div class="d-flex align-items-center">
                Customer
                <p-sortIcon field="name" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th
              pSortableColumn="name"
              class="b-top b-left"
              id="Portfolio"
              *ngIf="copyFilterModel.customerId && !copyFilterModel.portfolioId && !copyFilterModel.qeSiteId"
            >
              <div class="d-flex align-items-center">
                Portfolio
                <p-sortIcon field="name" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="name" class="b-top b-left" id="Site" *ngIf="copyFilterModel.customerId && copyFilterModel.portfolioId">
              <div class="d-flex align-items-center">
                Site
                <p-sortIcon field="name" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="actualEnergy" class="text-end b-top b-left" id="ActualEnergy">
              <div class="d-flex align-items-center justify-content-end">
                Actual Energy
                <p-sortIcon field="actualEnergy" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="waEnergy" class="text-end b-top" id="WaEnergy">
              <div class="d-flex align-items-center justify-content-end">
                Weather Adj. Energy
                <p-sortIcon field="waEnergy" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="expectedEnergy" class="text-end b-top b-right" id="ExpectedEnergy">
              <div class="d-flex align-items-center justify-content-end">
                Expected Energy
                <p-sortIcon field="expectedEnergy" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="insolationActual" class="text-end b-top" id="InsolationAct">
              <div class="d-flex align-items-center justify-content-end">
                Actual Insolation
                <p-sortIcon field="insolationActual" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="insolationMod" class="text-end b-top" id="InsolationMod">
              <div class="d-flex align-items-center justify-content-end">
                Modeled Insolation
                <p-sortIcon field="insolationMod" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="insolationVar" class="text-end b-top b-right" id="InsolationVar">
              <div class="d-flex align-items-center justify-content-end">
                Insolation Variance
                <p-sortIcon field="insolationVar" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="estLoss" class="text-end b-top" id="EstLoss">
              <div class="d-flex align-items-center justify-content-end">
                Energy Variance
                <p-sortIcon field="estLoss" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th pSortableColumn="actExp" class="text-end b-top" id="ActExp">
              <div class="d-flex align-items-center justify-content-end">
                Act / Exp
                <p-sortIcon field="actExp" class="ms-2"></p-sortIcon>
              </div>
            </th>
            <th
              pSortableColumn="actWA"
              class="text-end b-top b-right"
              id="ActWa"
              (click)="!filterModel.qeSiteId && mainSort('actWA', tableSortOptionList['actWA'], 'tableShortBy', 'tableShortDirection')"
            >
              <div class="d-flex align-items-center justify-content-end">
                Act / WA
                <p-sortIcon field="actWA" class="ms-2"></p-sortIcon>
              </div>
            </th>
          </tr>
          <tr class="column-filter" *ngIf="chartsData.tabularData?.length > 1">
            <th id="name" class="b-left">
              <p-columnFilter
                class="p-ms-auto"
                type="text"
                field="name"
                matchMode="contains"
                [showClearButton]="false"
                [matchModeOptions]="textMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="actualEnergy" class="b-left">
              <p-columnFilter
                type="numeric"
                field="actualEnergy"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="waEnergy">
              <p-columnFilter
                type="numeric"
                field="waEnergy"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="expectedEnergy" class="b-right">
              <p-columnFilter
                type="numeric"
                field="expectedEnergy"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="insolationActual">
              <p-columnFilter
                type="numeric"
                field="insolationActual"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="insolationMod">
              <p-columnFilter
                type="numeric"
                field="insolationMod"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="insolationVar" class="b-right">
              <p-columnFilter
                type="numeric"
                field="insolationVar"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="estLoss">
              <p-columnFilter
                type="numeric"
                field="estLoss"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="actExp">
              <p-columnFilter
                type="numeric"
                field="actExp"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
            <th id="actWA" class="b-right">
              <p-columnFilter
                type="numeric"
                field="actWA"
                matchMode="equals"
                [maxFractionDigits]="2"
                [showClearButton]="false"
                [matchModeOptions]="numericMatchModeOptions"
              ></p-columnFilter>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-data let-i="rowIndex">
          <tr>
            <td class="b-left" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <a class="cursor-pointer" (click)="selectValue(data?.id)">{{ data?.name }}</a>
            </td>
            <td class="text-end b-left" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <span *ngIf="data?.actualEnergy === null">-</span>
              <span *ngIf="data?.actualEnergy !== null">{{ data?.actualEnergy | sflDecimal }}</span>
            </td>
            <td class="text-end" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <span *ngIf="data?.waEnergy === null">-</span>
              <span *ngIf="data?.waEnergy !== null">{{ data?.waEnergy | sflDecimal }}</span>
            </td>
            <td class="text-end b-right" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <span *ngIf="data?.expectedEnergy === null">-</span>
              <span *ngIf="data?.expectedEnergy !== null">{{ data?.expectedEnergy | sflDecimal }}</span>
            </td>
            <td class="text-end" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <span *ngIf="data?.insolationActual === null">-</span>
              <span *ngIf="data?.insolationActual !== null">{{ data?.insolationActual | sflDecimal }}</span>
            </td>
            <td class="text-end" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <span *ngIf="data?.insolationMod === null">-</span>
              <span *ngIf="data?.insolationMod !== null">{{ data?.insolationMod | sflDecimal }}</span>
            </td>
            <td class="text-end b-right" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <span *ngIf="data?.insolationVar === null || data?.insolationVar === 'Infinity'">-</span>
              <span *ngIf="data?.insolationVar !== null && data?.insolationVar !== 'Infinity'">
                {{ data?.insolationVar | sflDecimal }}
              </span>
              <span *ngIf="data?.insolationVar !== null && data?.insolationVar !== 'Infinity' && data?.insolationVar !== 'NaN'"> % </span>
            </td>
            <td class="text-end" [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }">
              <span *ngIf="data?.estLoss === null">-</span>
              <span *ngIf="data?.estLoss !== null">{{ data?.estLoss | sflDecimal }}</span>
            </td>
            <td
              class="text-end"
              [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }"
              [ngStyle]="{
                'background-color': getColorValue(data?.actExp),
                color: '#ffffff'
              }"
            >
              <span *ngIf="data?.actExp === null">-</span>
              <span *ngIf="data?.actExp !== null">{{ data?.actExp | sflDecimal }}</span>
              <span *ngIf="data?.actExp !== null && data?.actExp !== 'NaN'">%</span>
            </td>
            <td
              class="text-end b-right"
              [ngClass]="{ 'b-bottom': i + 1 === chartsData.tabularData.length }"
              [ngStyle]="{
                'background-color': getColorValue(data?.actWA),
                color: '#ffffff'
              }"
            >
              <span *ngIf="data?.actWA === null || !data?.isSiteWA">-</span>
              <span *ngIf="data?.actWA !== null && data?.isSiteWA">{{ data?.actWA | sflDecimal }}</span>
              <span *ngIf="data?.actWA !== null && data?.actWA !== 'NaN' && data?.isSiteWA">%</span>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="10" class="no-record text-center b-left b-bottom b-right">No Data Found</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="row" *ngIf="copyFilterModel.qeSiteId">
    <div class="col-12 col-lg-6 col-xl-4 mb-3">
      <div class="chart-box" [nbSpinner]="systemLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <sfl-system-performance
          [systemData]="chartsData.systemData"
          [inChartsData]="chartsData"
          [isFullView]="false"
        ></sfl-system-performance>
      </div>
    </div>
    <div class="col-12 col-lg-6 col-xl-4 mb-3">
      <div class="chart-box" [nbSpinner]="siteProductionLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <sfl-site-production
          [siteProduction]="chartsData.siteProduction"
          [inChartsData]="chartsData"
          [isFullView]="false"
        ></sfl-site-production>
      </div>
    </div>
    <div class="col-12 col-lg-6 col-xl-4 mb-3">
      <div class="chart-box" [nbSpinner]="siteLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <sfl-site-performance [sitePerformance]="chartsData.sitePerformance" [isFullView]="false"></sfl-site-performance>
      </div>
    </div>
    <div class="col-12 col-lg-6 col-xl-4 mb-3">
      <div class="chart-box" [nbSpinner]="insolationLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <sfl-insolation [insolationData]="chartsData.insolationData" [isFullView]="false"></sfl-insolation>
      </div>
    </div>
    <div class="col-12 col-lg-6 col-xl-4 mb-3">
      <div class="chart-box" [nbSpinner]="energyLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <sfl-energy [energyData]="chartsData.energyData" [isFullView]="false"></sfl-energy>
      </div>
    </div>
    <div class="col-12 col-lg-6 col-xl-4 mb-3">
      <div class="chart-box" [nbSpinner]="inverterLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <sfl-inverter-performance [inverterPerformance]="chartsData.inverterPerformance" [isFullView]="false"></sfl-inverter-performance>
      </div>
    </div>
    <div class="col-12 mb-3">
      <div class="chart-box p-3" [nbSpinner]="heatmapLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <div class="mb-2">
          <strong>
            Inverter Heatmap ({{ copyFilterModel.startDate | date : dateFormat }} - {{ copyFilterModel.endDate | date : dateFormat }})
          </strong>
        </div>
        <div class="table-responsive">
          <table class="table table-hover table-bordered" aria-describedby="Site List">
            <thead>
              <tr>
                <th id="InsolationMod" (click)="sort('deviceName', sortOptionList['deviceName'], 'shortBy', 'shortDirection')">
                  Inverter
                  <span
                    class="fa cursor-pointer float-end"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['deviceName'] === 'desc',
                      'fa-arrow-down': sortOptionList['deviceName'] === 'asc',
                      'icon-selected': shortBy === 'deviceName'
                    }"
                  ></span>
                </th>
                <th
                  class="text-end"
                  id="InsolationVar"
                  (click)="sort('actualEnergy', sortOptionList['actualEnergy'], 'shortBy', 'shortDirection')"
                >
                  Actual Energy (kWh)
                  <span
                    class="fa cursor-pointer ms-2"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['actualEnergy'] === 'desc',
                      'fa-arrow-down': sortOptionList['actualEnergy'] === 'asc',
                      'icon-selected': shortBy === 'actualEnergy'
                    }"
                  ></span>
                </th>
                <th
                  class="text-end"
                  id="InsolationMod"
                  (click)="sort('waAdjusted', sortOptionList['waAdjusted'], 'shortBy', 'shortDirection')"
                >
                  Weather Adj. (kWh)
                  <span
                    class="fa cursor-pointer ms-2"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['waAdjusted'] === 'desc',
                      'fa-arrow-down': sortOptionList['waAdjusted'] === 'asc',
                      'icon-selected': shortBy === 'waAdjusted'
                    }"
                  ></span>
                </th>
                <th class="text-end" (click)="sort('estLoss', sortOptionList['estLoss'], 'shortBy', 'shortDirection')">
                  Energy Variance (kWh)
                  <span
                    class="fa cursor-pointer ms-2"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['estLoss'] === 'desc',
                      'fa-arrow-down': sortOptionList['estLoss'] === 'asc',
                      'icon-selected': shortBy === 'estLoss'
                    }"
                  ></span>
                </th>
                <th class="text-end" id="EstLoss" (click)="sort('pi', sortOptionList['pi'], 'shortBy', 'shortDirection')">
                  PI %
                  <span
                    class="fa cursor-pointer ms-2"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['pi'] === 'desc',
                      'fa-arrow-down': sortOptionList['pi'] === 'asc',
                      'icon-selected': shortBy === 'pi'
                    }"
                  ></span>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of chartsData.inverterHeatmapData | inverterHeatmapSort : shortBy : shortDirection">
                <td>{{ data?.deviceName }}</td>
                <td class="text-end">
                  <span *ngIf="data?.actualEnergy === null">-</span>
                  <span *ngIf="data?.actualEnergy !== null">{{ data?.actualEnergy | sflDecimal }}</span>
                </td>
                <td class="text-end">
                  <span *ngIf="data?.waAdjusted === null">-</span>
                  <span *ngIf="data?.waAdjusted !== null">{{ data?.waAdjusted | sflDecimal }}</span>
                </td>
                <td class="text-end">
                  <span *ngIf="data?.estLoss === null">-</span>
                  <span *ngIf="data?.estLoss !== null">{{ data?.estLoss | sflDecimal }}</span>
                </td>
                <td
                  class="text-end"
                  [ngStyle]="{
                    'background-color': getInverterColorValue(data?.pi),
                    color: '#000000'
                  }"
                >
                  <span *ngIf="data?.pi === null || data?.pi === 'Infinity'">-</span>
                  <span *ngIf="data?.pi !== null && data?.pi !== 'Infinity'">{{ data?.pi | sflDecimal }}</span>
                  <span *ngIf="data?.pi !== null && data?.pi !== 'Infinity'">%</span>
                </td>
              </tr>
              <tr *ngIf="!chartsData.inverterHeatmapData?.length">
                <td colspan="12" class="no-record text-center">No Data Found</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="chart-box p-3" [nbSpinner]="ticketLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
        <div class="mb-2"><strong>Losses</strong></div>
        <div class="table-responsive">
          <table class="table table-hover table-bordered" aria-describedby="Ticket List">
            <thead>
              <tr>
                <th scope="col">Number</th>
                <th scope="col">Device</th>
                <th scope="col" class="w-20">Issue</th>
                <th scope="col" class="w-20">Activity</th>
                <th scope="col">Opened</th>
                <th scope="col">Closed</th>
                <th class="text-end" scope="col">TR</th>
                <th scope="col">Status</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngIf="tickets.length">
                <tr
                  *ngFor="
                    let item of tickets
                      | paginate
                        : {
                            itemsPerPage: filterModel.itemsCount,
                            currentPage: currentPage,
                            totalItems: total
                          };
                    let i = index
                  "
                >
                  <td class="pointerTicketNumberLink" [routerLink]="['/entities/ticket/detail/view/' + item.ticketNumber]">
                    {{ item?.ticketNumber }}
                  </td>
                  <td>{{ item?.deviceLabel }}</td>
                  <td class="w-20">
                    <div *ngIf="item?.issue" nbTooltip="{{ item?.issue }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                      <sfl-read-more [content]="item?.issue"></sfl-read-more>
                    </div>
                  </td>
                  <td class="w-20">
                    <span *ngIf="item?.activityLog" nbTooltip="{{ item?.activityLog }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                      <sfl-read-more [content]="item?.activityLog"></sfl-read-more>
                    </span>
                  </td>
                  <td>
                    {{ item?.open | date : dateFormat }}
                  </td>
                  <td>
                    {{ item?.close | date : dateFormat }}
                  </td>
                  <td class="text-end">
                    <span>{{ item?.truckRoll }} </span>
                  </td>
                  <td class="m-3 text-center">
                    <span>{{ item?.statusStr }} </span>
                  </td>
                </tr>
              </ng-container>
              <tr *ngIf="!tickets.length">
                <td colspan="11" class="no-record text-center">No Ticketed Losses</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #recalculateTemplate>
  <div class="alert-box">
    <div class="modal-header">
      <h4 class="modal-title">Recalculate</h4>
      <button type="button" class="close" aria-label="Close" (click)="onModalClose()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <div class="mb-4">
        <label class="label" for="year">Duration</label>
        <ng-select
          [items]="durationDropdownList"
          bindLabel="name"
          bindValue="name"
          [(ngModel)]="recalculateModel.duration"
          (change)="onDurationChange()"
          placeholder="Select Status"
          [clearable]="false"
        >
        </ng-select>
      </div>
      <div class="row" *ngIf="recalculateModel.duration !== 'Specific Date'">
        <div class="col-6 pe-0">
          <label class="label" for="year">Start</label>
          <input
            class="form-control search-textbox"
            [nbDatepicker]="startDate"
            name="availabilityDate"
            placeholder="Select Date"
            id="input-availabilityDate"
            autocomplete="off"
            [(ngModel)]="recalculateModel.startDate"
            [attr.disabled]="recalculateModel.duration === 'Current Period' ? '' : null"
          />
          <nb-datepicker #startDate></nb-datepicker>
        </div>
        <div class="col-6">
          <label class="label" for="year">End</label>
          <input
            class="form-control search-textbox"
            [nbDatepicker]="endDate"
            name="availabilityDate"
            placeholder="Select Date"
            id="input-availabilityDate"
            autocomplete="off"
            [(ngModel)]="recalculateModel.endDate"
            [attr.disabled]="recalculateModel.duration === 'Current Period' ? '' : null"
          />
          <nb-datepicker #endDate></nb-datepicker>
        </div>
      </div>
      <div class="row" *ngIf="recalculateModel.duration === 'Specific Date'">
        <div class="col-12">
          <label class="label" for="year">Date</label>
          <input
            class="form-control search-textbox"
            [nbDatepicker]="specificDate"
            name="availabilityDate"
            placeholder="Select Date"
            id="input-availabilityDate"
            autocomplete="off"
            [(ngModel)]="recalculateModel.specificDate"
          />
          <nb-datepicker #specificDate></nb-datepicker>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button status="primary" size="small" (click)="submitRecalculate()" nbButton>Submit</button>
    </div>
  </div>
</ng-template>
