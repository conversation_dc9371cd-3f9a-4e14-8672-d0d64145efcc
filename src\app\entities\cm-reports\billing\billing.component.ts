import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FILTER_SECTION_ENUM, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { AllReportDropdown } from '../../../@shared/models/report.model';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { EditRefetchEmit } from '../../performance/data-table/data-table-model';
import { AppliedFilter } from '../../site-device/site-device.model';
import { NavigationBack } from '../../ticket-management/ticket.model';
import {
  TicketBillingFilterList,
  TicketsBilling,
  TicketsBillingDispatchesList,
  TicketsBillingEstimateList,
  TicketsBillingList,
  billingDispatchesList,
  billingEstimateList,
  cmBillingPageFilterKeys
} from '../cm-reports.model';
import { CmReportsService } from '../cm-reports.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-billing',
  templateUrl: './billing.component.html',
  styleUrls: ['./billing.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingComponent implements OnInit {
  loading = false;
  estimatesLoading = true;
  dispatchLoading = true;
  tickets: TicketsBillingList[] = [];
  subscription: Subscription = new Subscription();
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  estimateCurrentPage = 1;
  dispatchesCurrentPage = 1;
  estimatesTotal = 10;
  dispatchesTotal = 10;
  total = 10;
  viewPage = FILTER_PAGE_NAME.CM_BILLING_REPORT_LISTING;
  viewFilterSection = 'ticketsBillingFilterSection';
  sortOptionList = {
    Site: 'asc',
    Number: 'asc',
    Issue: 'asc',
    TotalHours: 'asc',
    TotalMaterialsCost: 'asc',
    EstNumber: 'asc',
    AdditionalHours: 'asc',
    CustomerPortfolio: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    Open: 'desc',
    Close: 'desc',
    TicketType: 'asc'
  };
  filterList = TicketBillingFilterList;
  allReportDropdown = new AllReportDropdown();
  fullDateFormat = AppConstants.fullDateFormat;
  filterDetails: FilterDetails = new FilterDetails();
  monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  userRole = this.storageService.get('user').authorities;
  viewType: 'billingView' | 'dispatchView' = 'billingView';
  selectedRecords: number[] = [];
  billingEstimatesList: billingEstimateList[] = [];
  billingDispatchList: billingDispatchesList[] = [];
  isDetailedExport = false;
  isBillingPage: boolean;
  isFilterBtnClicked: boolean = false;

  constructor(
    private readonly storageService: StorageService,
    private readonly cmService: CmReportsService,
    private readonly commonService: CommonService,
    public readonly datePipe: DatePipe,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initFilterDetails();
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = 'Close';
    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection);
    const localFilterData = this.storageService.get('userDefaultFilter');
    const defaultFilterData = this.storageService.get('user').userFilterSelection;
    this.isFilterDisplay = filterSection;
    let model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (filter) {
      this.filterModel = filter;
      model = JSON.parse(JSON.stringify(this.filterModel));
      if (this.filterModel.openDate && this.filterModel.openDate.start && this.filterModel.openDate.end) {
        model.openDate.start = this.datePipe.transform(this.filterModel.openDate.start, AppConstants.fullDateFormat);
        model.openDate.end = this.datePipe.transform(this.filterModel.openDate.end, AppConstants.fullDateFormat);
      }
      if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
        model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
        model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
      }
      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
      if (this.filterModel.page) {
        this.currentPage = this.filterModel.page + 1;
      }
      if (this.filterModel.itemsCount) {
        this.pageSize = this.filterModel.itemsCount;
      }
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;

      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
    if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, cmBillingPageFilterKeys)) {
      this.sort('Close', 'asc');
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.OPEN_DATE.show = true;
    filterItem.CLOSE_DATE.show = true;
    filterItem.START_DATE.show = true;
    filterItem.END_DATE.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.TICKET_ESTIMATION_STATUS.show = true;
    filterItem.TICKET_ESTIMATION_STATUS.multi = true;
    filterItem.TICKET_TYPE.show = true;
    filterItem.TICKET_TYPE.multi = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.default_sort = 'Site';
    this.filterDetails.filter_item = filterItem;
    this.filterDetails.filterSectionEnum = FILTER_SECTION_ENUM.CM_BILLING_REPORT_LISTING;
  }

  addFilter(event, filterFor: string) {
    if (event) {
      let name = '';
      if (filterFor === this.filterList.openDate || filterFor === this.filterList.closeDate) {
        if (event.name.start && event.name.end) {
          name = `${this.datePipe.transform(event.name.start, AppConstants.fullDateFormat)} - 
          ${this.datePipe.transform(event.name.end, AppConstants.fullDateFormat)}`;
        }
      } else if (event.length > 0) {
        if (event.length > 1) {
          name = `${event[0].name} +${event.length - 1}`;
        } else {
          name = event[0].name;
        }
      }
      if (this.appliedFilter.length) {
        const index = this.appliedFilter.findIndex(item => item.text === filterFor);
        if (index > -1) {
          this.appliedFilter[index].value = name;
        } else {
          this.appliedFilter.push({ text: filterFor, value: name });
        }
      } else {
        this.appliedFilter.push({ text: filterFor, value: name });
      }
    }
  }

  getAllTicketList(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    this.filterModel.startDate = null;
    this.filterModel.endDate = null;
    this.filterModel.ticketEstimateStatusIds = [];
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.openDate && this.filterModel.openDate.start && this.filterModel.openDate.end) {
      model.openDate.start = this.datePipe.transform(this.filterModel.openDate.start, AppConstants.fullDateFormat);
      model.openDate.end = this.datePipe.transform(this.filterModel.openDate.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.openDate = null;
      model.openDate = null;
    }
    if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.closeDate = null;
      model.closeDate = null;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.cmService.getAllTicketBillingList(model).subscribe({
      next: (res: TicketsBilling) => {
        this.bindTickets(res);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  sanitizeHtml(content: string): SafeHtml {
    return this.commonService.sanitizeHtml(content);
  }

  removeHtmlTagsForToolTips(content: string): string {
    return this.commonService.removeHtmlTagsForToolTips(content);
  }

  bindTickets(res: TicketsBilling) {
    setTimeout(() => {
      this.loading = false;
      this.tickets = res && res.ticketBillingReports ? res.ticketBillingReports : [];
      this.total = res && res.totalCount ? res.totalCount : 0;
      this.cdr.detectChanges();
    }, 0);
  }

  onFilterChange() {
    this.resetPage();
    this.getAllTicketList();
  }

  clearFilter(clearList = true) {
    this.resetPage();
    this.filterModel = new CommonFilter();
    this.filterModel.itemsCount = this.pageSize;
    this.appliedFilter = [];
    if (clearList) {
      this.getAllTicketList();
    }
  }

  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
  }

  toggleFilter() {
    this.isFilterDisplay = !this.isFilterDisplay;
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllTicketList();
  }

  openAndCloseDateChanged(event) {
    if ((event && event.start && event.end) || event === null) {
      this.resetPage();
      this.getAllTicketList();
    }
  }

  // Pagesize Change
  onChangeSize() {
    this.currentPage = 0;
    this.filterModel.page = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllTicketList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllTicketList();
  }

  exportData() {
    this.loading = true;
    this.filterModel.startDate = null;
    this.filterModel.endDate = null;
    this.filterModel.ticketEstimateStatusIds = [];
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.openDate && this.filterModel.openDate.start && this.filterModel.openDate.end) {
      model.openDate.start = this.datePipe.transform(this.filterModel.openDate.start, AppConstants.fullDateFormat);
      model.openDate.end = this.datePipe.transform(this.filterModel.openDate.end, AppConstants.fullDateFormat);
    } else {
      model.openDate = null;
    }
    if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
    } else {
      model.closeDate = null;
    }
    model.page = 0;
    model.itemsCount = this.total;
    this.subscription.add(
      this.cmService.exportExclusionData(model).subscribe({
        next: (res: Blob) => {
          if (res) {
            this.downloadFile(res, model);
            this.cdr.detectChanges();
          }
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  downloadFile(res: Blob, model) {
    const closeDate = model.closeDate ? new Date(model.closeDate.end) : new Date();
    const fileName = this.monthNames[closeDate.getMonth()];
    const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
    link.download = `${fileName} ${closeDate.getFullYear()}.xlsx`;
    link.click();
    this.loading = false;
  }

  refreshList(filterParams: CommonFilter) {
    this.isFilterBtnClicked = true;
    this.currentPage = filterParams.page;
    if (this.viewType === 'billingView') {
      this.getAllTicketList(true, filterParams);
    } else {
      this.filterModel = filterParams;
      this.getBillingEstimates(true, filterParams);
      this.getBillingDispatches(true, filterParams);
    }
  }

  openLink(id: number, inNewWindow: boolean) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['../entities/ticket/detail/view', `${id}`], { queryParams: { back: NavigationBack.BILLING } })
    );
    if (inNewWindow) {
      window.open(url, '_blank', 'width=' + screen.availWidth + ',height=' + screen.availHeight);
    } else {
      window.open(url, '_blank');
    }
  }

  switchHandler(value: ['billingView' | 'dispatchView']) {
    this.viewType = value[0];
    if (this.viewType === 'dispatchView') {
      if (!this.filterModel.ticketEstimateStatusIds.length) {
        this.filterModel.ticketEstimateStatusIds = [5];
      }
      const defaultModel: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
      defaultModel.page = 0;
      defaultModel.itemsCount = 100;
      this.billingEstimatesList = [];
      this.billingDispatchList = [];
      this.isBillingPage = true;
      this.getBillingEstimates(true, defaultModel);
      this.getBillingDispatches(true, defaultModel);
    } else {
      this.isBillingPage = false;
    }
  }

  billingEstimatesPageChange(data: EditRefetchEmit) {
    const paginationModel: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    paginationModel.page = Number(data.page);
    paginationModel.itemsCount = Number(data.itemsCount);
    this.getBillingEstimates(true, paginationModel);
  }

  bulkActionEstimatesRefresh(isRefresh: boolean) {
    if (isRefresh) {
      const bulkActionModel: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
      this.getBillingEstimates(true, bulkActionModel);
    }
  }

  getBillingEstimates(saveFilter = true, filterParams?: CommonFilter) {
    this.estimatesLoading = true;
    let estimateFilterModal;
    if (filterParams) {
      estimateFilterModal = filterParams;
    }
    this.estimateCurrentPage = estimateFilterModal.page;
    const model: CommonFilter = JSON.parse(JSON.stringify(estimateFilterModal));
    model.page = model.page === 0 ? Number(model.page) : Number(model.page - 1);
    if (estimateFilterModal.startDate) {
      model.startDate = this.datePipe.transform(estimateFilterModal.startDate, AppConstants.fullDateFormat);
    } else {
      estimateFilterModal.startDate = null;
      model.startDate = null;
    }
    if (estimateFilterModal.endDate) {
      model.endDate = this.datePipe.transform(estimateFilterModal.endDate, AppConstants.fullDateFormat);
    } else {
      estimateFilterModal.endDate = null;
      model.endDate = null;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, estimateFilterModal);
    }
    model.closeDate = null;
    model.openDate = null;
    this.cmService.getBillingEstimatesList(model).subscribe({
      next: (res: TicketsBillingEstimateList) => {
        setTimeout(() => {
          this.billingEstimatesList = res && res.estimateBillingReports ? res.estimateBillingReports : [];
          this.estimatesTotal = res && res.totalCount ? res.totalCount : 0;
          this.estimatesLoading = false;
          this.cdr.detectChanges();
        }, 0);
      },
      error: e => {
        this.estimatesLoading = false;
      }
    });
  }

  billingDispatchesPageChange(data: EditRefetchEmit) {
    const paginationModel: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    paginationModel.page = Number(data.page);
    paginationModel.itemsCount = Number(data.itemsCount);
    this.getBillingDispatches(true, paginationModel);
  }

  // GET getBillingDispatches LIST
  getBillingDispatches(saveFilter = true, filterParams?: CommonFilter) {
    this.dispatchLoading = true;
    let dispatchFilterModal;
    if (filterParams) {
      dispatchFilterModal = filterParams;
    }
    this.dispatchesCurrentPage = dispatchFilterModal.page;
    const model: CommonFilter = JSON.parse(JSON.stringify(dispatchFilterModal));
    model.page = model.page === 0 ? Number(model.page) : Number(model.page - 1);
    if (dispatchFilterModal.startDate) {
      model.startDate = this.datePipe.transform(dispatchFilterModal.startDate, AppConstants.fullDateFormat);
    } else {
      dispatchFilterModal.startDate = null;
      model.startDate = null;
    }
    if (dispatchFilterModal.endDate) {
      model.endDate = this.datePipe.transform(dispatchFilterModal.endDate, AppConstants.fullDateFormat);
    } else {
      dispatchFilterModal.endDate = null;
      model.endDate = null;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, dispatchFilterModal);
    }
    model.closeDate = null;
    model.openDate = null;
    this.cmService.getBillingDispatchesList(model).subscribe({
      next: (res: TicketsBillingDispatchesList) => {
        setTimeout(() => {
          this.billingDispatchList = res && res.ticketSiteBillingReportModels ? res.ticketSiteBillingReportModels : [];
          this.dispatchesTotal = res && res.totalCount ? res.totalCount : 0;
          this.dispatchLoading = false;
          this.cdr.detectChanges();
        }, 0);
      },
      error: e => {
        this.dispatchLoading = false;
      }
    });
  }

  // dispatchViewExportData for GET EXCEL
  dispatchViewExportData() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));

    if (this.filterModel.startDate) {
      model.startDate = this.datePipe.transform(this.filterModel.startDate, AppConstants.fullDateFormat);
    } else {
      this.filterModel.startDate = null;
      model.startDate = null;
    }
    if (this.filterModel.endDate) {
      model.endDate = this.datePipe.transform(this.filterModel.endDate, AppConstants.fullDateFormat);
    } else {
      this.filterModel.endDate = null;
      model.endDate = null;
    }
    model.closeDate = null;
    model.openDate = null;
    const exportParam = {
      ...model,
      isDetailExport: this.isDetailedExport
    };
    this.subscription.add(
      this.cmService.exportBillingDispatchData(exportParam).subscribe({
        next: (res: Blob) => {
          this.loading = false;
          this.downloadBillingDispatchFile(res, model);
          this.cdr.detectChanges();
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  downloadBillingDispatchFile(res: Blob, model) {
    const closeDate = model.endDate ? new Date(model.endDate) : new Date();
    const fileName = this.monthNames[closeDate.getMonth()];
    const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
    link.download = `${fileName} ${closeDate.getFullYear()}.xlsx`;
    link.click();
  }
}
