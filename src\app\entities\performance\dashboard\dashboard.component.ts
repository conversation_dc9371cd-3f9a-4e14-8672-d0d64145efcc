import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { FilterMatchMode } from 'primeng/api';
import { Subscription, forkJoin } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { AllReportDropdown } from '../../../@shared/models/report.model';
import { TowDecimalPipe } from '../../../@shared/pipes/decimal.pipe';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerService } from '../../customer-management/customer.service';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { SiteService } from '../../site-management/site.service';
import { Tickets } from '../../ticket-management/ticket.model';
import { ChartsData, FilterModel, RecalculateModel } from './dashboard.model';
import { DashboardService } from './dashboard.service';
import { SlideShowComponent } from './slide-show/slide-show.component';
import { checkAuthorisations } from '../../../@shared/utils';
import { ROLE_TYPE } from '../../../@shared/enums';

@Component({
  selector: 'sfl-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  providers: [TowDecimalPipe]
})
export class DashboardComponent implements OnInit {
  loading = false;
  portfolioLoading = false;
  siteListLoading = false;
  subscription: Subscription = new Subscription();
  customerList: Dropdown[] = [];
  portfolioList: Dropdown[] = [];
  siteList: Dropdown[] = [];
  filterModel: FilterModel = new FilterModel();
  copyFilterModel: FilterModel = new FilterModel();
  durationList: any = [
    {
      id: 1,
      name: 'Week'
    },
    {
      id: 2,
      name: 'Month'
    },
    {
      id: 3,
      name: 'Previous Month'
    },
    {
      id: 4,
      name: 'Quarter'
    },
    {
      id: 5,
      name: 'Previous Quarter'
    },
    {
      id: 7,
      name: 'Half Year'
    },
    {
      id: 8,
      name: 'Previous Half Year'
    },
    {
      id: 9,
      name: 'Year'
    },
    {
      id: 10,
      name: 'Previous Year'
    },
    {
      id: 11,
      name: 'Custom Date'
    }
  ];
  chartsData: ChartsData = new ChartsData();
  nextChartsData: ChartsData = new ChartsData();
  previousChartsData: ChartsData = new ChartsData();
  energyLoading = true;
  inverterLoading = true;
  siteLoading = true;
  siteProductionLoading = true;
  insolationLoading = true;
  systemLoading = true;
  showDownloadButton = false;
  downloadReportLoading = false;
  loaderList = [
    'loading',
    'energyLoading',
    'inverterLoading',
    'siteLoading',
    'siteProductionLoading',
    'insolationLoading',
    'systemLoading',
    'heatmapLoading',
    'ticketLoading'
  ];
  tickets: Tickets[] = [];
  total = 10;
  selectedList = {
    customer: { id: null, name: null },
    portfolio: { id: null, name: null },
    site: { id: null, name: null }
  };
  modalRef: BsModalRef;
  dateFormat = AppConstants.fullDateFormat;
  userDateFormat = AppConstants.momentDateFormat;
  nextButton = { text: null, for: null, id: null };
  previousButton = { text: null, for: null, id: null };
  heatmapLoading = true;
  ticketLoading = true;
  shortBy = 'deviceName';
  shortDirection = 'asc';
  sortOptionList = {
    deviceName: 'asc',
    waAdjusted: 'asc',
    actualEnergy: 'asc',
    estLoss: 'asc',
    pi: 'asc'
  };
  currentList = ['chartsData', 'tickets'];
  nextList = ['nextChartsData', 'nextTickets'];
  previousList = ['previousChartsData', 'previousTickets'];
  isFullView = false;
  viewPage = 'performanceDashboardPage';
  tableShortBy = 'actualEnergy';
  tableShortDirection = 'asc';
  tableSortOptionList = {
    name: 'asc',
    actualEnergy: 'asc',
    waEnergy: 'asc',
    expectedEnergy: 'asc',
    insolationActual: 'asc',
    insolationMod: 'asc',
    insolationVar: 'asc',
    estLoss: 'asc',
    actExp: 'asc',
    actWA: 'asc'
  };
  hideNextButton = false;
  hidePreviousButton = false;
  showBreadcrumb = false;
  userRole: string;
  workOrderId: number;
  workOrderList: any = [];
  textMatchModeOptions = [
    {
      label: 'Starts With',
      value: FilterMatchMode.STARTS_WITH
    },
    {
      label: 'Contains',
      value: FilterMatchMode.CONTAINS
    },
    {
      label: 'Does Not Contain',
      value: FilterMatchMode.NOT_CONTAINS
    },
    {
      label: 'Ends With',
      value: FilterMatchMode.ENDS_WITH
    },
    {
      label: 'Equals',
      value: FilterMatchMode.EQUALS
    }
  ];

  numericMatchModeOptions = [
    {
      label: 'Equals',
      value: FilterMatchMode.EQUALS
    },
    { label: 'Less Than', value: FilterMatchMode.LESS_THAN },
    { label: 'Less Than Or Equal To', value: FilterMatchMode.LESS_THAN_OR_EQUAL_TO },
    { label: 'Greater Than', value: FilterMatchMode.GREATER_THAN },
    { label: 'Greater Than Or Equal To', value: FilterMatchMode.GREATER_THAN_OR_EQUAL_TO }
  ];
  durationDropdownList = [
    {
      id: 1,
      name: 'Current Period'
    },
    {
      id: 2,
      name: 'Specific Date'
    },
    {
      id: 3,
      name: 'Date Range'
    }
  ];
  recalculateModel = new RecalculateModel();
  filteredcustomerIds = [];
  filteredportfolioIds = [];
  filteredsitesIds = [];
  maxEndDate: Date;
  minEndDate: Date;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly customerService: CustomerService,
    private readonly portfolioService: PortfolioService,
    private readonly siteService: SiteService,
    private readonly dashboardService: DashboardService,
    public readonly datePipe: DatePipe,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly router: Router,
    private readonly cdf: ChangeDetectorRef,
    private readonly twoDecimalPipe: TowDecimalPipe,
    private readonly commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.userRole = this.storageService.get('user').authorities;
    this.onDurationSelect();
    const filterData = this.dashboardService.getViewFilter();
    let filter = this.storageService.get(this.viewPage);
    if (filterData) {
      filter = filterData;
      this.dashboardService.setViewFilter(null);
    }
    if (filter) {
      filter.duration = filter.duration ? filter.duration : 'Week';
      filter.startDate = filter.startDate ? new Date(filter.startDate) : new Date();
      filter.endDate = filter.endDate ? new Date(filter.endDate) : new Date();
    }
    this.filterModel = filter ? filter : this.filterModel;
    this.copyFilterModel = JSON.parse(JSON.stringify(this.filterModel));
    let model: FilterModel = JSON.parse(JSON.stringify(this.filterModel));
    model.startDate = this.datePipe.transform(this.filterModel.startDate, AppConstants.fullDateFormat);
    model.endDate = this.datePipe.transform(this.filterModel.endDate, AppConstants.fullDateFormat);
    const apiArray = [this.customerService.getAllCustomer()];
    const tempObj = ['customerList'];
    const data: AllReportDropdown = new AllReportDropdown();
    data.customerIds = [...this.filterModel.customerIds];
    data.ids = [...this.filterModel.portfolioIds];
    apiArray.push(this.siteService.getAllReportSitesByPortfolioId(data));
    tempObj.push('siteList');
    this.loading = true;
    if (filter) {
      apiArray.push(this.dashboardService.getChartsData(model));
      tempObj.push('chartsData');
      if (this.filterModel.customerId) {
        const data: AllReportDropdown = new AllReportDropdown();
        data.ids = [this.filterModel.customerId];
        apiArray.push(this.portfolioService.getAllReportPortfoliosByCustomerId(data));
        tempObj.push('portfolioList');
      }
      if (this.filterModel.portfolioId) {
        const data: AllReportDropdown = new AllReportDropdown();
        data.customerIds = [this.filterModel.customerId];
        data.ids = [this.filterModel.portfolioId];
        apiArray.push(this.siteService.getAllReportSitesByPortfolioId(data));
        tempObj.push('siteList');
      }
      if (this.filterModel.qeSiteId) {
        apiArray.push(this.dashboardService.getAllTicketList(model));
        tempObj.push('tickets');
      }
      this.getAllLists(apiArray, tempObj);
    } else {
      this.getAllLists(apiArray, tempObj);
    }
  }

  getAllLists(apiArray: any, mapResultList: string[]) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          this[value] = res[index];
          if (value === 'chartsData') {
            this[value].tabularData.forEach(item => {
              for (let key in item) {
                if (item.hasOwnProperty(key)) {
                  if (key !== 'name' && key !== 'isSiteWA' && key !== 'id' && !isNaN(item[key])) {
                    item[key] = parseFloat(this.twoDecimalPipe.transform(item[key].toString()).replace(/,/g, ''));
                  }
                }
              }
            });
          }
        }
        setTimeout(() => {
          if (mapResultList.indexOf('portfolioList') > -1) {
            this.onCustomerSelect({ id: this.filterModel.customerId }, this.filterModel.portfolioId ? false : true, true);
          }
          if (mapResultList.indexOf('siteList') > -1) {
            this.onPortfolioSelect({ id: this.filterModel.portfolioId }, this.filterModel.qeSiteId ? false : true, true);
          }
          if (this.filterModel.qeSiteId) {
            this.onSiteSelectOrDeSelect({ id: this.filterModel.qeSiteId }, true, true);
          }
          this.showDownloadButton = true;
          this.startStopAllLoader(false);
        }, 0);
        this.cdf.detectChanges();
      },
      error: e => {
        this.startStopAllLoader(false);
      }
    });
  }

  filter(event) {
    this.filteredcustomerIds = [];
    this.filteredportfolioIds = [];
    this.filteredsitesIds = [];
    for (const obj of event.filteredValue) {
      if (this.filterModel.customerId === null) {
        this.filteredcustomerIds.push(obj.id);
      } else if (this.filterModel.customerId !== null && this.filterModel.portfolioId === null) {
        this.filteredportfolioIds.push(obj.id);
      } else if (this.filterModel.customerId !== null && this.filterModel.portfolioId !== null) {
        this.filteredsitesIds.push(obj.id);
      }
    }
  }

  getAllCustomer() {
    this.loading = true;
    this.subscription.add(
      this.customerService.getAllCustomer().subscribe({
        next: (res: Dropdown[]) => {
          this.customerList = res.filter(item => item.isActive);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onDurationSelect() {
    const today = new Date();
    const oneDayMillis = 24 * 60 * 60 * 1000;

    const getDateDaysAgo = (days: number) => new Date(today.getTime() - days * oneDayMillis);

    const getStartOfMonth = (year: number, month: number) => new Date(year, month, 1);
    const getEndOfMonth = (year: number, month: number) => new Date(year, month + 1, 0);

    const getQuarterStart = (date: Date) => {
      const quarter = Math.floor(date.getMonth() / 3);
      return new Date(date.getFullYear(), quarter * 3, 1);
    };

    const getQuarterEnd = (startDate: Date) => new Date(startDate.getFullYear(), startDate.getMonth() + 3, 0);

    const getHalfYearStart = (date: Date) => {
      const half = Math.floor(date.getMonth() / 6);
      return new Date(date.getFullYear(), half * 6, 1);
    };

    const getHalfYearEnd = (startDate: Date) => new Date(startDate.getFullYear(), startDate.getMonth() + 6, 0);

    // Ensure the end date doesn't cross into the following year
    const adjustEndDate = (startDate: Date, endDate: Date) => {
      const maxEndDate = new Date(startDate.getFullYear(), 11, 31); // Dec 31 of start date's year
      return endDate > maxEndDate ? maxEndDate : endDate;
    };

    // Set the min and max end date dynamically based on the start date
    const restrictEndDate = (startDate: Date) => {
      const maxEndDate = new Date(startDate.getFullYear(), 11, 31); // Dec 31 of start date's year
      this.maxEndDate = maxEndDate; // Optionally store maxEndDate if needed for UI
    };

    switch (this.filterModel.duration) {
      case 'Week':
        this.filterModel.startDate = getDateDaysAgo(7);
        this.filterModel.endDate = getDateDaysAgo(1); // End of the last week, not today
        break;

      case 'Month':
        this.filterModel.startDate = getStartOfMonth(today.getFullYear(), today.getMonth());
        this.filterModel.endDate = getEndOfMonth(today.getFullYear(), today.getMonth()); // Last day of the month
        break;

      case 'Previous Month':
        this.filterModel.startDate = getStartOfMonth(today.getFullYear(), today.getMonth() - 1);
        this.filterModel.endDate = getEndOfMonth(today.getFullYear(), today.getMonth() - 1); // Last day of the previous month
        break;

      case 'Quarter':
        this.filterModel.startDate = getQuarterStart(today);
        this.filterModel.endDate = getQuarterEnd(this.filterModel.startDate); // Last day of the quarter
        break;

      case 'Previous Quarter':
        this.filterModel.startDate = getQuarterStart(new Date(today.getFullYear(), today.getMonth() - 3));
        this.filterModel.endDate = adjustEndDate(this.filterModel.startDate, getQuarterEnd(this.filterModel.startDate));
        break;

      case 'Half Year':
        this.filterModel.startDate = getHalfYearStart(today);
        this.filterModel.endDate = getHalfYearEnd(this.filterModel.startDate); // Last day of the half-year
        break;

      case 'Previous Half Year':
        this.filterModel.startDate = getHalfYearStart(new Date(today.getFullYear(), today.getMonth() - 6));
        this.filterModel.endDate = adjustEndDate(this.filterModel.startDate, getHalfYearEnd(this.filterModel.startDate));
        break;

      case 'Year':
        this.filterModel.startDate = new Date(today.getFullYear(), 0, 1);
        this.filterModel.endDate = new Date(today.getFullYear(), 11, 31); // Last day of the current year
        break;

      case 'Previous Year':
        this.filterModel.startDate = new Date(today.getFullYear() - 1, 0, 1);
        this.filterModel.endDate = new Date(today.getFullYear() - 1, 11, 31); // Last day of the previous year
        break;
    }
    // Restrict end date within the start date's year
    restrictEndDate(new Date(this.filterModel.startDate));
  }

  onCustomerSelect(event = null, viewData = true, isFirstLoad = false, dataFor = 'current') {
    if (!isFirstLoad) {
      this.onCustomerDeSelect(false, viewData);
    }
    if (event) {
      const index = this.customerList.findIndex(x => x.id === event.id);
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };
      if (this.customerList[index + 1]) {
        this.nextButton = { text: this.customerList[index + 1].name, for: 'customer', id: this.customerList[index + 1].id };
      }
      if (index > 0) {
        this.previousButton = { text: this.customerList[index - 1].name, for: 'customer', id: this.customerList[index - 1].id };
      }
      if (this.filterModel.customerId) {
        if (!isFirstLoad) {
          this.getAllPortfolioByCustomer();
        }
        if (viewData) {
          if (!event.name) {
            event['name'] = this.customerList[index].name;
          }
          this.selectedList.customer.id = event ? event.id : null;
          this.selectedList.customer.name = event ? event.name : null;
        }
      }
      if (viewData) {
        this.viewData(dataFor, isFirstLoad);
      }
    }
  }

  onCustomerDeSelect(clear = true, needRemove = true) {
    if (needRemove) {
      this.portfolioList = [];
      this.siteList = [];
      this.filterModel.portfolioId = null;
      this.filterModel.qeSiteId = null;
      this.selectedList.portfolio = { id: null, name: null };
      this.selectedList.site = { id: null, name: null };
      this.getAllSiteByPortfolio();
    }
    if (clear) {
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };
    }
  }

  getAllPortfolioByCustomer() {
    this.portfolioLoading = true;
    const data: AllReportDropdown = new AllReportDropdown();
    if (this.filterModel.customerId) {
      data.ids.push(this.filterModel.customerId);
    }
    this.subscription.add(
      this.portfolioService.getAllReportPortfoliosByCustomerId(data).subscribe({
        next: (res: Dropdown[]) => {
          this.portfolioList = res.filter(item => item.isActive);
          this.portfolioLoading = false;
        },
        error: _e => {
          this.portfolioLoading = false;
        }
      })
    );
  }

  onPortfolioSelect(event = null, viewData = true, isFirstLoad = false, dataFor = 'current') {
    if (this.portfolioList.length) {
      if (!isFirstLoad) {
        this.onPortfolioDeSelect(false, viewData);
      }
      if (event) {
        const index = this.portfolioList.findIndex(x => x.id === event.id);
        this.previousButton = { text: null, for: null, id: null };
        this.nextButton = { text: null, for: null, id: null };
        if (this.portfolioList[index + 1]) {
          this.nextButton = {
            text: this.getSlicedName(this.portfolioList[index + 1].name),
            for: 'portfolio',
            id: this.portfolioList[index + 1].id
          };
        }
        if (index > 0) {
          this.previousButton = {
            text: this.getSlicedName(this.portfolioList[index - 1].name),
            for: 'portfolio',
            id: this.portfolioList[index - 1].id
          };
        }
        if (this.filterModel.portfolioId) {
          if (!isFirstLoad) {
            this.getAllSiteByPortfolio();
          }
          if (viewData) {
            if (!event.name) {
              event['name'] = this.portfolioList[index].name;
            }
            this.selectedList.portfolio.id = event ? event.id : null;
            this.selectedList.portfolio.name = event ? this.getSlicedName(event.name) : null;
          }
        }
        if (viewData) {
          this.viewData(dataFor, isFirstLoad);
        }
      }
    }
  }

  onPortfolioDeSelect(clear = true, needRemove = true) {
    if (needRemove) {
      this.siteList = [];
      this.filterModel.qeSiteId = null;
      this.selectedList.site = { id: null, name: null };
      this.getAllSiteByPortfolio();
    }
    if (clear) {
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };

      const index = this.customerList.findIndex(x => x.id === this.filterModel.customerId);
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };
      if (this.customerList[index + 1]) {
        this.nextButton = {
          text: this.customerList[index + 1].name,
          for: 'customer',
          id: this.customerList[index + 1].id
        };
      }
      if (index > 0) {
        this.previousButton = {
          text: this.customerList[index - 1].name,
          for: 'customer',
          id: this.customerList[index - 1].id
        };
      }
    }
  }

  getAllSiteByPortfolio() {
    this.siteListLoading = true;
    const data: AllReportDropdown = new AllReportDropdown();
    if (this.filterModel.customerId) {
      data.customerIds.push(this.filterModel.customerId);
    }
    if (this.filterModel.portfolioId) {
      data.ids.push(this.filterModel.portfolioId);
    }
    this.subscription.add(
      this.siteService.getAllReportSitesByPortfolioId(data).subscribe({
        next: (res: Dropdown[]) => {
          this.siteList = res.filter(item => item.isActive);
          this.siteListLoading = false;
        },
        error: _e => {
          this.siteListLoading = false;
        }
      })
    );
  }

  onSiteSelectOrDeSelect(event = null, viewData = true, isFirstLoad = false, dataFor = 'current') {
    if (this.siteList.length) {
      if (!event) {
        const index = this.portfolioList.findIndex(x => x.id === this.filterModel.portfolioId);
        this.previousButton = { text: null, for: null, id: null };
        this.nextButton = { text: null, for: null, id: null };
        if (this.portfolioList[index + 1]) {
          this.nextButton = {
            text: this.getSlicedName(this.portfolioList[index + 1].name),
            for: 'portfolio',
            id: this.portfolioList[index + 1].id
          };
        }
        if (index > 0) {
          this.previousButton = {
            text: this.getSlicedName(this.portfolioList[index - 1].name),
            for: 'portfolio',
            id: this.portfolioList[index - 1].id
          };
        }
      } else {
        const index = this.siteList.findIndex(x => x.id === event.id);
        this.previousButton = { text: null, for: null, id: null };
        this.nextButton = { text: null, for: null, id: null };
        if (this.siteList[index + 1]) {
          this.nextButton = { text: this.getSlicedName(this.siteList[index + 1].name), for: 'site', id: this.siteList[index + 1].id };
        }
        if (index > 0) {
          this.previousButton = { text: this.getSlicedName(this.siteList[index - 1].name), for: 'site', id: this.siteList[index - 1].id };
        }
        if (event && !event.name) {
          event['name'] = this.siteList[index].name;
        }
      }
      this.selectedList.site.id = event ? event.id : null;
      this.selectedList.site.name = event ? this.getSlicedName(event.name) : null;
      if (viewData) {
        this.viewData(dataFor, isFirstLoad);
      }
    }
  }

  ClearFilter() {
    this.siteList = [];
    this.portfolioList = [];
    this.selectedList = {
      customer: { id: null, name: null },
      portfolio: { id: null, name: null },
      site: { id: null, name: null }
    };
    this.filterModel = new FilterModel();
    this.onDurationSelect();
    this.storageService.set(this.viewPage, this.filterModel);
    this.tickets = [];
    this.chartsData = new ChartsData();
    this.getAllSiteByPortfolio();
  }

  viewData(getValueFor = 'current', isFirstLoad = false) {
    this.showBreadcrumb = true;
    let model: FilterModel = JSON.parse(JSON.stringify(this.filterModel));
    model.startDate = this.datePipe.transform(this.filterModel.startDate, AppConstants.fullDateFormat);
    model.endDate = this.datePipe.transform(this.filterModel.endDate, AppConstants.fullDateFormat);
    if (getValueFor === 'current' && !isFirstLoad) {
      this.getAllData(model, 'current');
    }
    if (this.filterModel.customerId) {
      const data = this.customerList.find(x => x.id === this.filterModel.customerId);
      this.selectedList.customer.id = data.id;
      this.selectedList.customer.name = data.name;
    } else {
      this.selectedList.customer.id = null;
      this.selectedList.customer.name = null;
    }
    if (this.filterModel.portfolioId) {
      const data = this.portfolioList.find(x => x.id === this.filterModel.portfolioId);
      this.selectedList.portfolio.id = data.id;
      this.selectedList.portfolio.name = this.getSlicedName(data.name);
    } else {
      this.selectedList.portfolio.id = null;
      this.selectedList.portfolio.name = null;
    }
    if (this.filterModel.qeSiteId) {
      const data = this.siteList.find(x => x.id === this.filterModel.qeSiteId);
      this.selectedList.site.id = data.id;
      this.selectedList.site.name = this.getSlicedName(data.name);
    } else {
      this.selectedList.site.id = null;
      this.selectedList.site.name = null;
    }
    if (this.filterModel.customerId) {
      this.getNextPreviousData(getValueFor);
    }
  }

  getNextPreviousData(getValueFor = 'current') {
    if (this.previousButton.id && (getValueFor === 'current' || getValueFor === 'previous')) {
      let model: FilterModel = JSON.parse(JSON.stringify(this.filterModel));
      model.startDate = this.datePipe.transform(this.filterModel.startDate, AppConstants.fullDateFormat);
      model.endDate = this.datePipe.transform(this.filterModel.endDate, AppConstants.fullDateFormat);
      if (this.previousButton.for === 'customer') {
        model.customerId = this.previousButton.id;
      } else if (this.previousButton.for === 'portfolio') {
        model.portfolioId = this.previousButton.id;
      } else {
        model.qeSiteId = this.previousButton.id;
      }
      this.hidePreviousButton = true;
      setTimeout(() => {
        this.getAllData(model, 'previous');
      }, 5000);
    }
    if (this.nextButton.id && (getValueFor === 'current' || getValueFor === 'next')) {
      let model: FilterModel = JSON.parse(JSON.stringify(this.filterModel));
      model.startDate = this.datePipe.transform(this.filterModel.startDate, AppConstants.fullDateFormat);
      model.endDate = this.datePipe.transform(this.filterModel.endDate, AppConstants.fullDateFormat);
      if (this.nextButton.for === 'customer') {
        model.customerId = this.nextButton.id;
      } else if (this.nextButton.for === 'portfolio') {
        model.portfolioId = this.nextButton.id;
      } else {
        model.qeSiteId = this.nextButton.id;
      }
      this.hideNextButton = true;
      setTimeout(() => {
        this.getAllData(model, 'next');
      }, 3000);
    }
  }

  getAllData(model: FilterModel, getValueFor = 'current') {
    if (getValueFor === 'current') {
      if (model.qeSiteId) {
        this.startStopAllLoader(true);
        setTimeout(() => {
          this.showDownloadButton = true;
        }, 300);
      } else {
        this.loading = true;
      }
    }
    const apiArray = !model.qeSiteId
      ? [this.dashboardService.getChartsData(model)]
      : [this.dashboardService.getChartsData(model), this.dashboardService.getAllTicketList(model)];
    let tempObj = [];
    if (getValueFor === 'current') {
      tempObj = !model.qeSiteId ? ['chartsData'] : this.currentList;
    } else if (getValueFor === 'previous') {
      tempObj = !model.qeSiteId ? ['previousChartsData'] : this.previousList;
    } else if (getValueFor === 'next') {
      tempObj = !model.qeSiteId ? ['nextChartsData'] : this.nextList;
    }
    this.getAllDataLists(apiArray, tempObj, getValueFor);
    this.storageService.set(this.viewPage, this.filterModel);
  }

  getAllDataLists(apiArray: any, mapResultList: string[], getValueFor) {
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        this.copyFilterModel = JSON.parse(JSON.stringify(this.filterModel));
        for (const [index, value] of mapResultList.entries()) {
          this[value] = res[index];
          if (value === 'chartsData') {
            this[value].tabularData.forEach(item => {
              for (let key in item) {
                if (item.hasOwnProperty(key)) {
                  if (key !== 'name' && key !== 'isSiteWA' && key !== 'id' && !isNaN(item[key])) {
                    item[key] = parseFloat(this.twoDecimalPipe.transform(item[key].toString()).replace(/,/g, ''));
                  }
                }
              }
            });
          }
        }
        if (getValueFor === 'previous') {
          this.hidePreviousButton = false;
        }
        if (getValueFor === 'next') {
          this.hideNextButton = false;
        }
        this.cdf.detectChanges();
        this.startStopAllLoader(false);
      },
      error: e => {
        this.startStopAllLoader(false);
      }
    });
  }

  startStopAllLoader(val) {
    setTimeout(() => {
      this.loaderList.forEach(x => (this[x] = val));
      this.cdf.detectChanges();
    }, 0);
  }

  getColorValue(value: number) {
    if (value >= -75 && value < -50) {
      return '#f96732';
    } else if (value >= -50 && value < -15) {
      return '#d08900';
    } else if (value >= -15 && value < -1) {
      return '#52a131';
    } else if (value >= -5) {
      return '#00b359';
    }
    return '#ff262e';
  }

  getInverterColorValue(value: number) {
    if (value > 95) {
      return '#ffffff';
    } else if (value <= 95 && value > 90) {
      return '#fccfd0';
    } else if (value <= 90 && value > 85) {
      return '#fa9fa1';
    } else if (value <= 85 && value > 50) {
      return '#f86f72';
    }
    return '#f64044';
  }

  switchRecord(from, to) {
    for (let i = 0; i < this[from].length; i++) {
      this[this[to][i]] = JSON.parse(JSON.stringify(this[this[from][i]]));
    }
  }

  selectValue(id: number, goTo: string = null, getValueFor = 'current') {
    let forData = 'current';
    if (getValueFor === 'previous') {
      this.switchRecord('currentList', 'nextList');
      this.switchRecord('previousList', 'currentList');
      forData = 'previous';
    } else if (getValueFor === 'next') {
      this.switchRecord('currentList', 'previousList');
      this.switchRecord('nextList', 'currentList');
      forData = 'next';
    }
    if (!id) {
      this.filterModel.customerId = id;
      this.filterModel.portfolioId = id;
      this.filterModel.qeSiteId = id;
      this.copyFilterModel = JSON.parse(JSON.stringify(this.filterModel));
      this.viewData(forData);
      this.selectedList = {
        customer: { id: null, name: null },
        portfolio: { id: null, name: null },
        site: { id: null, name: null }
      };
    } else {
      if (!this.filterModel.customerId || goTo === 'customer') {
        this.filterModel.customerId = id;
        this.copyFilterModel = JSON.parse(JSON.stringify(this.filterModel));
        const data = this.customerList.find(x => x.id === id);
        this.onCustomerSelect(data, true, false, forData);
        this.selectedList.portfolio.id = null;
        this.selectedList.portfolio.name = null;
        this.selectedList.site.id = null;
        this.selectedList.site.name = null;
      } else if (!this.filterModel.portfolioId || goTo === 'portfolio') {
        this.filterModel.portfolioId = id;
        this.copyFilterModel = JSON.parse(JSON.stringify(this.filterModel));
        const data = this.portfolioList.find(x => x.id === id);
        this.onPortfolioSelect(data, true, false, forData);
        this.selectedList.site.id = null;
        this.selectedList.site.name = null;
      } else {
        this.filterModel.qeSiteId = id;
        this.copyFilterModel = JSON.parse(JSON.stringify(this.filterModel));
        const data = this.siteList.find(x => x.id === id);
        this.onSiteSelectOrDeSelect(data, true, false, forData);
      }
    }
  }

  getWorkOrder() {
    this.dashboardService
      .getWorkOrderByYearSiteId({
        year: new Date(this.filterModel.startDate).getFullYear(),
        siteId: this.filterModel.qeSiteId
      })
      .subscribe({
        next: response => {
          this.workOrderList = response;
          this.workOrderId = null;
        }
      });
  }

  sort(sortBy: string, changeSort: string, sortFor: string, sortForDirection) {
    if (this[sortFor] === sortBy) {
      if (changeSort === 'asc') {
        changeSort = 'desc';
      } else {
        changeSort = 'asc';
      }
    }
    this.sortOptionList[sortBy] = changeSort;
    this[sortFor] = sortBy;
    this[sortForDirection] = changeSort;
  }

  expandView(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog'
    };
    this.isFullView = true;
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  compressView() {
    this.isFullView = false;
    this.modalRef.hide();
  }

  goToDataTable() {
    this.dashboardService.setViewFilter(this.filterModel);
    this.router.navigateByUrl('/entities/performance/data-table');
  }

  mainSort(sortBy: string, changeSort: string, sortFor: string, sortForDirection) {
    if (this[sortFor] === sortBy) {
      if (changeSort === 'asc') {
        changeSort = 'desc';
      } else {
        changeSort = 'asc';
      }
    }
    this.tableSortOptionList[sortBy] = changeSort;
    this[sortFor] = sortBy;
    this[sortForDirection] = changeSort;
  }

  slideShow() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full',
      initialState: {
        filterData: this.filterModel
      }
    };
    this.commonService.setAutoLogoutValue(false);
    this.commonService.encryptUserId().subscribe({
      next: response => {
        this.storageService.set(AppConstants.encryptUserId, response);
        this.modalRef = this.modalService.show(SlideShowComponent, ngModalOptions);
      },
      error: _e => {
        this.modalRef = this.modalService.show(SlideShowComponent, ngModalOptions);
      }
    });
  }

  downloadPerformanceReport() {
    const data = { ...this.filterModel };
    data.startDate = this.datePipe.transform(data.startDate, AppConstants.fullDateFormat);
    data.endDate = this.datePipe.transform(data.endDate, AppConstants.fullDateFormat);
    this.downloadReportLoading = true;
    const siteName = this.siteList.find(x => x.id === this.filterModel.qeSiteId).name;
    this.dashboardService.downloadPRReport(data).subscribe({
      next: response => {
        const link = this.commonService.createObject(response, 'application/pdf');
        link.download = `Performance Report(${siteName}_${data.startDate}-${data.endDate}).pdf`;
        link.click();
        this.downloadReportLoading = false;
      },
      error: _e => (this.downloadReportLoading = false)
    });
  }

  disableBreadcrumbs(value: string) {
    if (this.nextButton.id && this.previousButton.id) {
      return !this.hidePreviousButton && !this.hideNextButton ? false : true;
    }

    return value === 'previous' ? this.hidePreviousButton : this.hideNextButton;
  }

  openRecalculateModal(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true
    };
    this.modalRef = this.modalService.show(template, ngModalOptions);
    this.recalculateModel.startDate = this.filterModel.startDate;
    this.recalculateModel.endDate = this.filterModel.endDate;
  }

  onDurationChange() {
    if (this.recalculateModel.duration === 'Current Period') {
      this.recalculateModel.specificDate = '';
      this.recalculateModel.startDate = this.filterModel.startDate;
      this.recalculateModel.endDate = this.filterModel.endDate;
    } else {
      this.recalculateModel.specificDate = '';
      this.recalculateModel.startDate = '';
      this.recalculateModel.endDate = '';
    }
  }

  submitRecalculate() {
    this.downloadReportLoading = true;

    const data = {
      qeSiteId: this.filterModel.qeSiteId,
      startDate: this.datePipe.transform(this.recalculateModel.startDate, AppConstants.fullDateFormat),
      endDate: this.datePipe.transform(this.recalculateModel.endDate, AppConstants.fullDateFormat)
    };

    if (this.recalculateModel.duration === 'Specific Date') {
      data.startDate = this.datePipe.transform(this.recalculateModel.specificDate, AppConstants.fullDateFormat);
      data.endDate = this.datePipe.transform(this.recalculateModel.specificDate, AppConstants.fullDateFormat);
    }

    this.dashboardService.generateProdDataDashboard(data).subscribe({
      next: () => {
        this.viewData();
        this.onModalClose();
        this.downloadReportLoading = false;
      },
      error: () => {
        this.downloadReportLoading = false;
      }
    });
  }

  onModalClose() {
    this.modalRef.hide();
    this.recalculateModel = new RecalculateModel();
  }

  getSlicedName(value: string) {
    return value.substring(0, value.lastIndexOf(' ('));
  }

  downloadFile(res: Blob) {
    const data = { ...this.filterModel };
    data.startDate = this.datePipe.transform(data.startDate, AppConstants.fullDateFormat);
    data.endDate = this.datePipe.transform(data.endDate, AppConstants.fullDateFormat);
    const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
    if (this.filterModel.customerId === null) {
      link.download = `Performance Data (${data.startDate}-${data.endDate}).xlsx`;
    } else if (this.filterModel.customerId !== null && this.filterModel.portfolioId === null) {
      link.download = `${this.selectedList.customer.name} Performance Data (${data.startDate}-${data.endDate}).xlsx`;
    } else if (this.filterModel.customerId !== null && this.filterModel.portfolioId !== null) {
      link.download = `${this.selectedList.portfolio.name} (${this.selectedList.customer.name})  Performance Data (${data.startDate}-${data.endDate}).xlsx`;
    } else {
      link.download = `${this.selectedList.site.name} (${this.selectedList.customer.name}) Performance Data (${data.startDate}-${data.endDate}).xlsx`;
    }
    link.click();
    this.loading = false;
  }

  exportData() {
    this.filterModel.customerIds = [];
    this.filterModel.portfolioIds = [];
    this.filterModel.sitesIds = [];

    this.loading = true;
    if (this.filterModel.customerId === null && this.filteredcustomerIds.length === 0) {
      for (const obj of this.chartsData.tabularData) {
        this.filterModel.customerIds.push(obj.id);
      }
    } else if (this.filterModel.customerId === null && this.filteredcustomerIds.length > 0) {
      this.filterModel.customerIds = this.filteredcustomerIds;
    } else if (this.filterModel.customerId !== null && this.filterModel.portfolioId === null && this.filteredportfolioIds.length === 0) {
      for (const obj of this.chartsData.tabularData) {
        this.filterModel.portfolioIds.push(obj.id);
      }
    } else if (this.filterModel.customerId !== null && this.filterModel.portfolioId === null && this.filteredportfolioIds.length > 0) {
      this.filterModel.portfolioIds = this.filteredportfolioIds;
    } else if (this.filterModel.portfolioId !== null && this.filterModel.customerId !== null && this.filteredsitesIds.length === 0) {
      for (const obj of this.chartsData.tabularData) {
        this.filterModel.sitesIds.push(obj.id);
      }
    } else if (this.filterModel.portfolioId !== null && this.filterModel.customerId !== null && this.filteredsitesIds.length > 0) {
      this.filterModel.sitesIds = this.filteredsitesIds;
    }
    let model: FilterModel = JSON.parse(JSON.stringify(this.filterModel));
    model.startDate = this.datePipe.transform(this.filterModel.startDate, AppConstants.fullDateFormat);
    model.endDate = this.datePipe.transform(this.filterModel.endDate, AppConstants.fullDateFormat);
    this.subscription.add(
      this.dashboardService.exportChartsdata(model).subscribe({
        next: (res: Blob) => {
          if (res) {
            this.downloadFile(res);
          }
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  setCustomMaxEndDate() {
    const startDate = new Date(this.filterModel.startDate);
    this.maxEndDate = new Date(startDate.getFullYear(), 11, 31);
    this.minEndDate = startDate;
    this.filterModel.endDate = this.maxEndDate;
  }
}
