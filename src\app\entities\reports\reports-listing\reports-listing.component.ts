import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { ShowMessageComponent } from '../../../@shared/components/Show-Message/show-message/show-message.component';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants, ErrorMessages } from '../../../@shared/constants';
import {
  AllReportDropdown,
  ExportNonConformanceReport,
  REPORTSFILTERLIST,
  ReportModel,
  ReportModelDTO,
  ReportType,
  pmReportListingPageFilterKeys
} from '../../../@shared/models/report.model';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { ReportService } from '../../report/report.service';
import { ShareModelComponent } from '../../report/share-model/share-model.component';
import { AppliedFilter } from '../../site-device/site-device.model';
import { AddNewReportComponent } from '../add-new-report/add-new-report.component';
import { ViewReportDetailComponent } from '../view-report-detail/view-report-detail.component';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-reports-listing',
  templateUrl: './reports-listing.component.html',
  styleUrls: ['./reports-listing.component.scss']
})
export class ReportsListingComponent implements OnInit, OnDestroy {
  modalRef: BsModalRef;
  reportsData: ReportModel[] = [];
  reportIds: string[] = [];
  total: number;
  currentPage = 1;
  isDatahas: boolean;
  pageSize = AppConstants.rowsPerPage;
  subscription: Subscription = new Subscription();
  loading = false;
  disabled = false;
  showCheckbox = false;
  selectAllCheckbox = false;
  reportTypeList: ReportType[];
  allReportDropdown = new AllReportDropdown();
  temp: number[] = [];
  viewdeletetedbutton = false;
  viewArchUnarchbutton = false;
  user: string;
  viewPage = FILTER_PAGE_NAME.PM_REPORTS_LISTING;
  viewFilterSection = 'reportFilterSection';
  fullDateFormat = AppConstants.fullDateFormat;
  filter = {
    startYear: []
  };
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  filterList = REPORTSFILTERLIST;
  sortOptionList = {
    SiteName: 'asc',
    Year: 'desc',
    CustomerPortfolio: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc'
  };
  filterDetails: FilterDetails = new FilterDetails();
  previewHTML: string;
  previewModalRef: BsModalRef;
  linkData: any;
  linking: boolean = false;
  ENV = environment;
  userRole = this.storageService.get('user').authorities;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly reportService: ReportService,
    private readonly commonService: CommonService,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit() {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection);

    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.reportService.setData(false);
    this.filterModel.direction = 'desc';
    this.filterModel.sortBy = 'Year';
    this.user = this.storageService.get('user').authorities;
    this.filterModel.itemsCount = this.pageSize;
    this.isFilterDisplay = filterSection ? filterSection : false;
    this.initFilterDetails();
    if (this.filterModel.direction && this.filterModel.sortBy) {
      this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
    }
    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }
    if (this.filterModel.isDelete) {
      this.viewdeletetedbutton = this.filterModel.isDelete;
    }
    if (this.filterModel.isArchive) {
      this.viewArchUnarchbutton = this.filterModel.isArchive;
    }

    if (!this.filterModel.years.length) {
      this.filterModel.years = [this.commonService.getCurrentYear()];
    }
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
    if (
      this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, pmReportListingPageFilterKeys)
    ) {
      this.getAllReportsList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.START_YEAR.show = true;
    filterItem.ASSESSMENT_TYPE.show = true;
    filterItem.FREQUENCY_TYPE.show = true;
    filterItem.SHOW_DELETED.show = true;
    filterItem.SHOW_ARCHIVED.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.SITE.multi = true;
    filterItem.START_YEAR.multi = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.default_sort = 'Year';
    this.filterDetails.default_direction = 'desc';
    this.filterDetails.filter_item = filterItem;
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllReportsList(true, filterParams);
    setTimeout(() => {
      this.viewdeletetedbutton = filterParams.isDelete;
      this.viewArchUnarchbutton = filterParams.isArchive;
    }, 0);
  }

  selectDeselectAll() {
    this.reportIds = [];
    this.reportsData.forEach(element => {
      if (element.isCompleted) {
        element.isSelected = this.selectAllCheckbox;
      }
      if (element.isSelected) {
        this.reportIds.push(element.reportId);
      }
    });
  }

  selectReport(event, id) {
    let tempReport = [];
    this.reportIds = this.reportIds.length ? this.reportIds : [];
    this.reportsData.forEach(element => {
      if (element.isCompleted) {
        tempReport.push(element);
      }
    });
    this.selectAllCheckbox = tempReport.every(function (item: any) {
      return item.isSelected === true;
    });
    if (event.target.checked) {
      this.reportIds.push(id);
    } else {
      this.reportIds = this.reportIds.filter(e => {
        return e !== id;
      });
    }
  }

  onDownload() {
    this.loading = true;
    const model = JSON.parse(JSON.stringify(this.filterModel));
    model['reportIds'] = this.selectAllCheckbox ? [] : this.reportIds;
    this.reportService.downloadReports(model).subscribe({
      next: res => {
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          initialState: { message: res.message, title: 'Notice' }
        };
        this.modalRef = this.modalService.show(ShowMessageComponent, ngModalOptions);
        this.loading = false;
        this.modalRef.content.onClose.subscribe(result => {
          this.reportIds = [];
          this.showCheckbox = false;
          this.selectAllCheckbox = false;
          this.reportsData.forEach(element => {
            element.isSelected = false;
          });
        });
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  changeReport(fromCacheFilter = false) {
    if (!fromCacheFilter) {
      this.resetPage();
    }
    this.getAllReportsList();
  }

  // Bind Report Data
  getAllReportsList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
      this.selectAllCheckbox = false;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.reportService.getAllReportListing(this.filterModel).subscribe({
        next: (res: ReportModelDTO) => {
          this.reportsList(res);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  reportsList(data: ReportModelDTO) {
    this.reportsData = data.siteVisitReports;
    this.total = data.totalReport;
    this.loading = false;
  }

  // sorting
  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllReportsList();
  }

  // Page Change
  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllReportsList();
  }

  // Page Size Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllReportsList();
  }

  // Clear workorder, site, portfolio and customer
  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = this.pageSize;
  }

  viewReportDetails(reportId, template: TemplateRef<any>) {
    this.loading = true;
    this.subscription.add(
      this.reportService.checkgeneralImages(reportId).subscribe({
        next: data => {
          if (data) {
            this.loading = false;
            this.alertService.showErrorToast('No more than 8 images permitted!');
          } else {
            this.subscription.add(
              this.reportService.getPreviewReport(reportId).subscribe({
                next: (data: string) => {
                  if (data) {
                    this.loading = false;
                    this.previewHTML = data;
                    this.previewModalRef = this.modalService.show(template, {
                      class: 'modal-xl modal-dialog-right'
                    });
                  }
                },
                error: e => {
                  this.loading = false;
                }
              })
            );
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  downloadZipFile(fileName) {
    this.loading = true;
    this.subscription.add(
      this.reportService.downloadZipFile(fileName).subscribe({
        next: data => {
          if (data) {
            const link = this.commonService.createObject(data, 'application/pdf');
            const finalReportName = fileName;
            link.download = finalReportName;
            link.click();
            this.loading = false;
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onLink() {
    this.loading = true;
    this.subscription.add(
      this.reportService.getReportZipFileRequest().subscribe({
        next: data => {
          this.loading = false;
          this.linkData = data;
          this.linking = true;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  /* To copy any Text */
  imageCopyUrl(workOrderId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-content',
      initialState: {
        message: environment.baseUrl + '/entities/reports/sitevisits/image-gallery/' + workOrderId
      }
    };
    this.modalRef = this.modalService.show(ShareModelComponent, ngModalOptions);
  }

  // Delete Report
  deleteReport(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this report?' }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        this.loading = true;
        if (result) {
          this.subscription.add(
            this.reportService.deleteReport(event).subscribe({
              next: res => {
                this.loading = false;
                if (res) {
                  if (this.currentPage !== 0 && this.reportsData.length === 1) {
                    this.onChangeSize();
                  } else {
                    this.getAllReportsList();
                    this.alertService.showSuccessToast(res.message);
                  }
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = false;
        }
      });
    }
  }

  deleteJHAReport(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this report?' }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        this.loading = true;
        if (result) {
          this.subscription.add(
            this.reportService.deleteJhaReport(event).subscribe({
              next: res => {
                this.loading = false;
                if (res) {
                  if (this.currentPage !== 0 && this.reportsData.length === 1) {
                    this.onChangeSize();
                  } else {
                    this.getAllReportsList();
                    this.alertService.showSuccessToast(res.message);
                  }
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = false;
        }
      });
    }
  }

  // Download Report - PDF
  downloadReport(workOrderId) {
    this.loading = true;
    this.reportService.downloadSitePdfReport(workOrderId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, 'application/pdf');
          const reportName = this.reportsData[0].siteName;
          this.commonService.CreateReportName(this.reportsData[0].siteName, this.reportsData[0].WorkorderName);
          link.download = reportName + '.pdf';
          link.click();
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  // Download Report - PPT
  downloadPptReport(workOrderId, isPpt) {
    this.loading = true;
    this.reportService.downloadSitePptReport(workOrderId).subscribe({
      next: data => {
        this.loading = false;
        if (data) {
          const link = this.commonService.createObject(data, 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
          const reportName = this.commonService.CreateReportName(this.reportsData[0].siteName, this.reportsData[0].WorkorderName);
          link.download = reportName + '.pptx';
          link.click();
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  // view report detail
  viewReport(id, reportId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        reportId: reportId,
        id: id,
        viewdeletetedbutton: this.viewdeletetedbutton
      }
    };
    this.modalRef = this.modalService.show(ViewReportDetailComponent, ngModalOptions);
  }

  // view report detail
  addNewReport() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: { message: 'Are you sure want to delete this report?' }
    };
    this.modalRef = this.modalService.show(AddNewReportComponent, ngModalOptions);
    this.modalRef.content.event.subscribe(res => {
      if (res) {
        this.getAllReportsList();
        this.loading = false;
      }
    });
  }

  imageJhaCopyUrl(workOrderId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-content',
      initialState: {
        message: environment.baseUrl + '/entities/reports/jhas/image-gallery/' + workOrderId
      }
    };
    this.modalRef = this.modalService.show(ShareModelComponent, ngModalOptions);
  }

  restoreSVReport(id) {
    if (id) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to restore this report?' }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        this.loading = true;
        if (result) {
          this.subscription.add(
            this.reportService.alldeletedReportRestorebyId(id).subscribe({
              next: res => {
                this.loading = false;
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.getAllReportsList();
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = false;
        }
      });
    }
  }

  restoreJHAReport(id) {
    if (id) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to restore this report?' }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        this.loading = true;
        if (result) {
          this.subscription.add(
            this.reportService.alljhadeletedReportRestorebyId(id).subscribe({
              next: res => {
                this.loading = false;
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.getAllReportsList();
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = false;
        }
      });
    }
  }

  toggleFilter() {
    this.isFilterDisplay = !this.isFilterDisplay;
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
  }

  addNCReport() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    this.subscription.add(
      this.reportService.exportNCReport(filterModel).subscribe({
        next: (data: ExportNonConformanceReport[]) => {
          const tittle = 'Non-Conformance_Item';
          const rows: any = [
            [
              'Customer',
              'Portfolio',
              'Site',
              'Report Type',
              'Urgent',
              'Component',
              'Issue/Observation',
              'Actions/Recommendations',
              'Location/Device Name',
              'Resolved',
              'Estimated Hours',
              'Special Tools',
              'Materials Estimate',
              'Lift',
              '# of People Required'
            ]
          ];
          for (const i of data) {
            const tempData = [
              i.customer,
              i.portfolio,
              i.site,
              i.reportName,
              i.urgent === true ? 'X' : '',
              i.component,
              i.issue,
              i.actions,
              i.location,
              i.resolved === true ? 'Yes' : 'No',
              i.estimatedHours ? i.estimatedHours : '',
              i.specialTools ? i.specialTools : '',
              i.materialsEstimate ? `$ ${i.materialsEstimate}` : '',
              i.isLiftRequired === true ? 'Yes' : 'No',
              i.numberOfPeopleRequired ? i.numberOfPeopleRequired : ''
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  archiveUnarchiveReportById(id) {
    if (id) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: this.filterModel.isArchive === true ? ErrorMessages.unArchiveMessage : ErrorMessages.archiveMessage }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        this.loading = true;
        if (result) {
          this.subscription.add(
            this.reportService.archieveUnarchievebyId(id).subscribe({
              next: res => {
                this.loading = false;
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.getAllReportsList();
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = false;
        }
      });
    }
  }

  bulkArchive() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message:
          this.filterModel.isArchive === true
            ? 'Are you sure you want to unarchive ' + this.total + ' reports ?'
            : 'Are you sure you want to archive ' + this.total + ' reports ?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      this.loading = true;
      if (result) {
        this.subscription.add(
          this.reportService.getAllArchieveUnarchieve(this.filterModel).subscribe({
            next: res => {
              this.alertService.showSuccessToast(res.message);
              this.getAllReportsList();
            },
            error: e => {
              this.loading = false;
            }
          })
        );
      } else {
        this.loading = false;
      }
    });
  }

  Back() {
    this.linking = false;
  }

  // Track by
  trackByFunction(index: number, element) {
    return element ? index : null;
  }

  // Destroy
  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
    this.subscription.unsubscribe();
  }
}
