import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Subscription, forkJoin } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import {
  DeviceListModel,
  DeviceModel,
  ImportSiteDevice,
  ListOfAutomationSiteDevice,
  ListOfDeviceType,
  ListOfSelectedQEDevice,
  ListOfSites,
  ListOfUnit
} from '../site-device.model';
import { SiteDeviceService } from '../site-device.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-import-device',
  templateUrl: './import-device.component.html',
  styleUrls: ['./import-device.component.scss']
})
export class ImportDeviceComponent implements OnInit {
  ComponentTitle = 'Import Devices From Data Source';
  filterModel: CommonFilter = new CommonFilter();
  loading = true;
  modelList = [];
  deviceListModel: DeviceListModel = new DeviceListModel();
  deviceModel: DeviceModel = new DeviceModel();
  listOfDeviceType: ListOfDeviceType[] = [];
  siteList: ListOfSites[] = [];
  siteInformation = true;
  dataSource: any = [];
  mfg: any;
  noDcLoad = [];
  qEDeviceName = [];
  listOfReportingUnit: ListOfUnit[] = [];
  listOfPlottingUnit: ListOfUnit[] = [];
  subscription: Subscription = new Subscription();
  isDeviceImported = false;
  listOfRejectedQEDevice: ListOfAutomationSiteDevice[] = [];
  userRoll: string;

  sortOptionList = {
    DeviceName: 'asc',
    DeviceID: 'asc',
    QEDeviceName: 'asc'
  };
  constructor(
    public readonly siteDeviceService: SiteDeviceService,
    private readonly alertService: AlertService,
    private readonly _location: Location,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.userRoll = this.storageService.get('user').authorities;
    this.loading = false;
    const prePopulateData: ImportSiteDevice = this.siteDeviceService.getImportDevicePrePopulateData();
    if (prePopulateData) {
      this.deviceModel.automationDataSourceId = prePopulateData.automationDataSourceId;
      this.deviceModel.automationSiteDetailId = Number(prePopulateData.automationSiteDetailId);
      this.siteDeviceService.setImportDevicePrePopulateData(null);
    }
    this.getDataSource();
  }

  trackByIndex(index: number, obj: any): any {
    return index;
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
  }

  getDataSource() {
    this.subscription.add(
      this.siteDeviceService.getDataSource(false).subscribe({
        next: (res: any) => {
          this.dataSource = res.listOfAutomationDataSource;
          if (this.deviceModel.automationDataSourceId) {
            this.onDataSourceSelect();
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onDataSourceSelect(_e: any = null) {
    if (_e) {
      this.deviceModel.automationSiteDetailId = null;
    }
    this.deviceModel.siteId = null;
    this.deviceModel.customerId = null;
    this.deviceModel.portfolioId = null;
    this.getSite();
  }

  getSite() {
    this.loading = true;
    this.subscription.add(
      this.siteDeviceService.getSite(this.deviceModel.automationDataSourceId).subscribe({
        next: (res: ListOfSites[]) => {
          this.siteList = res;
          if (!this.deviceModel?.automationSiteDetailId && this.siteList.length) {
            this.deviceModel.automationSiteDetailId = this.siteList[0].id;
          }
          if (this.deviceModel.automationSiteDetailId) {
            this.onSiteSelect(this.siteList.find(x => x.id === this.deviceModel.automationSiteDetailId));
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onSiteSelect(e: ListOfSites) {
    this.loading = true;
    this.deviceModel.customerId = null;
    this.deviceModel.portfolioId = null;
    this.deviceModel.siteId = e.siteId;
    this.subscription.add(
      this.siteDeviceService.getDeviceCustomer(e.id).subscribe({
        next: (res: DeviceListModel) => {
          if (this.listOfRejectedQEDevice.length) {
            for (const i of this.listOfRejectedQEDevice) {
              const index = res.listOfAutomationSiteDevice.findIndex(x => x.hardwareId === i.hardwareId);
              if (index > -1) {
                res.listOfAutomationSiteDevice[index] = i;
              }
            }
          }
          this.deviceListModel = res;
          this.listOfDeviceType = res.listOfDeviceType;
          for (const [index, value] of res.listOfAutomationSiteDevice.entries()) {
            if (value.deviceTypeId === 12 || value.deviceTypeId === 24) {
              this.noDcLoad[index] = true;
            } else {
              this.noDcLoad[index] = false;
            }
          }
          this.getAllDeviceList();
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getAllDeviceList() {
    for (const [index, value] of this.deviceListModel.listOfAutomationSiteDevice.entries()) {
      setTimeout(() => {
        this.getDataByDeviceType(value.deviceTypeId, this.deviceModel.siteId, index);
      }, 0);
    }
    this.isDeviceImported = true;
  }

  onDeviceTypeSelect(e, i: number) {
    this.deviceListModel.listOfAutomationSiteDevice[i].qEDeviceName = null;
    this.deviceListModel.listOfAutomationSiteDevice[i].deviceModelId = null;
    this.deviceListModel.listOfAutomationSiteDevice[i].plottingUnit = null;
    this.deviceListModel.listOfAutomationSiteDevice[i].reportingUnit = null;
    this.deviceListModel.listOfAutomationSiteDevice[i].mfg = null;
    this.deviceListModel.listOfAutomationSiteDevice[i].size = null;
    this.modelList[i] = [];
    this.qEDeviceName[i] = [];
    if (e.id === 24 || e.id === 12) {
      this.noDcLoad[i] = true;
    } else {
      this.noDcLoad[i] = false;
    }
    this.getDataByDeviceType(e.id, this.deviceModel.siteId, i);
    this.checkSelection(i);
  }

  getDataByDeviceType(id: number, siteId: number, index: number) {
    const tempArray = [
      this.siteDeviceService.getDevicePlottingReporting(siteId, id, this.deviceModel.automationSiteDetailId),
      this.siteDeviceService.getDeviceQeName(siteId, id)
    ];
    const tempArrayObj = ['listOfPlottingUnit', 'qEDeviceName'];
    this.getAllLists(tempArray, tempArrayObj, index);
  }

  getAllLists(apiArray: any, mapResultList: string[], i: number) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          if (value === 'listOfPlottingUnit') {
            this.mapListOfUnits(res[index], i);
          } else if (value === 'qEDeviceName') {
            this.mapListOfModels(res[index], i);
          }
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  mapListOfUnits(res, index: number) {
    this.deviceListModel.listOfAutomationSiteDevice[index].plottingUnit = res.defaultPlottingUnit;
    this.deviceListModel.listOfAutomationSiteDevice[index].reportingUnit = res.defaultReportingUnit;
    this.listOfPlottingUnit[index] = res.listOfPlottingUnit;
    this.listOfReportingUnit[index] = res.listOfReportingUnit;
  }

  mapListOfModels(res, index: number) {
    this.qEDeviceName[index] = [];
    this.modelList[index] = res;
    for (const i of this.modelList[index]) {
      for (const j of i.listOfQESiteDevice) {
        this.qEDeviceName[index].push(j);
      }
    }
  }

  onModelSelect(e, i, onDeviceChange = false) {
    this.deviceListModel.listOfAutomationSiteDevice[i].deviceModelName = e.deviceModel;
    this.deviceListModel.listOfAutomationSiteDevice[i].mfg = e.mfg;
    this.deviceListModel.listOfAutomationSiteDevice[i].size = e.size;
    if (!onDeviceChange) {
      this.deviceListModel.listOfAutomationSiteDevice[i].siteDeviceId = null;
      this.deviceListModel.listOfAutomationSiteDevice[i].qEDeviceName = null;
      this.deviceListModel.listOfAutomationSiteDevice[i].rank = null;
      this.deviceListModel.listOfAutomationSiteDevice[i].dcLoad = null;
      this.deviceListModel.listOfAutomationSiteDevice[i].serialNumber = null;
    }
    this.qEDeviceName[i] = e.listOfQESiteDevice;
    this.checkSelection(i);
  }

  addNewDeviceName = (name: string) => {
    return { siteDeviceName: name, tag: true, siteDeviceId: -1 };
  };

  addNewModel = (name: string) => {
    if (checkAuthorisations([ROLE_TYPE.ADMIN])) {
      return { deviceModel: name, tag: true, equipmentId: -1 };
    }
  };

  onDeviceNameSelect(e, i) {
    if (!this.deviceListModel.listOfAutomationSiteDevice[i].deviceModelId) {
      this.deviceListModel.listOfAutomationSiteDevice[i].deviceModelId = e.equipmentId;
      this.onModelSelect(this.modelList[i].filter(x => x.equipmentId === e.equipmentId)[0], i, true);
    }
    this.deviceListModel.listOfAutomationSiteDevice[i].siteDeviceId = e.siteDeviceId;
    this.deviceListModel.listOfAutomationSiteDevice[i].rank = e.rank;
    this.deviceListModel.listOfAutomationSiteDevice[i].dcLoad = e.dcLoad;
    this.deviceListModel.listOfAutomationSiteDevice[i].serialNumber = e.serialNumber;
    this.checkSelection(i);
  }

  checkSelection(index: number) {
    let val = false;
    if (
      this.deviceListModel.listOfAutomationSiteDevice[index].deviceModelId &&
      this.deviceListModel.listOfAutomationSiteDevice[index].qEDeviceName
    ) {
      if (this.deviceListModel.listOfAutomationSiteDevice[index].deviceModelId === -1) {
        if (this.deviceListModel.listOfAutomationSiteDevice[index].size && this.deviceListModel.listOfAutomationSiteDevice[index].mfg) {
          val = true;
        }
      } else {
        val = true;
      }
    }
    this.deviceListModel.listOfAutomationSiteDevice[index].isSelected = val;
  }

  importDevices() {
    const selectedDeviceList: ListOfAutomationSiteDevice[] = this.deviceListModel.listOfAutomationSiteDevice.filter(
      x => x.isSelected === true
    );
    if (selectedDeviceList.length) {
      this.loading = true;
      this.deviceModel.customerId = this.deviceListModel.customerId;
      this.deviceModel.portfolioId = this.deviceListModel.portfolioId;
      for (const i of selectedDeviceList) {
        const tempObj: ListOfSelectedQEDevice = new ListOfSelectedQEDevice();
        tempObj.deviceId = i.hardwareId;
        tempObj.deviceName = i.deviceName;
        tempObj.siteDeviceId = i.siteDeviceId;
        tempObj.deviceTypeId = Number(i.deviceTypeId);
        tempObj.deviceModelId = Number(i.deviceModelId);
        tempObj.deviceModelName = i.deviceModelName;
        tempObj.qEDeviceName = i.qEDeviceName;
        tempObj.dcLoad = i.dcLoad ? Number(i.dcLoad) : null;
        tempObj.serialNumber = i.serialNumber ? i.serialNumber : null;
        tempObj.plottingUnit = Number(i.plottingUnit);
        tempObj.reportingUnit = Number(i.reportingUnit);
        tempObj.rank = i.rank ? i.rank : null;
        tempObj.mfg = i.mfg;
        tempObj.size = i.size;
        this.deviceModel.listOfSelectedQEDevice.push(tempObj);
      }
      this.subscription.add(
        this.siteDeviceService.importDevices(this.deviceModel).subscribe({
          next: res => {
            if (res.message) {
              this.alertService.showSuccessToast(res.message);
            }
            this.listOfRejectedQEDevice = [];
            if (res.listOfId && res.listOfId.length) {
              for (const i of res.listOfId) {
                const data: ListOfAutomationSiteDevice = this.deviceListModel.listOfAutomationSiteDevice.find(x => x.hardwareId === i);
                this.listOfRejectedQEDevice.push(data);
              }
            }
            this.deviceModel.listOfSelectedQEDevice = [];
            this.goBack();
            this.onSiteSelect({ id: this.deviceModel.automationSiteDetailId, siteId: this.deviceModel.siteId, name: '' });
          },
          error: e => {
            if (e.message) {
              this.alertService.showErrorToast(e.message);
            }
            this.loading = false;
          }
        })
      );
    } else {
      this.alertService.showWarningToast('Please select device to import.');
    }
  }

  goBack() {
    this.siteDeviceService.setOpenTabName('Device List');
    this._location.back();
  }
}
