import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { SiteAuditReportEditComponent } from './site-audit-report-edit/site-audit-report-edit.component';
import { SiteAuditReportImageGalleryComponent } from './site-audit-report-image-gallery/site-audit-report-image-gallery.component';
import { SiteAuditReportListingComponent } from './site-audit-report-listing/site-audit-report-listing.component';
import { SiteAuditUploadComponent } from './site-audit-upload/site-audit-upload.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: SiteAuditReportListingComponent,
    data: { pageTitle: 'Site Audit' }
  },
  {
    path: 'edit/:id/:type',
    component: SiteAuditReportEditComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Update Site Audit'
    }
  },
  {
    path: 'image-gallery/:id',
    component: SiteAuditReportImageGalleryComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Image Gallery'
    }
  },
  {
    path: 'upload/:id',
    component: SiteAuditUploadComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Upload Image'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SiteAuditReportRoutingModule {}
