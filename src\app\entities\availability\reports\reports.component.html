<nb-card class="availabilityReportSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Exclusions</h6>
        <div class="ms-auto">
          <button
            class="linear-mode-button me-2"
            nbButton
            status="primary"
            size="small"
            type="button"
            *ngIf="
              reportsData?.customerReport?.exclusionReports?.length ||
              reportsData?.portfolioReport?.exclusionReports?.length ||
              reportsData?.siteReport?.exclusionReports?.length ||
              exclusionReportsData?.exclusionReports?.length
            "
            (click)="exportToPdf()"
          >
            Export to PDF
          </button>
          <button
            class="linear-mode-button"
            nbButton
            status="primary"
            size="small"
            type="button"
            *ngIf="
              reportsData?.customerReport?.exclusionReports?.length ||
              reportsData?.portfolioReport?.exclusionReports?.length ||
              reportsData?.siteReport?.exclusionReports?.length ||
              exclusionReportsData?.exclusionReports?.length
            "
            (click)="exportToExcel()"
          >
            Export to EXCEL
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 performanceFilter">
        <div class="form-control-group mb-3">
          <div class="row align-items-center">
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label d-flex" for="customer">
                Customer
                <nb-toggle
                  id="availability-customer-toggle"
                  *ngIf="filterModel.reportType === 'Availability Report'"
                  class="ms-2"
                  [(checked)]="filterModel.isCustomerSelected"
                  (checkedChange)="toggleChange($event, 'isCustomerSelected')"
                  [disabled]="checkAuthorisationsFn([roleType.FIELDTECH, roleType.ANALYST, roleType.PORTFOLIOMANAGER, roleType.MANAGER])"
                  status="primary"
                ></nb-toggle>
              </label>
              <ng-select
                id="availability-customer-drop-down"
                name="customer"
                [items]="customerList"
                [multiple]="true"
                (change)="onCustomerChange()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.customerIds"
                notFoundText="No Customer Found"
                placeholder="Select Customer"
                appendTo="body"
                (search)="onDropdownSearchFilter($event, 'filteredCustomerIds')"
                (close)="filteredCustomerIds = []"
                [closeOnSelect]="false"
              >
                <ng-template ng-header-tmp *ngIf="customerList && customerList.length">
                  <button type="button" (click)="selectUnselectAllCustomer(true)" class="btn btn-sm btn-primary">Select all</button>
                  <button type="button" (click)="selectUnselectAllCustomer(false)" class="btn btn-sm btn-primary ms-1">Unselect all</button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label d-flex" for="portfolio">
                Portfolio
                <nb-toggle
                  id="availability-portfolio-toggle"
                  *ngIf="filterModel.reportType === 'Availability Report'"
                  class="ms-2"
                  [(checked)]="filterModel.isPortfolioSelected"
                  (checkedChange)="toggleChange($event, 'isPortfolioSelected')"
                  status="primary"
                  [disabled]="checkAuthorisationsFn([roleType.FIELDTECH, roleType.ANALYST, roleType.PORTFOLIOMANAGER, roleType.MANAGER])"
                ></nb-toggle>
              </label>
              <ng-select
                id="availability-portfolio-drop-down"
                name="portfolio"
                [multiple]="true"
                [items]="portfolioList"
                (change)="onPortfolioChange()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.portfolioIds"
                notFoundText="No Portfolio Found"
                placeholder="Select Portfolio"
                appendTo="body"
                [closeOnSelect]="false"
                (search)="onDropdownSearchFilter($event, 'filteredPortfolioIds')"
                (close)="filteredPortfolioIds = []"
              >
                <ng-template ng-header-tmp *ngIf="portfolioList && portfolioList.length">
                  <button type="button" (click)="selectUnselectAllPortfolio(true)" class="btn btn-sm btn-primary">Select all</button>
                  <button type="button" (click)="selectUnselectAllPortfolio(false)" class="btn btn-sm btn-primary ms-1">
                    Unselect all
                  </button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                    >
                      {{ item.name }}
                    </span>
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value h-28px" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label d-flex" for="site">
                Site
                <nb-toggle
                  id="availability-site-toggle"
                  *ngIf="filterModel.reportType === 'Availability Report'"
                  class="ms-2"
                  [(checked)]="filterModel.isSiteSelected"
                  (checkedChange)="toggleChange($event, 'isSiteSelected')"
                  status="primary"
                  [disabled]="checkAuthorisationsFn([roleType.FIELDTECH, roleType.ANALYST, roleType.PORTFOLIOMANAGER, roleType.MANAGER])"
                >
                </nb-toggle>
              </label>
              <ng-select
                id="availability-site-drop-down"
                name="site"
                [multiple]="true"
                [items]="siteList"
                (change)="onSiteChange()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.siteIds"
                notFoundText="No Site Found"
                placeholder="Select Site"
                appendTo="body"
                [virtualScroll]="true"
                [closeOnSelect]="false"
                (search)="onDropdownSearchFilter($event, 'filteredSiteIds')"
                (close)="filteredSiteIds = []"
              >
                <ng-template ng-header-tmp *ngIf="siteList && siteList.length">
                  <button type="button" (click)="selectUnselectAllSite(true)" class="btn btn-sm btn-primary">Select all</button>
                  <button type="button" (click)="selectUnselectAllSite(false)" class="btn btn-sm btn-primary ms-1">Unselect all</button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label" for="reportType">Report Type</label>
              <ng-select
                id="reportType"
                class="sfl-track-drop-down"
                name="reportType"
                (change)="reportTypeChange()"
                [items]="reportTypeLists"
                bindLabel="name"
                bindValue="name"
                [(ngModel)]="filterModel.reportType"
                notFoundText="No Report Type Found"
                placeholder="Select Report Type"
                [clearable]="false"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label" for="year">Year</label>
              <ng-select
                id="startYear"
                class="sfl-track-drop-down"
                name="startYear"
                (change)="onYearSelect($event)"
                [items]="years"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.year"
                notFoundText="No Year Found"
                placeholder="Select Year"
                [clearable]="false"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 d-md-none d-lg-block"></div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label" for="interval">Interval</label>
              <ng-select
                id="interval"
                class="sfl-track-drop-down"
                name="interval"
                (change)="onIntervalSelect($event)"
                [items]="intervalType"
                bindLabel="name"
                bindValue="name"
                [(ngModel)]="filterModel.interval"
                notFoundText="No Interval Found"
                placeholder="Select Interval"
                [clearable]="false"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label" for="period">Period</label>
              <ng-select
                *ngIf="filterModel.interval !== 'Daily'"
                id="period"
                class="sfl-track-drop-down"
                name="period"
                [items]="periodLists"
                bindLabel="name"
                bindValue="name"
                [(ngModel)]="filterModel.period"
                notFoundText="No Period Found"
                placeholder="Select Period"
                [clearable]="false"
                appendTo="body"
                (change)="reportsData = null; exclusionReportsData = null"
              >
              </ng-select>
              <div class="d-flex align-items-center" *ngIf="filterModel.interval === 'Daily'">
                <ng-select
                  *ngIf="
                    (filterModel.customerIds.length === 1 || filterModel.customerId) &&
                    filterModel.portfolioIds.length === 1 &&
                    filterModel.siteIds.length === 1
                  "
                  id="period"
                  class="flex-box sfl-track-drop-down"
                  name="period"
                  [items]="periodLists"
                  bindLabel="name"
                  bindValue="name"
                  [(ngModel)]="filterModel.period"
                  notFoundText="No Period Found"
                  placeholder="Select Period"
                  [clearable]="false"
                  appendTo="body"
                  (change)="reportsData = null; exclusionReportsData = null; periodDateRange = null"
                >
                </ng-select>
                <span
                  class="mx-2"
                  *ngIf="
                    (filterModel.customerIds.length === 1 || filterModel.customerId) &&
                    filterModel.portfolioIds.length === 1 &&
                    filterModel.siteIds.length === 1
                  "
                  >OR</span
                >
                <input
                  class="form-control search-textbox flex-box sfl-track-input"
                  [nbDatepicker]="reportDateRange"
                  [(ngModel)]="periodDateRange"
                  name="reportDateRange"
                  placeholder="Select Date Range"
                  id="input-reportDateRange"
                  readonly
                  autocomplete="off"
                  (ngModelChange)="reportsData = null; exclusionReportsData = null; filterModel.period = null"
                />
                <nb-rangepicker #reportDateRange></nb-rangepicker>
              </div>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label" for="interval">Filter By</label>
              <ng-select
                id="filterBy"
                class="sfl-track-drop-down sfl-track-drop-down"
                name="filterBy"
                [items]="filterByList"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.filterBy"
                notFoundText="No Filter By Found"
                placeholder="Select Filter By"
                [clearable]="false"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label" for="operator">Comparison Operator</label>
              <ng-select
                id="operator"
                class="sfl-track-drop-down"
                name="operator"
                [items]="operatorsList"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.operator"
                notFoundText="No Interval Found"
                placeholder="Select Interval"
                [clearable]="false"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 mb-2 pe-0">
              <label class="label" for="thresholdValue">Threshold</label>
              <input
                id="thresholdValue"
                name="thresholdValue"
                nbInput
                fullWidth
                [(ngModel)]="filterModel.thresholdValue"
                placeholder="Threshold Value"
                class="form-control sfl-track-input"
              />
            </div>
            <div class="col-12 col-md-4 col-lg-2 mt-4 mb-2 d-flex">
              <button
                id="view-report-btn"
                class="linear-mode-button"
                nbButton
                status="primary"
                size="small"
                type="button"
                [disabled]="
                  (!filterModel?.customerIds?.length || !filterModel?.customerId) &&
                  !filterModel?.portfolioIds?.length &&
                  !filterModel?.siteIds?.length
                "
                (click)="getReportData(); generated = true"
              >
                View Report
              </button>
              <button
                id="clear-btn"
                class="linear-mode-button ms-2"
                nbButton
                status="primary"
                size="small"
                type="button"
                [disabled]="loading"
                (click)="clearFilter()"
              >
                clear
              </button>
            </div>
          </div>
        </div>
      </div>
      <div id="exportSection" class="col-12">
        <div class="row">
          <div class="col-12" *ngIf="reportsData && filterModel.reportType === 'Availability Report'">
            <div
              *ngIf="filterModel.isCustomerSelected && reportsData?.customerReport?.exclusionReports?.length"
              class="table-responsive mt-3"
            >
              <table class="table table-hover table-bordered" aria-describedby="Customar Report Data Table">
                <thead>
                  <tr>
                    <th scope="col">Date</th>
                    <th
                      scope="col"
                      (click)="
                        cusDirection['name'] = cusDirection['name'] === 'desc' ? 'asc' : 'desc';
                        cusSort.key = 'name';
                        cusSort.direction = cusDirection['name']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Customer
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': cusDirection['name'] === 'desc',
                            'fa-arrow-down': cusDirection['name'] === 'asc',
                            'icon-selected': cusSort.key === 'name'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        cusDirection['actualOperatingPeriods'] = cusDirection['actualOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        cusSort.key = 'actualOperatingPeriods';
                        cusSort.direction = cusDirection['actualOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Actual Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': cusDirection['actualOperatingPeriods'] === 'desc',
                            'fa-arrow-down': cusDirection['actualOperatingPeriods'] === 'asc',
                            'icon-selected': cusSort.key === 'actualOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        cusDirection['eligibleOperatingPeriods'] = cusDirection['eligibleOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        cusSort.key = 'eligibleOperatingPeriods';
                        cusSort.direction = cusDirection['eligibleOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Eligible Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': cusDirection['eligibleOperatingPeriods'] === 'desc',
                            'fa-arrow-down': cusDirection['eligibleOperatingPeriods'] === 'asc',
                            'icon-selected': cusSort.key === 'eligibleOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        cusDirection['excludedOperatingPeriods'] = cusDirection['excludedOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        cusSort.key = 'excludedOperatingPeriods';
                        cusSort.direction = cusDirection['excludedOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Excluded Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': cusDirection['excludedOperatingPeriods'] === 'desc',
                            'fa-arrow-down': cusDirection['excludedOperatingPeriods'] === 'asc',
                            'icon-selected': cusSort.key === 'excludedOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        cusDirection['availability'] = cusDirection['availability'] === 'desc' ? 'asc' : 'desc';
                        cusSort.key = 'availability';
                        cusSort.direction = cusDirection['availability']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Availability
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': cusDirection['availability'] === 'desc',
                            'fa-arrow-down': cusDirection['availability'] === 'asc',
                            'icon-selected': cusSort.key === 'availability'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        cusDirection['adjustedAvailability'] = cusDirection['adjustedAvailability'] === 'desc' ? 'asc' : 'desc';
                        cusSort.key = 'adjustedAvailability';
                        cusSort.direction = cusDirection['adjustedAvailability']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Adjusted Availability
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': cusDirection['adjustedAvailability'] === 'desc',
                            'fa-arrow-down': cusDirection['adjustedAvailability'] === 'asc',
                            'icon-selected': cusSort.key === 'adjustedAvailability'
                          }"
                        ></span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let item of reportsData?.customerReport?.exclusionReports | sort : cusSort.key : cusSort.direction;
                      let i = index
                    "
                  >
                    <td>{{ item.date }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.actualOperatingPeriods | number }}</td>
                    <td>{{ item.eligibleOperatingPeriods }}</td>
                    <td>{{ item.excludedOperatingPeriods | number }}</td>
                    <td>{{ item.availability | number }}%</td>
                    <td>{{ item.adjustedAvailability | number }}%</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              *ngIf="
                (filterModel.isPortfolioSelected ||
                  (!filterModel.isPortfolioSelected && !filterModel.isCustomerSelected && !filterModel.isSiteSelected)) &&
                reportsData?.portfolioReport?.exclusionReports?.length
              "
              class="table-responsive mt-3"
            >
              <table class="table table-hover table-bordered" aria-describedby="Customar Report Data Table">
                <thead>
                  <tr>
                    <th scope="col">Date</th>
                    <th
                      scope="col"
                      (click)="
                        portfolioDirection['name'] = portfolioDirection['name'] === 'desc' ? 'asc' : 'desc';
                        portfolioSort.key = 'name';
                        portfolioSort.direction = portfolioDirection['name']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Portfolio
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': portfolioDirection['name'] === 'desc',
                            'fa-arrow-down': portfolioDirection['name'] === 'asc',
                            'icon-selected': portfolioSort.key === 'name'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        portfolioDirection['actualOperatingPeriods'] =
                          portfolioDirection['actualOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        portfolioSort.key = 'actualOperatingPeriods';
                        portfolioSort.direction = portfolioDirection['actualOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Actual Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': portfolioDirection['actualOperatingPeriods'] === 'desc',
                            'fa-arrow-down': portfolioDirection['actualOperatingPeriods'] === 'asc',
                            'icon-selected': portfolioSort.key === 'actualOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        portfolioDirection['eligibleOperatingPeriods'] =
                          portfolioDirection['eligibleOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        portfolioSort.key = 'eligibleOperatingPeriods';
                        portfolioSort.direction = portfolioDirection['eligibleOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Eligible Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': portfolioDirection['eligibleOperatingPeriods'] === 'desc',
                            'fa-arrow-down': portfolioDirection['eligibleOperatingPeriods'] === 'asc',
                            'icon-selected': portfolioSort.key === 'eligibleOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        portfolioDirection['excludedOperatingPeriods'] =
                          portfolioDirection['excludedOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        portfolioSort.key = 'excludedOperatingPeriods';
                        portfolioSort.direction = portfolioDirection['excludedOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Excluded Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': portfolioDirection['excludedOperatingPeriods'] === 'desc',
                            'fa-arrow-down': portfolioDirection['excludedOperatingPeriods'] === 'asc',
                            'icon-selected': portfolioSort.key === 'excludedOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        portfolioDirection['availability'] = portfolioDirection['availability'] === 'desc' ? 'asc' : 'desc';
                        portfolioSort.key = 'availability';
                        portfolioSort.direction = portfolioDirection['availability']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Availability
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': portfolioDirection['availability'] === 'desc',
                            'fa-arrow-down': portfolioDirection['availability'] === 'asc',
                            'icon-selected': portfolioSort.key === 'availability'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        portfolioDirection['adjustedAvailability'] = portfolioDirection['adjustedAvailability'] === 'desc' ? 'asc' : 'desc';
                        portfolioSort.key = 'adjustedAvailability';
                        portfolioSort.direction = portfolioDirection['adjustedAvailability']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Adjusted Availability
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': portfolioDirection['adjustedAvailability'] === 'desc',
                            'fa-arrow-down': portfolioDirection['adjustedAvailability'] === 'asc',
                            'icon-selected': portfolioSort.key === 'adjustedAvailability'
                          }"
                        ></span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let item of reportsData?.portfolioReport?.exclusionReports | sort : portfolioSort.key : portfolioSort.direction;
                      let i = index
                    "
                  >
                    <td>{{ item.date }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.actualOperatingPeriods | number }}</td>
                    <td>{{ item.eligibleOperatingPeriods }}</td>
                    <td>{{ item.excludedOperatingPeriods | number }}</td>
                    <td>{{ item.availability | number }}%</td>
                    <td>{{ item.adjustedAvailability | number }}%</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div *ngIf="filterModel.isSiteSelected && reportsData?.siteReport?.exclusionReports?.length" class="table-responsive mt-3">
              <table class="table table-hover table-bordered" aria-describedby="Customar Report Data Table">
                <thead>
                  <tr>
                    <th scope="col">Date</th>
                    <th
                      scope="col"
                      (click)="
                        siteDirection['name'] = siteDirection['name'] === 'desc' ? 'asc' : 'desc';
                        siteSort.key = 'name';
                        siteSort.direction = siteDirection['name']
                      "
                    >
                      <div class="d-flex align-items-center">
                        site
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': siteDirection['name'] === 'desc',
                            'fa-arrow-down': siteDirection['name'] === 'asc',
                            'icon-selected': siteSort.key === 'name'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        siteDirection['actualOperatingPeriods'] = siteDirection['actualOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        siteSort.key = 'actualOperatingPeriods';
                        siteSort.direction = siteDirection['actualOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Actual Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': siteDirection['actualOperatingPeriods'] === 'desc',
                            'fa-arrow-down': siteDirection['actualOperatingPeriods'] === 'asc',
                            'icon-selected': siteSort.key === 'actualOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        siteDirection['eligibleOperatingPeriods'] = siteDirection['eligibleOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        siteSort.key = 'eligibleOperatingPeriods';
                        siteSort.direction = siteDirection['eligibleOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Eligible Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': siteDirection['eligibleOperatingPeriods'] === 'desc',
                            'fa-arrow-down': siteDirection['eligibleOperatingPeriods'] === 'asc',
                            'icon-selected': siteSort.key === 'eligibleOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        siteDirection['excludedOperatingPeriods'] = siteDirection['excludedOperatingPeriods'] === 'desc' ? 'asc' : 'desc';
                        siteSort.key = 'excludedOperatingPeriods';
                        siteSort.direction = siteDirection['excludedOperatingPeriods']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Excluded Operating Periods
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': siteDirection['excludedOperatingPeriods'] === 'desc',
                            'fa-arrow-down': siteDirection['excludedOperatingPeriods'] === 'asc',
                            'icon-selected': siteSort.key === 'excludedOperatingPeriods'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        siteDirection['availability'] = siteDirection['availability'] === 'desc' ? 'asc' : 'desc';
                        siteSort.key = 'availability';
                        siteSort.direction = siteDirection['availability']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Availability
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': siteDirection['availability'] === 'desc',
                            'fa-arrow-down': siteDirection['availability'] === 'asc',
                            'icon-selected': siteSort.key === 'availability'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th
                      scope="col"
                      (click)="
                        siteDirection['adjustedAvailability'] = siteDirection['adjustedAvailability'] === 'desc' ? 'asc' : 'desc';
                        siteSort.key = 'adjustedAvailability';
                        siteSort.direction = siteDirection['adjustedAvailability']
                      "
                    >
                      <div class="d-flex align-items-center">
                        Adjusted Availability
                        <span
                          class="fa px-1 cursor-pointer ms-auto"
                          [ngClass]="{
                            'fa-arrow-up': siteDirection['adjustedAvailability'] === 'desc',
                            'fa-arrow-down': siteDirection['adjustedAvailability'] === 'asc',
                            'icon-selected': siteSort.key === 'adjustedAvailability'
                          }"
                        ></span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="let item of reportsData?.siteReport?.exclusionReports | sort : siteSort.key : siteSort.direction; let i = index"
                  >
                    <td>{{ item.date }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.actualOperatingPeriods | number }}</td>
                    <td>{{ item.eligibleOperatingPeriods }}</td>
                    <td>{{ item.excludedOperatingPeriods | number }}</td>
                    <td>{{ item.availability | number }}%</td>
                    <td>{{ item.adjustedAvailability | number }}%</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              class="mt-3 text-center"
              *ngIf="
                !reportsData?.customerReport?.exclusionReports?.length &&
                !reportsData?.portfolioReport?.exclusionReports?.length &&
                !reportsData?.siteReport?.exclusionReports?.length &&
                generated
              "
            >
              No Data Found
            </div>
          </div>
          <div class="col-12" *ngIf="exclusionReportsData && filterModel.reportType === 'Exclusion Report'">
            <nb-accordion class="mb-3" *ngFor="let item of exclusionReportsData.exclusionReports">
              <nb-accordion-item [expanded]="exclusionAccordion" class="border-bottom">
                <nb-accordion-item-header class="accordion_head">
                  <strong>{{ item?.customerName }}</strong>
                </nb-accordion-item-header>
                <nb-accordion-item-body>
                  <div class="row">
                    <div class="col-12 col-sm-6 col-md-5 col-lg-4">
                      <div echarts [options]="getChartOption(item?.reportChartData)" [theme]="currentTheme"></div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-7 col-lg-8">
                      <div *ngIf="item?.reportTableData?.customerExclusions?.length" class="table-responsive mt-3">
                        <table class="table table-hover table-bordered" aria-describedby="Customar Report Data Table">
                          <thead>
                            <tr>
                              <th scope="col">Site (Portfolio)</th>
                              <th scope="col" *ngFor="let header of item?.reportTableData?.customerExclusions; let i = index">
                                {{ header }}
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let exclusion of item?.reportTableData?.siteExclusions; let i = index">
                              <td [ngClass]="{ bold: exclusion.name === 'Total' }">{{ exclusion.name }}</td>
                              <td [ngClass]="{ bold: exclusion.name === 'Total' }" *ngFor="let exclusionValue of exclusion?.value">
                                {{ exclusionValue | number }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
            </nb-accordion>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
