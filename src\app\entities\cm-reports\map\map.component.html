<nb-card class="ticketMapSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Map Report</h6>
        <div class="ms-auto">
          <button
            class="linear-mode-button"
            nbButton
            status="primary"
            size="small"
            type="button"
            (click)="exportData()"
            [disabled]="loading"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 ticketFilter appFilter mb-3">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (clearParentList)="ticketLocations = []"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div class="col-12">
        <div class="row">
          <div class="col-12 col-lg-6 pe-sm-0 map-min-height">
            <div class="row map-section">
              <div class="col-12 pe-sm-0 mb-2 switch-section">
                <div class="d-flex">
                  <div class="employees mb-2">
                    <label class="mb-2" for="employees"><em class="fas fa-circle me-2"></em>Employees</label>
                    <div class="d-flex">
                      <span class="pe-3">Hide</span>
                      <nb-toggle
                        status="primary"
                        [(checked)]="filterModel.showEmployees"
                        (checkedChange)="changeEmployees($event)"
                        [disabled]="
                          checkAuthorisationsFn([roleType.FIELDTECH, roleType.ANALYST, roleType.PORTFOLIOMANAGER, roleType.MANAGER])
                        "
                      ></nb-toggle>
                      <span class="ps-3">Show</span>
                    </div>
                  </div>
                  <div class="tickets mb-2 ms-3 ms-lg-5">
                    <label class="mb-2" for="tickets"><em class="fas fa-circle me-2"></em>Tickets</label>
                    <div class="d-flex">
                      <span class="pe-3">Hide</span>
                      <nb-toggle
                        status="primary"
                        [disabled]="
                          checkAuthorisationsFn([roleType.FIELDTECH, roleType.ANALYST, roleType.PORTFOLIOMANAGER, roleType.MANAGER])
                        "
                        [(checked)]="filterModel.showTickets"
                        (checkedChange)="changeTickets()"
                      ></nb-toggle>
                      <span class="ps-3">Show</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 map-row">
                <div id="map"></div>
              </div>
            </div>
          </div>
          <div class="col-12 col-lg-6 pe-sm-0 mt-3 mt-lg-0">
            <div
              id="fixed-table"
              setTableHeight
              [isFilterDisplay]="isFilterDisplay"
              [hasPagination]="false"
              class="col-12 table-responsive table-card-view"
            >
              <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                <thead>
                  <tr>
                    <th scope="col" class="text-center">Site</th>
                    <ng-container *ngIf="userRole[0] !== 'customer'">
                      <th scope="col" class="text-center">Region</th>
                      <th scope="col" class="text-center">Subregion</th>
                    </ng-container>
                    <th scope="col" class="text-end">Number Of Tickets</th>
                    <th scope="col" class="text-end">Total Affected kWac</th>
                    <th scope="col" class="text-end">Est. kWh Loss</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of ticketLocations; let i = index">
                    <td data-title="Site" class="text-center">{{ item?.site }}</td>
                    <ng-container *ngIf="userRole[0] !== 'customer'">
                      <td data-title="Region" class="text-center">{{ item?.regionName }}</td>
                      <td data-title="Subregion" class="text-center">{{ item?.subRegionName }}</td>
                    </ng-container>
                    <td data-title="Number Of Tickets" class="text-end">{{ item?.numberOfTickets }}</td>
                    <td data-title="Total Affected kWac" class="text-end">{{ item?.totalAffectedKwac }}</td>
                    <td data-title="Est. kWh Loss" class="text-end">{{ item?.totalAffectedHours }}</td>
                  </tr>
                  <tr *ngIf="!ticketLocations.length">
                    <td colspan="6" class="no-record text-center">No Data Found</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
