import { Date<PERSON>ip<PERSON> } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { fork<PERSON>oin } from 'rxjs';
import { Subscription } from 'rxjs-compat/Subscription';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { AllReportDropdown } from '../../../@shared/models/report.model';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter } from '../../site-device/site-device.model';
import { TicketStatusMapping } from '../../ticket-management/ticket.model';
import { cmExclusionReportPageFilterKeys, Exclusion, ExclusionFilterList, ExclusionsList } from '../cm-reports.model';
import { CmReportsService } from '../cm-reports.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-exclusion-listing',
  templateUrl: './exclusion-listing.component.html',
  styleUrls: ['./exclusion-listing.component.scss']
})
export class ExclusionListingComponent implements OnInit {
  loading = false;
  exclusions: Exclusion[] = [];
  subscription: Subscription = new Subscription();
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  total = 10;
  viewPage = FILTER_PAGE_NAME.CM_EXCLUSION_REPORT_LISTING;
  filterList = ExclusionFilterList;
  viewFilterSection = 'exclusionsFilterSection';
  ticketStatus = TicketStatusMapping;
  sortOptionList = {
    Number: 'asc',
    Priority: 'asc',
    Site: 'asc',
    Device: 'asc',
    Close: 'asc',
    Open: 'desc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    CustomerPortfolio: 'asc',
    TicketType: 'asc'
  };
  fullDateFormat = AppConstants.fullDateFormat;
  allReportDropdown = new AllReportDropdown();
  filterDetails: FilterDetails = new FilterDetails();
  userRole = this.storageService.get('user').authorities;

  constructor(
    private readonly datePipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly storageService: StorageService,
    private readonly cmReportsService: CmReportsService
  ) {}

  ngOnInit() {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection);

    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.states = (localFilterData || defaultFilterData).states;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.initFilterDetails();
    this.filterModel.direction = 'desc';
    this.filterModel.itemsCount = this.pageSize;
    this.isFilterDisplay = filterSection;
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));

    if (
      this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, cmExclusionReportPageFilterKeys)
    ) {
      const tempArray = [this.cmReportsService.getAllExclusionList(model)],
        tempArrayObj = ['bindExclusionList'];

      this.getAllLists(tempArray, tempArrayObj);
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = 'exclusionsFilterSection';
    this.filterDetails.page_name = this.viewPage;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.STATE.show = true;
    filterItem.STATUS.show = true;
    filterItem.EXCLUSION_FROM.show = true;
    filterItem.EXCLUSION_TO.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.TICKET_TYPE.show = true;
    filterItem.TICKET_TYPE.multi = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.default_direction = 'desc';
    this.filterDetails.filter_item = filterItem;
  }

  getAllLists(apiArray: any, mapResultList: string[], filterArrayObj = []) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          if (value === 'bindExclusionList') {
            this.bindExclusions(res[index]);
          } else {
            this[value] = res[index];
          }
        }
        setTimeout(() => {
          this.loading = false;
        }, 1000);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getAllExclusionList(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    const model = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.exclusionFrom && this.filterModel.exclusionFrom.start && this.filterModel.exclusionFrom.end) {
      model.exclusionFrom.start = this.datePipe.transform(this.filterModel.exclusionFrom.start, AppConstants.fullDateFormat);
      model.exclusionFrom.end = this.datePipe.transform(this.filterModel.exclusionFrom.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.exclusionFrom = null;
      model.exclusionFrom = null;
    }
    if (this.filterModel.exclusionTo && this.filterModel.exclusionTo.start && this.filterModel.exclusionTo.end) {
      model.exclusionTo.start = this.datePipe.transform(this.filterModel.exclusionTo.start, AppConstants.fullDateFormat);
      model.exclusionTo.end = this.datePipe.transform(this.filterModel.exclusionTo.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.exclusionTo = null;
      model.exclusionTo = null;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, model);
    }
    this.cmReportsService.getAllExclusionList(model).subscribe({
      next: (res: ExclusionsList) => {
        this.bindExclusions(res);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  bindExclusions(res: ExclusionsList) {
    this.loading = false;
    this.exclusions = res && res.ticketExclusions ? res.ticketExclusions : [];
    this.total = res && res.totalCount ? res.totalCount : 0;
  }

  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
  }

  // Pagesize Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllExclusionList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllExclusionList();
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllExclusionList();
  }

  clearFilter() {
    this.resetPage();
    this.filterModel = new CommonFilter();
    this.filterModel.itemsCount = this.pageSize;
    this.appliedFilter = [];
    this.getAllExclusionList();
  }

  exportData() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.exclusionFrom && this.filterModel.exclusionFrom.start && this.filterModel.exclusionFrom.end) {
      filterModel.exclusionFrom.start = this.datePipe.transform(this.filterModel.exclusionFrom.start, AppConstants.fullDateFormat);
      filterModel.exclusionFrom.end = this.datePipe.transform(this.filterModel.exclusionFrom.end, AppConstants.fullDateFormat);
    } else {
      filterModel.exclusionFrom = null;
    }
    if (this.filterModel.exclusionTo && this.filterModel.exclusionTo.start && this.filterModel.exclusionTo.end) {
      filterModel.exclusionTo.start = this.datePipe.transform(this.filterModel.exclusionTo.start, AppConstants.fullDateFormat);
      filterModel.exclusionTo.end = this.datePipe.transform(this.filterModel.exclusionTo.end, AppConstants.fullDateFormat);
    } else {
      filterModel.exclusionTo = null;
    }
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    this.subscription.add(
      this.cmReportsService.getAllExclusionList(filterModel).subscribe({
        next: (data: ExclusionsList) => {
          const tittle = 'Exclusions';
          const rows: any = [
            ['Number', 'Priority', 'Ticket Type', 'Customer', 'Portfolio', 'Site', 'Device', 'Issue', 'Opened', 'Closed', 'Status']
          ];
          for (const i of data.ticketExclusions) {
            const tempData = [
              i.ticketNumber,
              i.priority,
              i.ticketTypeName,
              i.customerName,
              i.portfolioName,
              i.site,
              i.deviceLabel ? i.deviceLabel : '',
              i.issue,
              `${this.formateDate(i.opened)}`,
              i.closed ? `${this.formateDate(i.closed)}` : '',
              i.status
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  formateDate(date: Date | string) {
    return this.datePipe.transform(date, AppConstants.fullDateFormat);
  }

  toggleFilter() {
    this.isFilterDisplay = !this.isFilterDisplay;
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllExclusionList(true, filterParams);
  }
}
