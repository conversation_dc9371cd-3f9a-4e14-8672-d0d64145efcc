<nb-card class="spinnerDisplay" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex">
      <h6>{{ getComponentTitle() }}</h6>
      <div class="ms-auto d-flex">
        <div class="d-flex ml-auto" *ngIf="customer.isArchive === false && !isCreate">
          <label class="label d-flex mt-2 me-2" for="customer">Active </label>
          <div class="d-flex">
            <nb-toggle
              [(checked)]="customer.isActive"
              (checkedChange)="activeToggleChange(customer.isActive)"
              [disabled]="isDetail"
              status="primary"
              class="me-3"
            ></nb-toggle>
          </div>
        </div>
        <div class="d-flex ml-auto" *ngIf="userRoles[0] !== 'customer' && !isCreate">
          <label class="label d-flex mt-2 me-2" for="customer">Archive </label>
          <div class="d-flex">
            <nb-toggle
              (checkedChange)="(!!customer.isArchive)"
              status="primary"
              [disabled]="isDetail"
              [(checked)]="customer.isArchive"
              class="me-3"
            ></nb-toggle>
          </div>
        </div>
        <button
          nbButton
          status="primary"
          size="small"
          type="submit"
          id="customerSubmit"
          (click)="createForm.onSubmit()"
          class="float-end me-2"
          *ngIf="isEdit || isCreate"
        >
          <span class="d-none d-lg-inline-block">Save</span>
          <i class="d-inline-block d-lg-none fa-solid fa-save"></i>
        </button>
        <button
          nbButton
          status="danger"
          size="small"
          type="button"
          id="customerSubmit"
          (click)="onDelete(customer.id)"
          class="float-end me-2"
          *ngIf="checkAuthorisationsFn([roleType.ADMIN]) && !isCreate"
        >
          <span class="d-none d-lg-inline-block">Delete</span>
          <i class="d-inline-block d-lg-none fa-solid fa-trash"></i>
        </button>
        <button
          *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER, roleType.FIELDTECH]) && !customer.isArchive"
          (click)="isEdit = true; isDetail = false"
          nbButton
          status="primary"
          size="small"
          type="button"
          id="siteSubmit"
          class="float-end me-2"
        >
          <span class="d-none d-lg-inline-block">Edit</span>
          <i class="d-inline-block d-lg-none fa-solid fa-pen"></i>
        </button>
        <button nbButton status="basic" type="button" (click)="goBack()" size="small" class="float-end">
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <form
      name="createForm"
      #createForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="createForm?.form?.valid && createCustomer()"
    >
      <div class="row mb-2">
        <div class="col-12 col-sm-6 col-lg-4">
          <div class="form-control-group">
            <label class="label" for="input-customerName">Customer Name<span class="ms-1 text-danger">*</span></label>
            <span *ngIf="isDetail">{{ customer.customerName }}</span>
            <div *ngIf="isEdit || isCreate">
              <input
                nbInput
                fullWidth
                [(ngModel)]="customer.customerName"
                #customerName="ngModel"
                name="customerName"
                id="input-customerName"
                pattern=".*\S.*"
                class="form-control"
                spellcheck="true"
                contenteditable="true"
                required
              />
              <sfl-error-msg [control]="customerName" [isFormSubmitted]="createForm?.submitted" fieldName="Customer Name"></sfl-error-msg>
            </div>
          </div>
        </div>
        <div class="col-12 col-sm-3 col-lg-4" *ngIf="isEdit || isDetail">
          <div class="form-control-group">
            <label class="label" for="input-noOfSite"> Number of Sites</label>
            <ng-container>
              <label>{{ customer?.totalSites }}</label>
            </ng-container>
          </div>
        </div>
        <div class="col-12 col-sm-3 col-lg-4" *ngIf="isEdit || isDetail">
          <div class="form-control-group">
            <label class="label" for="input-noOfSite"> kWdc</label>
            <ng-container>
              <label>{{ customer?.sumDCSize | sflRound | sflNumberWithCommas }}</label>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="row mb-2" *ngIf="userRoles[0] !== 'customer'">
        <div class="col-12">
          <div class="form-control-group">
            <label class="label" for="input-email">CM Truck Rolls Counted By:</label>
            <nb-radio-group
              [disabled]="isDetail"
              class="d-block d-sm-flex"
              name="truckRole"
              [(ngModel)]="customer.truckRoleBaseOnFieldTech"
            >
              <nb-radio [value]="false">Dispatches to Site</nb-radio>
              <nb-radio [value]="true">Number of Technicians Dispatched to Site (Luminace Only)</nb-radio>
            </nb-radio-group>
          </div>
        </div>
      </div>
      <nb-accordion>
        <nb-accordion-item [expanded]="true" class="border-bottom mb-2">
          <nb-accordion-item-header class="accordion_head"> Scope </nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="form-group row">
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <div class="form-control-group">
                  <label class="label" for="input-siteVisit">Site Visit<span class="ms-1 text-danger">*</span></label>
                  <span *ngIf="isDetail">{{ customer.taskDetails.siteVisitName }}</span>
                  <div *ngIf="isEdit || isCreate">
                    <ng-select
                      fullWidth
                      id="siteVisit"
                      name="siteVisit"
                      required
                      [(ngModel)]="customer.taskDetails.siteVisit"
                      #siteVisit="ngModel"
                      size="large"
                      [clearable]="false"
                      appendTo="body"
                    >
                      <sfl-error-msg [control]="siteVisit" [isFormSubmitted]="createForm?.submitted" fieldName="Site Visit"></sfl-error-msg>
                      <ng-option *ngFor="let siteVisit of siteVisits" [value]="siteVisit.id">
                        {{ siteVisit.name }}
                      </ng-option>
                    </ng-select>
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-inverterPM">Inverter PM<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.inverterPMName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="inverterPM"
                    name="inverterPM"
                    required
                    [(ngModel)]="customer.taskDetails.inverterPM"
                    #inverterPM="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <sfl-error-msg [control]="inverterPM" [isFormSubmitted]="createForm?.submitted" fieldName="Inveter PM"></sfl-error-msg>
                    <ng-option *ngFor="let inverterPM of inverterPMs" [value]="inverterPM.id">
                      {{ inverterPM.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-electricalVOC">Electrical - VOC<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.electricalVOCName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="electricalVOC"
                    name="electricalVOC"
                    required
                    [(ngModel)]="customer.taskDetails.electricalVOC"
                    #electricalVOC="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let electricalVOC of electricalVOCs" [value]="electricalVOC.id">
                      {{ electricalVOC.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-electricalIV">Electrical - IV Curve<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.electricalIVName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="electricalIV"
                    name="electricalIV"
                    required
                    [(ngModel)]="customer.taskDetails.electricalIV"
                    #electricalIV="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let electricalIV of electricalIVs" [value]="electricalIV.id">
                      {{ electricalIV.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-mvpm">Medium Voltage PM<span class="ms-1 text-danger">*</span><br /> </label>
                <span *ngIf="isDetail">{{ customer.taskDetails.mvpmName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="mvpm"
                    name="mvpm"
                    required
                    [(ngModel)]="customer.taskDetails.mvpm"
                    #mvpm="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let mediumVoltagePM of mediumVoltagePMs" [value]="mediumVoltagePM.id">
                      {{ mediumVoltagePM.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-tpm">Tracker Preventative Maintenance<span class="ms-1 text-danger">*</span> </label>
                <span *ngIf="isDetail">{{ customer.taskDetails.tpmName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="tpm"
                    name="tpm"
                    required
                    [(ngModel)]="customer.taskDetails.tpm"
                    #tpm="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let tpm of tpms" [value]="tpm.id">
                      {{ tpm?.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-thermal">Thermal<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.thermalName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="thermal"
                    name="thermal"
                    required
                    [(ngModel)]="customer.taskDetails.thermal"
                    #thermal="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let thermal of thermals" [value]="thermal.id">
                      {{ thermal.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-aerialScan">Aerial Scan<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.aerialScanName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="aerialScan"
                    name="aerialScan"
                    required
                    [(ngModel)]="customer.taskDetails.aerialScan"
                    #aerialScan="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let aerialScan of aerialScans" [value]="aerialScan.id">
                      {{ aerialScan.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-vegetation">Vegetation<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.vegetationName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="vegetation"
                    name="vegetation"
                    required
                    [(ngModel)]="customer.taskDetails.vegetation"
                    #vegetation="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let vegetation of vegetations" [value]="vegetation.id">
                      {{ vegetation.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-monitoing">Monitoring<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.monitoingName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="monitoing"
                    name="monitoing"
                    required
                    [(ngModel)]="customer.taskDetails.monitoing"
                    #monitoing="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let monitoring of monitorings" [value]="monitoring.id">
                      {{ monitoring.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-performanceReporting">Performance Reporting<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.performanceReportingName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="performanceReporting"
                    name="performanceReporting"
                    required
                    [(ngModel)]="customer.taskDetails.performanceReporting"
                    #performanceReporting="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let performanceReporting of performanceReportings" [value]="performanceReporting.id">
                      {{ performanceReporting.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-trq">Module Torque<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.trqName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="trq"
                    name="trq"
                    required
                    [(ngModel)]="customer.taskDetails.trq"
                    #trq="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let trq of trqs" [value]="trq.id">
                      {{ trq?.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-mvth">Medium Voltage Thermal<span class="ms-1 text-danger">*</span></label>
                <span *ngIf="isDetail">{{ customer.taskDetails.mvthName }}</span>
                <div *ngIf="isEdit || isCreate">
                  <ng-select
                    fullWidth
                    id="mvth"
                    name="mvth"
                    required
                    [(ngModel)]="customer.taskDetails.mvth"
                    #mvth="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let mvth of mvThermals" [value]="mvth.id">
                      {{ mvth?.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <nb-accordion>
        <nb-accordion-item [expanded]="true" class="border-bottom mb-2">
          <nb-accordion-item-header class="accordion_head"> Availability </nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="form-control-group row">
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <nb-toggle
                  status="primary"
                  [disabled]="isDetail"
                  [(ngModel)]="customer.isAvailability"
                  name="availability"
                  labelPosition="start"
                >
                  <label class="label">Availability</label>
                </nb-toggle>
              </div>
            </div>
            <div class="form-group row">
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <div class="form-control-group">
                  <label class="label" for="input-email">Operating Interval</label>
                  <nb-radio-group
                    class="d-flex"
                    [disabled]="!customer.isAvailability || isDetail"
                    [(ngModel)]="customer.customerAvailabilityDetail.operatingInterval"
                    name="operatingInterval"
                  >
                    <nb-radio [value]="2">1 Hour</nb-radio>
                    <nb-radio [value]="1">15 Min</nb-radio>
                  </nb-radio-group>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <div class="form-control-group">
                  <label class="label" for="input-customerName">Irradiance Threshold(W/M2)</label>
                  <span *ngIf="isDetail">{{
                    customer.customerAvailabilityDetail.irradianceThreshold ? customer.customerAvailabilityDetail.irradianceThreshold : '-'
                  }}</span>
                  <div *ngIf="isEdit || isCreate">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="customer.customerAvailabilityDetail.irradianceThreshold"
                      #irradianceThreshold="ngModel"
                      name="irradianceThreshold"
                      id="input-irradianceThreshold"
                      pattern=".*\S.*"
                      class="form-control"
                      spellcheck="true"
                      contenteditable="true"
                      [disabled]="!customer.isAvailability"
                      sflNumbersOnly
                    />
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <div class="form-control-group">
                  <label class="label" for="input-customerName">Power Threshold (Kilowatts)</label>
                  <span *ngIf="isDetail">{{
                    customer.customerAvailabilityDetail.powerThreshold ? customer.customerAvailabilityDetail.powerThreshold : '-'
                  }}</span>
                  <div *ngIf="isEdit || isCreate">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="customer.customerAvailabilityDetail.powerThreshold"
                      #powerThreshold="ngModel"
                      name="powerThreshold"
                      id="input-powerThreshold"
                      pattern=".*\S.*"
                      class="form-control"
                      spellcheck="true"
                      contenteditable="true"
                      [disabled]="!customer.isAvailability"
                      sflNumbersOnly
                    />
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <div class="form-control-group">
                  <label class="label" for="input-customerName">Grace Period (Hours)</label>
                  <span *ngIf="isDetail">{{
                    customer.customerAvailabilityDetail.gracePeriod ? customer.customerAvailabilityDetail.gracePeriod : '-'
                  }}</span>
                  <div *ngIf="isEdit || isCreate">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="customer.customerAvailabilityDetail.gracePeriod"
                      #customerName="ngModel"
                      name="gracePeriod"
                      id="input-gracePeriod"
                      pattern=".*\S.*"
                      class="form-control"
                      spellcheck="true"
                      contenteditable="true"
                      [disabled]="!customer.isAvailability"
                      sflNumbersOnly
                    />
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <div class="form-control-group">
                  <label class="label" for="input-customerName">Availability Guarantee (%)</label>
                  <span *ngIf="isDetail">{{
                    customer.customerAvailabilityDetail.availabilityGuarantee
                      ? customer.customerAvailabilityDetail.availabilityGuarantee
                      : '-'
                  }}</span>
                  <div *ngIf="isEdit || isCreate">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="customer.customerAvailabilityDetail.availabilityGuarantee"
                      #customerName="ngModel"
                      name="availabilityGuarantee"
                      id="input-availabilityGuarantee"
                      pattern=".*\S.*"
                      class="form-control"
                      spellcheck="true"
                      contenteditable="true"
                      [disabled]="!customer.isAvailability"
                      sflNumbersOnly
                    />
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label class="label" for="input-email">Auto Grace Period Exclusion</label>
                <div>
                  <nb-toggle
                    [(ngModel)]="customer.customerAvailabilityDetail.isAutoGracePeriodExclusion"
                    status="primary"
                    labelPosition="start"
                    name="isAutoGracePeriodExclusion"
                    [disabled]="!customer.isAvailability || isDetail"
                  >
                  </nb-toggle>
                </div>
              </div>
            </div>
            <div class="row mb-2">
              <div class="col-12">
                <button type="button" [disabled]="!customer.isAvailability || isDetail" (click)="addExclusion()" class="btn btn-info">
                  Add Exclusion Type
                </button>
              </div>
            </div>
            <div class="row mb-2" *ngIf="customer.listOfAvailabilityExclusionType.length">
              <div class="col-12 col-sm-6 col-lg-4 mb-1">
                <div class="row">
                  <div class="col-3">
                    <label class="label">Index No.</label>
                  </div>
                  <div class="col-6">
                    <label class="label">Exclusion Type</label>
                  </div>
                  <div class="col-3">
                    <label class="label">Is Active</label>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-lg-4 mb-1 d-none d-sm-block" *ngIf="customer.listOfAvailabilityExclusionType.length >= 2">
                <div class="row">
                  <div class="col-3">
                    <label class="label">Index No.</label>
                  </div>
                  <div class="col-6">
                    <label class="label">Exclusion Type</label>
                  </div>
                  <div class="col-3">
                    <label class="label">Is Active</label>
                  </div>
                </div>
              </div>
              <div class="col-lg-4 mb-1 d-none d-lg-block" *ngIf="customer.listOfAvailabilityExclusionType.length >= 3">
                <div class="row">
                  <div class="col-3">
                    <label class="label">Index No.</label>
                  </div>
                  <div class="col-6">
                    <label class="label">Exclusion Type</label>
                  </div>
                  <div class="col-3">
                    <label class="label">Is Active</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-12 col-sm-6 col-lg-4 mb-2" *ngFor="let items of customer.listOfAvailabilityExclusionType; let i = index">
                <div class="row">
                  <div class="col-3">
                    <span>{{ items.exclusionIndex }}</span>
                  </div>
                  <div class="col-6">
                    <div>
                      <span *ngIf="isDetail">{{ items.exclusionType }}</span>
                      <div *ngIf="isEdit || isCreate">
                        <input
                          nbInput
                          fullWidth
                          [disabled]="!customer.isAvailability"
                          class="form-control"
                          spellcheck="true"
                          contenteditable="true"
                          name="{{ 'exclusionType-' + i }}"
                          id="{{ 'input-exclusionType' + i }}"
                          [(ngModel)]="items.exclusionType"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-3">
                    <nb-toggle
                      name="{{ 'isActive' + i }}"
                      [(ngModel)]="items.isActive"
                      [disabled]="!customer.isAvailability || isDetail"
                      status="primary"
                    ></nb-toggle>
                  </div>
                </div>
              </div>
            </div>
            <div class="row" *ngIf="customer.listOfAvailabilityExclusionType.length && !isDetail">
              <div class="col-12 col-sm-6 mb-2">
                <span class="text-primary pointerReportLink" (click)="addExclusion()">+ Add Exclusion Type</span>
              </div>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <nb-accordion *appHasPermission="roleType.ADMIN">
        <nb-accordion-item [expanded]="true" class="border-bottom mb-2">
          <nb-accordion-item-header class="accordion_head">Alerts</nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="row">
              <div class="col-12 col-lg-6 mb-2 mb-lg-0">
                <div class="row">
                  <div class="col-12 col-md-6 mb-2">
                    <nb-toggle
                      status="primary"
                      [disabled]="isDetail"
                      [(ngModel)]="customerOutage.zeroGeneration"
                      name="isZeroGeneration"
                      labelPosition="start"
                    >
                      <label class="label">Zero Generation</label>
                    </nb-toggle>
                  </div>
                </div>
                <div class="row mb-2">
                  <div class="col-12 col-md-6 mb-2 mb-sm-0">
                    <div class="form-control-group">
                      <label class="label" for="input-alertpowerThreshold">Power Threshold (kW)</label>
                      <span *ngIf="isDetail">{{ customerOutage?.powerThreshold ? customerOutage?.powerThreshold : '-' }}</span>
                      <div *ngIf="isEdit || isCreate">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="customerOutage.powerThreshold"
                          #alertPowerThreshold="ngModel"
                          name="alertPowerThreshold"
                          id="input-alertpowerThreshold"
                          class="form-control"
                          [disabled]="!customerOutage.zeroGeneration"
                          sflNumbersOnly
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-6">
                    <div class="form-control-group">
                      <label class="label" for="input-triggerCount">Trigger Count</label>
                      <span *ngIf="isDetail">{{ customerOutage?.triggerCount ? customerOutage?.triggerCount : '-' }}</span>
                      <div *ngIf="isEdit || isCreate">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="customerOutage.triggerCount"
                          #triggerCount="ngModel"
                          [disabled]="!customerOutage.zeroGeneration"
                          name="triggerCount"
                          id="input-triggerCount"
                          class="form-control"
                          sflNumbersOnly
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-group" *ngIf="!isCreate">
                  <label class="label">Daytime Alerting Window </label>
                  <div id="fixed-table">
                    <table class="table table-hover table-bordered" aria-describedby="Site Performance List">
                      <thead>
                        <tr>
                          <th scope="col">Month</th>
                          <th scope="col">Local Start Time</th>
                          <th scope="col">Local End Time</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let Daytime of customerOutage.timeSetting; let i = index">
                          <td>
                            <span>{{ getMonthName(Daytime.month) }}</span>
                          </td>
                          <td>
                            <span *ngIf="isDetail">{{ Daytime.startTime }}</span>
                            <input
                              *ngIf="isCreate || isEdit"
                              name="expectedStartTime-{{ i }}"
                              id="input-name"
                              [(ngModel)]="customerOutage?.timeSetting[i].startTime"
                              class="form-control"
                              [disabled]="!customerOutage.zeroGeneration"
                              spellcheck="true"
                              contenteditable="true"
                              nbInput
                              (ngModelChange)="formatTime($event, i, 'startTime')"
                              maxlength="5"
                              placeholder="hh:mm"
                              class="form-control"
                            />
                          </td>
                          <td>
                            <span *ngIf="isDetail">{{ Daytime.endTime }}</span>
                            <input
                              *ngIf="isCreate || isEdit"
                              name="expectedEndTime-{{ i }}"
                              id="input-production"
                              [(ngModel)]="customerOutage?.timeSetting[i].endTime"
                              class="form-control"
                              [disabled]="!customerOutage.zeroGeneration"
                              spellcheck="true"
                              contenteditable="true"
                              nbInput
                              maxlength="5"
                              placeholder="hh:mm"
                              (ngModelChange)="formatTime($event, i, 'endTime')"
                              class="form-control"
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="col-12 col-lg-6" id="fixed-table" *ngIf="!isCreate">
                <table class="table table-hover table-bordered" aria-describedby="Site Performance List">
                  <thead>
                    <tr>
                      <th scope="col">Portfolio - Sites</th>
                      <th scope="col">Zero Generation</th>
                      <th scope="col">Use Customer Settings</th>
                    </tr>
                  </thead>
                  <tbody *ngIf="customerOutage?.portfoliosSites?.length > 0">
                    <ng-container *ngFor="let portfolio of customerOutage.portfoliosSites; let portfolioIndex = index">
                      <tr>
                        <td class="text-start">
                          <span class="fw-bold">{{ portfolio.name }}</span>
                        </td>
                        <td>
                          <nb-toggle
                            status="primary"
                            [disabled]="isDetail"
                            [(ngModel)]="portfolio.zeroGeneration"
                            name="zeroGenerationPortfolio-{{ portfolioIndex }}"
                            labelPosition="start"
                          >
                          </nb-toggle>
                        </td>
                        <td>
                          <nb-toggle
                            status="primary"
                            [disabled]="isDetail || !portfolio.zeroGeneration"
                            [(ngModel)]="portfolio.isParentSetting"
                            name="useCustomerSettingPortfolio-{{ portfolioIndex }}"
                            labelPosition="start"
                          >
                          </nb-toggle>
                        </td>
                      </tr>
                      <tr *ngFor="let sites of portfolio.sites; let siteIndex = index">
                        <td class="text-end">
                          <span>{{ sites.siteName }}</span>
                        </td>
                        <td>
                          <nb-toggle
                            status="primary"
                            [disabled]="isDetail || !portfolio.zeroGeneration"
                            [(ngModel)]="sites.zeroGeneration"
                            name="zeroGenerationSite-{{ siteIndex }}-{{ portfolioIndex }}"
                            labelPosition="start"
                          >
                          </nb-toggle>
                        </td>
                        <td>
                          <nb-toggle
                            status="primary"
                            [disabled]="isDetail || !portfolio.zeroGeneration || !sites.zeroGeneration"
                            [(ngModel)]="sites.isParentSetting"
                            name="useCustomerSetting-{{ siteIndex }}-{{ portfolioIndex }}"
                            labelPosition="start"
                          >
                          </nb-toggle>
                        </td>
                      </tr>
                    </ng-container>
                  </tbody>
                  <tbody *ngIf="!customerOutage?.portfoliosSites?.length">
                    <tr>
                      <td colspan="3" class="no-record text-center">No portfolios found</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <nb-accordion>
        <nb-accordion-item [expanded]="true" class="border-bottom mb-2">
          <nb-accordion-item-header class="accordion_head"> Performance Report </nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="row">
              <div class="col-12 col-md-4 mb-2">
                <div class="form-control-group">
                  <label class="label">Auto Generate Performance Report</label>
                  <nb-toggle
                    status="primary"
                    [disabled]="isDetail"
                    [(ngModel)]="customer.isAutoGeneratePR"
                    name="isAutoGeneratePR"
                    labelPosition="start"
                    (checkedChange)="onAutoGeneratePRChange($event)"
                  ></nb-toggle>
                </div>
              </div>
              <div class="col-12 col-md-4 mb-2" *ngIf="customer.isAutoGeneratePR">
                <div class="form-control-group">
                  <label class="label">Number of business days following report period<span class="ms-1 text-danger">*</span></label>
                  <span *ngIf="isDetail">{{ customer.prGenerateWorkDays || '-' }}</span>
                  <ng-select
                    fullWidth
                    id="PRGenerateWorkDay"
                    name="PRGenerateWorkDay"
                    required
                    [(ngModel)]="customer.prGenerateWorkDays"
                    #prGenerateWorkDays="ngModel"
                    size="large"
                    [clearable]="false"
                    appendTo="body"
                    *ngIf="isEdit || isCreate"
                  >
                    <sfl-error-msg
                      [control]="prGenerateWorkDays"
                      [isFormSubmitted]="createForm?.submitted"
                      fieldName="Working day"
                    ></sfl-error-msg>
                    <ng-option *ngFor="let PRGenerateWorkDay of PRGenerateWorkDaysList" [value]="PRGenerateWorkDay">
                      {{ PRGenerateWorkDay }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div
                class="col-12 col-md-4 mb-2 text-end"
                *ngIf="
                  (customer.isAutoGeneratePR && isAutoGeneratePR && isEdit) || (customer.isAutoGeneratePR && isAutoGeneratePR && isDetail)
                "
              >
                <button
                  nbButton
                  status="primary"
                  size="small"
                  type="submit"
                  id="generate-report"
                  (click)="checkAutoMapPRWorkOrder()"
                  [disabled]="isDetail"
                >
                  Generate Report
                </button>
              </div>
              <div
                id="fixed-table"
                class="col-12 table-responsive table-card-view"
                *ngIf="
                  (customer.isAutoGeneratePR && isAutoGeneratePR && isEdit) || (customer.isAutoGeneratePR && isAutoGeneratePR && isDetail)
                "
                [nbSpinner]="logsLoading"
                nbSpinnerStatus="primary"
              >
                <table class="table table-hover table-bordered" aria-describedby="Audit Logs">
                  <thead>
                    <tr>
                      <th scope="col">Requested Date</th>
                      <th scope="col">Requested By</th>
                      <th scope="col" class="text-end">Total Records</th>
                      <th scope="col" class="text-end">Total Processed Records</th>
                      <th scope="col" class="text-end">Total Pending Records</th>
                      <th scope="col">Automatic/Manual</th>
                    </tr>
                  </thead>
                  <tbody>
                    <ng-container *ngIf="auditLogs?.length; else noAuditLogs">
                      <tr *ngFor="let item of auditLogs; let i = index">
                        <td data-title="Requested Date">{{ item?.requestDate }}</td>
                        <td data-title="Requested By">{{ item?.requestedUser }}</td>
                        <td data-title="Total Records" class="text-end">{{ item?.totalRecords }}</td>
                        <td data-title="Total Processed Records" class="text-end">{{ item?.processRecords }}</td>
                        <td data-title="Total Pending Records" class="text-end">{{ item?.pendingRecords }}</td>
                        <td data-title="Automatic/Manual">
                          {{ item?.isAutoRequest ? 'Automatic' : 'Manual' }}
                        </td>
                      </tr>
                    </ng-container>
                    <ng-template #noAuditLogs>
                      <tr>
                        <td colspan="6" class="no-record text-center">No Data Found</td>
                      </tr>
                    </ng-template>
                  </tbody>
                </table>
              </div>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <nb-accordion class="mb-2" [nbSpinner]="attachmentsLoading">
        <nb-accordion-item [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head">Customer Files </nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="file-attachments">
              <div class="d-flex align-items-start justify-content-end mb-3">
                <input
                  nbInput
                  name="fileSearch"
                  id="input-fileSearch"
                  [(ngModel)]="fileSearchText"
                  #fileSearchModel="ngModel"
                  class="form-control me-2"
                  placeholder="Search Files"
                  (ngModelChange)="fileSearchChanged()"
                />

                <button
                  *ngIf="userRoles[0] !== 'customer' && isEdit"
                  nbButton
                  status="primary"
                  size="small"
                  type="button"
                  id="addFiles"
                  class="me-2"
                  [disabled]="!allSelectedFiles.length"
                  (click)="openEditTagsModal(addRemoveFilesTagsModal)"
                >
                  <span class="d-flex"><em class="pi pi-tag me-2"></em>Manage tags</span>
                </button>
                <button
                  *ngIf="userRoles[0] !== 'customer' && isEdit"
                  nbButton
                  status="primary"
                  size="small"
                  type="button"
                  id="addFiles"
                  class="me-2"
                  (click)="openFileUploadSidePanel(false, null)"
                >
                  <span class="d-flex"><em class="pi pi-plus me-2"></em>Add Files</span>
                </button>
              </div>
              <div class="main-image-gallery">
                <div id="fixed-table" setTableHeight [isFilterDisplay]="true" class="col-12 table-responsive table-card-view">
                  <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                    <thead *ngIf="createFileUploadList.length || fileAttachments?.fileGallery?.length">
                      <tr>
                        <th *ngIf="userRoles[0] !== 'customer' && isEdit" id="select-all" class="text-center">
                          <nb-checkbox
                            id="select-all"
                            class="sfl-track-checkbox"
                            [(ngModel)]="isSelectedAllFiles"
                            (change)="selectAllFiles()"
                            name="selectAllFiles"
                          >
                          </nb-checkbox>
                        </th>
                        <th id="FileName" (click)="sortFiles('fileName', sortOptionList['fileName'])">
                          File Name
                          <span
                            class="fa cursor-pointer ms-auto"
                            [ngClass]="{
                              'fa-arrow-up': sortOptionList['fileName'] === 'desc',
                              'fa-arrow-down': sortOptionList['fileName'] === 'asc',
                              'icon-selected': sortBy === 'fileName'
                            }"
                          ></span>
                        </th>
                        <th id="Tags">Tags</th>
                        <th id="Note">Note</th>
                        <th id="UploadedBy">Uploaded By</th>
                        <th id="Date" (click)="sortFiles('createdDate', sortOptionList['createdDate'])">
                          Date
                          <span
                            class="fa cursor-pointer ms-auto"
                            [ngClass]="{
                              'fa-arrow-up': sortOptionList['createdDate'] === 'desc',
                              'fa-arrow-down': sortOptionList['createdDate'] === 'asc',
                              'icon-selected': sortBy === 'createdDate'
                            }"
                          ></span>
                        </th>
                        <th id="Action" class="text-center">Action</th>
                      </tr>
                    </thead>
                    <tbody *ngIf="!isCreate">
                      <ng-container *ngIf="fileAttachments?.fileGallery?.length">
                        <tr
                          *ngFor="
                            let document of fileAttachments?.fileGallery
                              | paginate
                                : {
                                    id: 'documents',
                                    itemsPerPage: filesPaginationParams.itemsCount,
                                    currentPage: filesPaginationParams.currentPage,
                                    totalItems: fileAttachments?.totalCount
                                  }
                          "
                        >
                          <td data-title="Select Files" class="text-center" *ngIf="userRoles[0] !== 'customer' && isEdit">
                            <nb-checkbox
                              id="select-file+{{ document.id }}"
                              class="sfl-track-checkbox"
                              name="selectSingleFiles+{{ document.id }}"
                              (change)="singleFilesCheckChanged(document)"
                              [(ngModel)]="document.isSelectedForPreview"
                              [checked]="document.isSelectedForPreview"
                            >
                            </nb-checkbox>
                          </td>
                          <td data-title="File Name">
                            <div class="d-flex align-items-center">
                              <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                              <a [href]="document.fileUrl" target="_blank">
                                {{ document.fileName }}
                              </a>
                            </div>
                          </td>
                          <td data-title="tags">
                            <ng-container *ngIf="document?.fileTagTxt?.length">
                              <span class="tag-info-badge fw-bold" *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5">
                                <span class="px-2">
                                  {{ tagName }}
                                </span>
                              </span>
                              {{ document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : '' }}
                            </ng-container>
                            <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                          </td>
                          <td data-title="Note">
                            <div
                              *ngIf="document.notes"
                              nbTooltip="{{ document.notes.length > 600 ? (document.notes | slice : 0 : 600) + '...' : document.notes }}"
                              nbTooltipPlacement="top"
                              nbTooltipStatus="primary"
                            >
                              <sfl-read-more [content]="document.notes"></sfl-read-more>
                            </div>
                            <span *ngIf="!document.notes">N/A</span>
                          </td>
                          <td data-title="Uploaded By">{{ document.createdBy }}</td>
                          <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                          <td data-title="Action" class="text-center">
                            <div class="d-flex align-items-center justify-content-center">
                              <em
                                *ngIf="isEdit"
                                class="fa fa-edit text-primary cursor-pointer me-3"
                                (click)="openFileUploadSidePanel(true, document)"
                              ></em>
                              <em
                                class="fa fa-download text-primary cursor-pointer me-3"
                                (click)="downloadDropBoxFile(document.id, document.fileName)"
                              ></em>
                              <em
                                *ngIf="userRoles[0] !== 'customer'"
                                class="fa fa-trash text-danger cursor-pointer"
                                (click)="deleteDropBoxFile(document.id)"
                              ></em>
                            </div>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                    <tbody *ngIf="isCreate">
                      <ng-container *ngFor="let document of createFileUploadList">
                        <tr *ngIf="document.fileType === 'document'">
                          <td data-title="File Name">
                            <div class="d-flex align-items-center">
                              <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                              <span>
                                {{ document.fileName }}
                              </span>
                            </div>
                          </td>
                          <td data-title="tags">
                            <ng-container *ngIf="document?.fileTagTxt?.length">
                              <span class="tag-info-badge fw-bold" *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5">
                                <span class="px-2">
                                  {{ tagName }}
                                </span>
                              </span>
                              {{ document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : '' }}
                            </ng-container>
                            <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                          </td>
                          <td data-title="Note">
                            <div *ngIf="document.notes" nbTooltip="{{ document.notes }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                              <sfl-read-more [content]="document.notes"></sfl-read-more>
                            </div>
                            <span *ngIf="!document.notes">N/A</span>
                          </td>
                          <td data-title="Uploaded By">{{ document.createdBy }}</td>
                          <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                          <td data-title="Action" class="text-center">
                            <div class="d-flex align-items-center justify-content-center" *ngIf="userRoles[0] !== 'customer'">
                              <em class="fa fa-trash text-danger cursor-pointer" (click)="deleteDropBoxFile(document.id, true)"></em>
                            </div>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </table>
                  <ng-container *ngIf="(!fileAttachments?.fileGallery?.length && !isCreate) || (!createFileUploadList?.length && isCreate)">
                    <p class="no-record text-center">No Data Found</p>
                  </ng-container>
                </div>
                <div class="mt-2 d-md-flex align-items-center" *ngIf="fileAttachments?.fileGallery?.length && !isCreate">
                  <div class="d-flex align-items-center">
                    <label class="mb-0">Items per page: </label>
                    <ng-select
                      class="ms-2"
                      [(ngModel)]="filesPaginationParams.pageSize"
                      [clearable]="false"
                      [searchable]="false"
                      (change)="onChangeSize()"
                      name="documentsPageSize"
                      #documentsPageSize="ngModel"
                      appendTo="body"
                    >
                      <ng-option value="5">5</ng-option>
                      <ng-option value="10">10</ng-option>
                      <ng-option value="50">50</ng-option>
                      <ng-option value="100">100</ng-option>
                    </ng-select>
                  </div>
                  <strong class="ms-md-3">Total: {{ fileAttachments?.totalCount }}</strong>
                  <div class="ms-md-auto ms-sm-0">
                    <pagination-controls
                      id="documents"
                      (pageChange)="onPageChange($event)"
                      class="paginate ticket-attachment"
                    ></pagination-controls>
                  </div>
                </div>
              </div>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <nb-accordion class="mb-2 customer-note" [nbSpinner]="customerNotesLoading" *ngIf="userRoles[0] !== 'customer'">
        <nb-accordion-item [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head">Customer Notes </nb-accordion-item-header>
          <nb-accordion-item-body>
            <sfl-notes-listing
              [entityId]="id"
              [entityTypeId]="entityTypeId"
              [entityTypeName]="entityTypeName"
              [isEntityEditMode]="isEdit"
              [isEntityViewMode]="isDetail"
              [isEntityCreateMode]="isCreate"
              (notesListingLoadingEvent)="customerNotesLoading = $event"
            ></sfl-notes-listing>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
      <nb-accordion *ngIf="customer.isCustomerUserSelected">
        <nb-accordion-item [expanded]="true" class="border-bottom">
          <nb-accordion-item-header class="accordion_head"> Customer API Gateway </nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="row">
              <div class="col-12 col-md-4">
                <div class="form-control-group">
                  <label class="label"> Customer Key </label>
                  <div class="d-flex align-items-center">
                    <input nbInput placeholder="Key" [(ngModel)]="customer.customerKey" name="key" fullWidth />
                    <em
                      class="fa fa-refresh text-primary cursor-pointer ms-2"
                      nbTooltip="Regenerate"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      nbSuffix
                      (click)="regenerateKey()"
                    ></em>
                  </div>
                </div>
              </div>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </form>
  </nb-card-body>
</nb-card>

<ng-template #addRemoveFilesTagsModal>
  <div class="selcet-inverter">
    <div class="modal-header align-items-center">
      <h6 class="modal-title">Manage file tags</h6>
      <button type="button" class="close" aria-label="Close" (click)="addRemoveFilesTagsModalRef.hide(); searchText = ''">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="row align-items-center">
        <!-- <p><strong>Selected files :</strong> {{ selectedFilesNames }}</p> -->
        <div class="file-attachments">
          <strong>Selected files :</strong>
          <ng-container *ngIf="allSelectedFiles?.length">
            <span class="tag-info-badge fw-bold" *ngFor="let file of allSelectedFiles | slice : 0 : 3">
              <span class="px-2">
                {{ file?.fileName }}
              </span>
            </span>
            <span nbTooltip="{{ selectedFilesNamesString }}" nbtooltipplacement="top" nbTooltipStatus="primary">
              <span class="tag-info-badge fw-bold" *ngIf="allSelectedFiles?.length > 3">{{ '+' + (allSelectedFiles?.length - 3) }}</span>
            </span>
          </ng-container>
        </div>
        <div class="col-12 mt-2 file-tag-dd">
          <label class="label" for="fileTagsApply">Select file tags </label>
          <ng-select
            name="fileTagsApply"
            id="region-drop-down"
            class="sfl-track-dropdown"
            [multiple]="true"
            [items]="filesTagList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="fileTagIds"
            #fileTagsApply="ngModel"
            notFoundText="No File Tag Found"
            placeholder="Select File Tag"
            [closeOnSelect]="false"
            (search)="onFilter($event)"
            (ngModelChange)="reorderTags()"
            (close)="filteredAppliedTags = []"
          >
            <ng-template ng-header-tmp *ngIf="filesTagList && filesTagList.length">
              <button type="button" (click)="toggleSelectUnselectAllTags(true)" class="btn btn-sm btn-primary me-2">Select all</button>
              <button type="button" (click)="toggleSelectUnselectAllTags(false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
              {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div class="d-flex justify-content-end align-items-center">
        <button
          class="linear-mode-button me-3"
          nbButton
          status="secondary"
          size="small"
          (click)="addRemoveMultipleFilesTags(false)"
          type="button"
        >
          Remove Tags
        </button>
        <button
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          (click)="addRemoveMultipleFilesTags(true)"
          type="button"
        >
          Apply Tags
        </button>
      </div>
    </div>
  </div>
</ng-template>
