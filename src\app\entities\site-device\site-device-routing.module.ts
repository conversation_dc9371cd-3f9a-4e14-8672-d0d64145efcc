import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { ImportDeviceComponent } from './import-device/import-device.component';
import { SiteDeviceEditAddComponent } from './site-device-edit-add/site-device-edit-add.component';
import { SiteDeviceListingComponent } from './site-device-listing/site-device-listing.component';
import { SiteDeviceComponent } from './site-device.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: SiteDeviceComponent,
    data: { pageTitle: 'Devices' },
    children: [
      {
        path: '',
        redirectTo: 'list',
        data: { pageTitle: 'Devices' }
      },
      {
        path: 'list',
        component: SiteDeviceListingComponent,
        data: { pageTitle: 'Devices' }
      },
      {
        path: 'add',
        component: SiteDeviceEditAddComponent,
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
          pageTitle: 'Add Device'
        }
      },
      {
        path: 'edit/:id',
        component: SiteDeviceEditAddComponent,
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
          pageTitle: 'Update Device'
        }
      },
      {
        path: 'import',
        component: ImportDeviceComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SiteDeviceRoutingModule {}
