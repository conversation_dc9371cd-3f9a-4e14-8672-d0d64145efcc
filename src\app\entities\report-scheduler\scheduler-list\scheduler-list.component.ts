import { Component, OnInit } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { AddEditSchedulerComponent } from '../add-edit-scheduler/add-edit-scheduler.component';
import { ScheduledReports, SchedulerList } from '../report-scheduler.model';
import { ReportSchedulerService } from '../report-scheduler.service';
import { InactiveScheduleComponent } from './inactive-schedule/inactive-schedule.component';
import { ROLE_TYPE } from '../../../@shared/enums';

@Component({
  selector: 'sfl-scheduler-list',
  templateUrl: './scheduler-list.component.html',
  styleUrls: ['./scheduler-list.component.scss']
})
export class SchedulerListComponent implements OnInit {
  loading = false;
  subscription: Subscription = new Subscription();
  schedulerLists: ScheduledReports[] = [];
  modalRef: BsModalRef;
  userRoles: string;
  roleType = ROLE_TYPE;

  constructor(
    public readonly reportSchedulerService: ReportSchedulerService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.userRoles = this.storageService.get('user').authorities;
    this.getReportScheduleList();
  }

  getReportScheduleList() {
    const tempObj = {
      name: null,
      take: 500,
      skip: 0,
      sortBy: null,
      direction: 'asc'
    };
    this.loading = true;
    this.subscription.add(
      this.reportSchedulerService.getReportScheduleList(tempObj).subscribe({
        next: (res: SchedulerList) => {
          this.schedulerLists = res.scheduledReports;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  openSchedulerModel(mode, id = null) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        schedulerId: id,
        mode: mode
      }
    };
    this.modalRef = this.modalService.show(AddEditSchedulerComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        this.loading = false;
        this.alertService.showSuccessToast(`${id ? 'Successfully updated a schedule.' : 'Successfully created a schedule.'}`);
        this.getReportScheduleList();
      }
    });
  }

  onRunSchedule(scheduleId: number) {
    this.loading = true;
    this.subscription.add(
      this.reportSchedulerService.runScheduleForcefully(scheduleId).subscribe({
        next: () => {
          this.loading = false;
          this.alertService.showSuccessToast('Schedule run successfully.');
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onInactiveSchedule(scheduleId: number, scheduler) {
    const valuehold = !scheduler.status;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        action: 'active-inactive',
        schedulerId: scheduleId,
        isActive: scheduler.status
      }
    };
    this.modalRef = this.modalService.show(InactiveScheduleComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        this.loading = false;
        this.alertService.showSuccessToast(`${valuehold ? 'Successfully Inactivated' : 'Successfully Activated'}`);
        this.getReportScheduleList();
      } else {
        scheduler.status = valuehold;
      }
    });
  }

  onDelete(scheduleId: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        action: 'delete',
        schedulerId: scheduleId
      }
    };
    this.modalRef = this.modalService.show(InactiveScheduleComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        this.loading = false;
        this.alertService.showSuccessToast(`${'Successfully deleted'}`);
        this.getReportScheduleList();
      }
    });
  }
}
