import { Location } from '@angular/common';
import { Component, OnD<PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription, debounceTime, forkJoin } from 'rxjs';
import { CommonDropboxFileUploadComponent } from '../../../@shared/components/common-dropbox-file-upload/common-dropbox-file-upload.component';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { TagListResponseModel } from '../../../@shared/components/image-dropbox-gallery/drop-box.model';
import { APP_ROUTES, AppConstants } from '../../../@shared/constants';
import { FrequencyList } from '../../../@shared/models/assessment.model';
import {
  Customer,
  CustomerAvailabilityModel,
  CustomerOutageResponse,
  ExclusionType,
  Month,
  PRGenerateLogs
} from '../../../@shared/models/customer.model';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { DropboxImageGalleryService } from '../../../@shared/services/dropbox-image-gallery.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AssessmentService } from '../../assessment-management/assessment.service';
import { CustomerAPIGatewayService } from '../../customer-api-gateway/customer-api-gateway.service';
import { NotesEntityName, NotesEntityType } from '../../notes-management/notes-management.model';
import { ProfileService } from '../../profile/profile.service';
import { AttachmentListResponse, FileListPaginationParams } from '../../ticket-management/ticket.model';
import { CustomerService } from '../customer.service';
import { DataSharingService } from '../datasharing.service';
import { CustomerSiteInfoArchiveComponent } from './customer-site-info-archive/customer-site-info-archive.component';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-customer-add-edit',
  templateUrl: './customer-add-edit.component.html',
  styleUrls: ['./customer-add-edit.component.scss']
})
export class CustomerAddEditComponent implements OnInit, OnDestroy {
  customer: Customer = new Customer();
  customerOutage: CustomerOutageResponse = new CustomerOutageResponse();
  loading = false;
  subscription: Subscription = new Subscription();
  isEdit = false;
  isDetail = false;
  isCreate = false;
  contactNoFormat = AppConstants.phoneNumberMask;
  frequency: FrequencyList[] = [];
  siteVisits: FrequencyList[] = [];
  electricalIVs: FrequencyList[] = [];
  electricalVOCs: FrequencyList[] = [];
  thermals: FrequencyList[] = [];
  aerialScans: FrequencyList[] = [];
  inverterPMs: FrequencyList[] = [];
  mediumVoltagePMs: FrequencyList[] = [];
  vegetations: FrequencyList[] = [];
  monitorings: FrequencyList[] = [];
  performanceReportings: FrequencyList[] = [];
  tpms: FrequencyList[] = [];
  trqs: FrequencyList[] = [];
  mvThermals: FrequencyList[] = [];
  auditLogs: PRGenerateLogs[] = [];
  modalRef: BsModalRef;
  id: number;
  count = 0;
  showExclusion = false;
  userRoles: string;
  PRGenerateWorkDaysList: number[] = [
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30
  ];
  logsLoading = false;
  isAutoGeneratePR = false;
  updatedData: any[] = [];
  currentIsActiveStatusInBE = false;
  attachmentsLoading = false;
  createFileUploadList = [];
  fileAttachments: AttachmentListResponse = new AttachmentListResponse();
  filesPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  isSelectedAllFiles = false;
  filesTagList: TagListResponseModel[] = [];
  filteredAppliedTags = [];
  allSelectedFiles = [];
  fileTagIds = [];
  selectedFilesNamesString: string;
  fileSearchText = '';
  fileSearchModelChanged = new Subject<string>();
  addRemoveFilesTagsModalRef: BsModalRef;
  sortOptionList = {
    fileName: 'asc',
    createdDate: 'asc'
  };
  filterModel: { page: number; items: number; sortBy: string; direction: string } = { page: 0, items: 10, direction: '', sortBy: '' };
  fullDateFormat = AppConstants.fullDateFormat;
  customerNotesLoading = false;
  entityTypeName = NotesEntityName[NotesEntityType.CUSTOMERS];
  entityTypeId = NotesEntityType.CUSTOMERS;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly customerService: CustomerService,
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly route: ActivatedRoute,
    private readonly assessmentService: AssessmentService,
    private readonly _location: Location,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService,
    private readonly customerAPIGatewayService: CustomerAPIGatewayService,
    private readonly dataSharingService: DataSharingService,
    private readonly profileService: ProfileService,
    private readonly commonService: CommonService,
    private readonly dropBoxService: DropboxImageGalleryService
  ) {
    this.dataSharingService.updatedData$.subscribe(updatedData => {
      if (updatedData && updatedData.length) {
        this.updatedData = updatedData;
        this.customer.isArchive = updatedData[0].isArchive;
        this.customer.isActive = updatedData[0].isActive;
      }
    });
  }

  ngOnInit() {
    this.getAllFrequency();
    this.userRoles = this.storageService.get('user').authorities;
    this.route.params.subscribe(params => {
      if (params && params.id) {
        this.id = params.id;
        this.getCustomer(params.id);
        if (params.mode === 'edit') {
          this.isEdit = true;
        } else {
          this.isDetail = true;
        }
      } else {
        this.isCreate = true;
        this.isDetail = false;
        this.isEdit = false;
      }
    });
    this.commonService.commonUploadFinish$.subscribe(res => {
      if (res) {
        this.getFilesAttachmentsList();
      }
    });

    this.fileSearchModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.getFilesAttachmentsList();
    });
  }

  getMonthName(month: number): string {
    switch (month) {
      case 1:
        return Month.JANUARY;
      case 2:
        return Month.FEBRUARY;
      case 3:
        return Month.MARCH;
      case 4:
        return Month.APRIL;
      case 5:
        return Month.MAY;
      case 6:
        return Month.JUNE;
      case 7:
        return Month.JULY;
      case 8:
        return Month.AUGUST;
      case 9:
        return Month.SEPTEMBER;
      case 10:
        return Month.OCTOBER;
      case 11:
        return Month.NOVEMBER;
      case 12:
        return Month.DECEMBER;
    }
  }
  // Get By Customer Id
  getCustomer(id) {
    this.loading = true;
    this.subscription.add(
      this.customerService.getById(id).subscribe({
        next: (res: Customer) => {
          this.id = id ? id : this.customer.id;
          if (!res.customerAvailabilityDetail) {
            res.customerAvailabilityDetail = new CustomerAvailabilityModel();
          }
          this.customer = res;
          this.customerOutage = res.outageSetting;

          this.isAutoGeneratePR = res.isAutoGeneratePR;
          this.currentIsActiveStatusInBE = res.isActive;
          if (this.customer.isAutoGeneratePR) {
            this.getPRGenerateLogs();
          }
          this.getFilesAttachmentsList();
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  activeToggleChange(event) {
    if (event === true && !this.currentIsActiveStatusInBE) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-full-dialog',

        initialState: {
          customerId: this.id,
          archiveModalFrom: 'customer'
        }
      };
      this.modalRef = this.modalService.show(CustomerSiteInfoArchiveComponent, ngModalOptions);
    } else {
      this.storageService.clear('siteDashboardPage');
      this.storageService.clear('dashboardPage');
    }
  }

  // Get Add and Edit Tittle
  getComponentTitle() {
    let result = 'Add Customer';
    if (!this.customer.id) {
      return result;
    }
    if (this.isDetail) {
      result = 'Customer Details';
      return result;
    } else {
      result = `Edit Customer`;
      return result;
    }
  }

  addExclusion() {
    if (!this.customer.listOfAvailabilityExclusionType.length) {
      const newExclusions: ExclusionType[] = [
        { id: 0, exclusionType: 'Snow Cover', exclusionIndex: 1, isActive: true },
        { id: 0, exclusionType: 'Grid Outage', exclusionIndex: 2, isActive: true }
      ];
      this.customer.listOfAvailabilityExclusionType = newExclusions;
    } else {
      const newExclusion: ExclusionType = new ExclusionType();
      newExclusion.exclusionIndex = this.customer.listOfAvailabilityExclusionType.length + 1;
      this.customer.listOfAvailabilityExclusionType.push(newExclusion);
    }
  }

  onAutoGeneratePRChange(event) {
    if (event) {
      this.customer.prGenerateWorkDays = 10;
      if (this.isAutoGeneratePR) {
        this.getPRGenerateLogs();
      }
    } else {
      delete this.customer.prGenerateWorkDays;
    }
  }

  // Create and Update Customer
  createCustomer() {
    this.customer.customerAvailabilityDetail.availabilityGuarantee = Number(this.customer.customerAvailabilityDetail.availabilityGuarantee);
    this.customer.customerAvailabilityDetail.gracePeriod = Number(this.customer.customerAvailabilityDetail.gracePeriod);
    this.customer.customerAvailabilityDetail.irradianceThreshold = Number(this.customer.customerAvailabilityDetail.irradianceThreshold);
    this.customer.customerAvailabilityDetail.powerThreshold = Number(this.customer.customerAvailabilityDetail.powerThreshold);
    this.customer.outageSetting = this.customerOutage;
    this.customer.archiveUpdatedData = this.updatedData;
    if (!this.customer.id) {
      this.loading = true;
      this.customer.isActive = true;
      this.customer.isArchive = false;
      this.subscription.add(
        this.customerService.createCustomer(this.customer).subscribe({
          next: res => {
            if (this.createFileUploadList.length) {
              this.uploadFiles(res.entryid, res.message);
            } else {
              this.router.navigateByUrl(APP_ROUTES.CUSTOMERS);
              this.alertService.showSuccessToast(res.message);
            }
            this.loading = false;
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    } else {
      if (this.customer.isArchive) {
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          initialState: {
            message: `${this.customer.customerName} will be archived along with all ${this.customer.customerName}'s portfolios, sites and devices. Are you sure you want to save?`
          }
        };
        this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
        this.modalRef.content.onClose.subscribe(result => {
          if (result) {
            this.updateCustomerAfterArchiveConfirmation();
          }
        });
      } else {
        this.updateCustomerAfterArchiveConfirmation();
      }
    }
  }

  uploadFiles(id, msg?: string) {
    const tempArray = [];
    if (this.createFileUploadList.length) {
      for (const fileObj of this.createFileUploadList) {
        const formData: FormData = new FormData();
        formData.append('files', fileObj.file as File);
        if (fileObj.fileTag.length) {
          for (const tag of fileObj.fileTag) {
            formData.append('fileTagIds', `${tag}`);
          }
        }
        formData.append('customerId', `${id}`);
        formData.append('id', '0');
        formData.append('portfolioId', null);
        formData.append('siteId', null);
        formData.append('entityId', `${id}`);
        formData.append('entityNumber', '');
        formData.append('moduleType', '11');
        formData.append('fileType', `${fileObj.fileType}`);
        if (fileObj.notes) {
          formData.append('notes', `${fileObj.notes}`);
        }
        tempArray.push(this.dropBoxService.uploadFilesToGallery(formData));
      }
    }
    this.subscription.add(
      forkJoin(tempArray).subscribe({
        next: res => {
          this.createFileUploadList = [];
          this.router.navigateByUrl(APP_ROUTES.CUSTOMERS);
          this.alertService.showSuccessToast(msg);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  updateCustomerAfterArchiveConfirmation() {
    this.loading = true;
    this.subscription.add(
      this.customerService.updateCustomer(this.customer).subscribe({
        next: res => {
          this.updatedData = [];
          this.dataSharingService.updateData(this.updatedData);
          this.router.navigateByUrl(APP_ROUTES.CUSTOMERS);
          this.dataSharingService.clearCachedCusPortSites();
          this.loading = false;
          this.alertService.showSuccessToast(res.message);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  // get All Frequency
  getAllFrequency() {
    this.loading = true;
    this.subscription.add(
      this.assessmentService.getAllFrequency().subscribe({
        next: res => {
          this.frequency = res;
          this.loading = false;
          this.getAllFrequencyList();
          if (this.siteVisits.length !== 0) {
            this.customer.taskDetails.siteVisit = this.siteVisits[this.siteVisits.length - 1].id;
          }
          if (this.electricalIVs.length !== 0) {
            this.customer.taskDetails.electricalIV = this.electricalIVs[this.electricalIVs.length - 1].id;
          }
          if (this.electricalVOCs.length !== 0) {
            this.customer.taskDetails.electricalVOC = this.electricalVOCs[this.electricalVOCs.length - 1].id;
          }
          if (this.thermals.length !== 0) {
            this.customer.taskDetails.thermal = this.thermals[this.thermals.length - 1].id;
          }
          if (this.trqs.length !== 0) {
            this.customer.taskDetails.trq = this.trqs[this.trqs.length - 1].id;
          }
          if (this.mvThermals.length !== 0) {
            this.customer.taskDetails.mvth = this.mvThermals[this.mvThermals.length - 1].id;
          }
          if (this.aerialScans.length !== 0) {
            this.customer.taskDetails.aerialScan = this.aerialScans[this.aerialScans.length - 1].id;
          }
          if (this.inverterPMs.length !== 0) {
            this.customer.taskDetails.inverterPM = this.inverterPMs[this.inverterPMs.length - 1].id;
          }
          if (this.mediumVoltagePMs.length !== 0) {
            this.customer.taskDetails.mvpm = this.mediumVoltagePMs[this.mediumVoltagePMs.length - 1].id;
          }
          if (this.vegetations.length !== 0) {
            this.customer.taskDetails.vegetation = this.vegetations[this.vegetations.length - 1].id;
          }
          if (this.monitorings.length !== 0) {
            this.customer.taskDetails.monitoing = this.monitorings[this.monitorings.length - 1].id;
          }
          if (this.performanceReportings.length !== 0) {
            this.customer.taskDetails.performanceReporting = this.performanceReportings[this.performanceReportings.length - 1].id;
          }
          if (this.tpms.length !== 0) {
            this.customer.taskDetails.tpm = this.tpms[this.tpms.length - 1].id;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  // get All FrequencyData
  getAllFrequencyList() {
    this.frequency.forEach(element => {
      if (element.siteVisit) {
        this.siteVisits.push(element);
      }
      if (element.electricalIvCurve) {
        this.electricalIVs.push(element);
      }
      if (element.electricalVocImp) {
        this.electricalVOCs.push(element);
      }
      if (element.thermal) {
        this.thermals.push(element);
      }
      if (element.aerialScan) {
        this.aerialScans.push(element);
      }
      if (element.inverterPM) {
        this.inverterPMs.push(element);
      }
      if (element.mediumVoltagePM) {
        this.mediumVoltagePMs.push(element);
      }
      if (element.vegetation) {
        this.vegetations.push(element);
      }
      if (element.monitoring) {
        this.monitorings.push(element);
      }
      if (element.performanceReporting) {
        this.performanceReportings.push(element);
      }
      if (element.tpm) {
        this.tpms.push(element);
      }
      if (element.trq) {
        this.trqs.push(element);
      }
      if (element.mvth) {
        this.mvThermals.push(element);
      }
    });
  }

  onDelete(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete this customer?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe({
        next: result => {
          if (result) {
            this.loading = true;
            this.subscription.add(
              this.customerService.deleteCustomer(event).subscribe({
                next: res => {
                  if (res) {
                    this.alertService.showSuccessToast(res.message);
                    this.profileService.setDefaultFilterSelection();
                    this.goBack();
                    this.loading = false;
                  }
                },
                error: e => (this.loading = false)
              })
            );
          }
        },
        error: () => (this.loading = false)
      });
    }
  }

  goBack() {
    this._location.back();
  }

  getPRGenerateLogs() {
    this.logsLoading = true;
    this.customerService.getPRGenerateLogs(this.id).subscribe({
      next: res => {
        this.auditLogs = res;
        this.logsLoading = false;
      },
      error: () => (this.logsLoading = false)
    });
  }

  checkAutoMapPRWorkOrder() {
    this.logsLoading = true;
    this.customerService.checkAutoMapPRWorkOrder(this.id).subscribe({
      next: res => {
        this.alertService.showSuccessToast(res.message);
        this.getPRGenerateLogs();
      },
      error: () => (this.logsLoading = false)
    });
  }

  regenerateKey() {
    this.loading = true;

    this.subscription.add(
      this.customerAPIGatewayService.regenerateCustomerAPI(this.customer.id, true).subscribe({
        next: (response: string) => {
          this.customer.customerKey = response;
          this.loading = false;
        },
        error: () => (this.loading = false)
      })
    );
  }

  formatTime(time: string, index: number, fieldToUpdate: string) {
    const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (timePattern.test(time)) {
      this.customerOutage.timeSetting[index][fieldToUpdate] = time;
    } else if (time.charAt(2) === ':') {
      this.customerOutage.timeSetting[index][fieldToUpdate] = time;
    } else if (time.length === 4) {
      const hours = time.substring(0, 2);
      const minutes = time.substring(2, 4);
      const formattedTime = `${hours}:${minutes}`;
      this.customerOutage.timeSetting[index][fieldToUpdate] = formattedTime;
    }
  }

  //Below code is for Site Files dropbox 2890 jira ticket

  getFilesAttachmentsList(requestParams = null) {
    this.attachmentsLoading = true;
    const getListingParams = {
      siteId: null,
      customerId: Number(this.id),
      portfolioId: null,
      entityId: Number(this.id),
      entityNumber: '',
      parentId: null,
      fileType: 'document',
      imagePreviewId: 0,
      isCustomerFacing: false,
      moduleType: 11,
      page: this.filterModel.page,
      sortBy: this.filterModel.sortBy,
      direction: this.filterModel.direction,
      itemsCount: this.filterModel.items,
      search: this.fileSearchText
    };
    this.subscription.add(
      this.dropBoxService.getGalleryImageFiles(getListingParams).subscribe({
        next: (res: AttachmentListResponse) => {
          let selectedForPreviewCount = 0;
          this.isSelectedAllFiles = false;
          const updatedFileGallery = res.fileGallery.map(file => {
            const isSelected = this.allSelectedFiles.some(selectedFile => selectedFile.id === file.id);
            if (isSelected) {
              selectedForPreviewCount++;
            }
            return {
              ...file,
              isSelectedForPreview: isSelected
            };
          });

          this.fileAttachments = {
            totalCount: res.totalCount,
            fileGallery: [...updatedFileGallery]
          };
          if (checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
            this.fileAttachments.fileGallery.forEach(file => {
              file.fileTagTxt = file.fileTagTxt.filter(tag => tag !== 'Customer Facing');
            });
          }
          this.isSelectedAllFiles = this.fileAttachments.fileGallery.length === selectedForPreviewCount;
          // this.cdr.detectChanges();
          this.attachmentsLoading = false;
        },
        error: err => {
          this.attachmentsLoading = false;
        }
      })
    );
  }

  openFileUploadSidePanel(mode, fileItem) {
    const entityDetails = {
      customerId: Number(this.id),
      portfolioId: null,
      siteId: null,
      entityId: Number(this.id),
      entityNumber: '',
      moduleType: 11
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        isFileEditMode: mode,
        fileItemObj: fileItem,
        entityDetails: entityDetails,
        parentModuleName: 'Site',
        isParentCreateMode: this.isCreate ? true : false
      }
    };
    this.modalRef = this.modalService.show(CommonDropboxFileUploadComponent, ngModalOptions);
    if (this.isCreate) {
      this.modalRef.content.fileUploadList.subscribe(res => {
        if (res && res.length) {
          this.createFileUploadList.push(...res);
        }
      });
    } else {
      this.modalRef.content.isParentRefresh.subscribe(res => {
        if (res) {
          this.getFilesAttachmentsList();
        }
      });
    }
  }

  downloadDropBoxFile(fileId, fileName) {
    this.loading = true;
    this.dropBoxService.downloadPreviewedImage(fileId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, data.type);
          link.download = fileName;
          link.click();
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  deleteDropBoxFile(fileId, isCreateMode = false) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (isCreateMode) {
          this.createFileUploadList = this.createFileUploadList.filter(item => item.id !== fileId);
        } else {
          this.subscription.add(
            this.dropBoxService.deleteImageGalleryFiles(fileId).subscribe({
              next: data => {
                this.alertService.showSuccessToast(`file deleted Successfully.`);
                this.getFilesAttachmentsList();
                this.loading = false;
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      }
    });
  }

  onPageChange(obj) {
    this.filesPaginationParams.currentPage = obj;

    this.filterModel.items = this.filesPaginationParams.itemsCount;
    this.filterModel.page = obj - 1;
    this.getFilesAttachmentsList();
  }

  onChangeSize() {
    this.filesPaginationParams.itemsCount = Number(this.filesPaginationParams.pageSize);
    this.filterModel.page = this.filesPaginationParams.currentPage = 0;
    this.filterModel.items = this.filesPaginationParams.itemsCount;
    this.getFilesAttachmentsList();
  }

  selectAllFiles() {
    if (this.isSelectedAllFiles) {
      // If selecting all files
      for (const element of this.fileAttachments.fileGallery) {
        element.isSelectedForPreview = true; // Mark as selected
        if (!this.allSelectedFiles.some(file => file.id === element.id)) {
          this.allSelectedFiles.push(element);
        }
      }
    } else {
      // If deselecting all files
      for (const element of this.fileAttachments.fileGallery) {
        element.isSelectedForPreview = false; // Mark as deselected
        // Remove from allSelectedFiles if it exists
        const index = this.allSelectedFiles.findIndex(file => file.id === element.id);
        if (index !== -1) {
          this.allSelectedFiles.splice(index, 1);
        }
      }
    }
  }

  singleFilesCheckChanged(files) {
    if (files.isSelectedForPreview) {
      // Add the file only if it's not already in allSelectedFiles
      if (!this.allSelectedFiles.includes(files)) {
        this.allSelectedFiles.push(files);
      }
    } else {
      // If the file is not selected for preview, remove it from allSelectedFiles
      const index = this.allSelectedFiles.findIndex(file => file.id === files.id);
      if (index !== -1) {
        this.allSelectedFiles.splice(index, 1);
      }
    }

    this.isSelectedAllFiles = this.fileAttachments.fileGallery.every(file => this.allSelectedFiles.includes(file));
  }

  fileSearchChanged() {
    this.fileSearchModelChanged.next(null);
  }

  getFilesTagList() {
    this.loading = true;
    this.dropBoxService.getFileTagList().subscribe({
      next: data => {
        if (data) {
          this.filesTagList = data;
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  openEditTagsModal(template: TemplateRef<any>) {
    if (this.allSelectedFiles) {
      this.getFilesTagList();
      this.selectedFilesNamesString = this.allSelectedFiles.map(item => item.fileName).join(', ');
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-md'
      };
      setTimeout(() => {
        this.addRemoveFilesTagsModalRef = this.modalService.show(template, ngModalOptions);
      }, 0);
    } else {
      this.alertService.showWarningToast('Please select at least one file.');
    }
  }

  addRemoveMultipleFilesTags(isApplyTags: boolean) {
    const filesIds: number[] = this.allSelectedFiles.filter(item => item.isSelectedForPreview === true).map(item => item.id);
    const multipleImagesTags = {
      filesIds: filesIds,
      fileTagIds: this.fileTagIds,
      isApplyTags: isApplyTags
    };
    if (filesIds && filesIds.length && this.fileTagIds && this.fileTagIds.length) {
      this.addRemoveTags(multipleImagesTags);
    } else {
      this.alertService.showWarningToast(`Please select at least one file tag.`);
    }
  }

  addRemoveTags(filesTagsParams) {
    this.loading = true;
    this.subscription.add(
      this.dropBoxService.applyTagsToFiles(filesTagsParams).subscribe({
        next: res => {
          this.addRemoveFilesTagsModalRef.hide();
          this.loading = false;
          this.allSelectedFiles = [];
          this.fileTagIds = [];
          this.isSelectedAllFiles = false;
          this.filesPaginationParams.currentPage = 1;
          this.alertService.showSuccessToast(`Tags updated successfully.`);
          this.getFilesAttachmentsList();
        },
        error: err => {
          this.loading = false;
          this.isSelectedAllFiles = false;
          this.allSelectedFiles = [];
        }
      })
    );
  }

  onFilter(event: any) {
    if (event.term) {
      this.filteredAppliedTags = event.items?.map(element => element.id);
    } else {
      this.filteredAppliedTags = [];
    }
  }

  toggleSelectUnselectAllTags(isSelect = false) {
    if (isSelect) {
      if (!this.filteredAppliedTags.length) {
        this.fileTagIds = this.filesTagList.map(site => site.id);
      } else {
        if (!Array.isArray(this.fileTagIds)) {
          this.fileTagIds = [];
        }
        this.fileTagIds = [...new Set([...this.fileTagIds, ...JSON.parse(JSON.stringify(this.filteredAppliedTags))])];
      }
    } else {
      if (this.filteredAppliedTags.length) {
        this.fileTagIds = this.fileTagIds.filter(x => !this.filteredAppliedTags.includes(x));
      } else {
        this.fileTagIds = [];
      }
    }
  }

  reorderTags() {
    const selectedTags = this.filesTagList.filter(tag => this.fileTagIds.includes(tag.id));
    const unselectedTags = this.filesTagList.filter(tag => !this.fileTagIds.includes(tag.id));
    this.filesTagList = [...selectedTags, ...unselectedTags];
  }

  sortFiles(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.filterModel.items = this.filesPaginationParams.pageSize;
    this.filterModel.page = this.filesPaginationParams.currentPage - 1;
    const params = {
      sortBy: this.filterModel.sortBy,
      direction: this.filterModel.direction,
      itemsCount: this.filterModel.items,
      page: this.filterModel.page
    };
    this.getFilesAttachmentsList(params);
  }

  // Destroy
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
