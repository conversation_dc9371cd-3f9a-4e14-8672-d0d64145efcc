import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription, forkJoin } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants/app.constant';
import { AllReportDropdown } from '../../../@shared/models/report.model';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter } from '../../site-device/site-device.model';
import {
  AuditDispatch,
  AuditDispatchDetailFilter,
  AuditDispatchFilterList,
  AuditReportList,
  cmTicketAuditReportPageFilterKeys
} from '../cm-reports.model';
import { CmReportsService } from '../cm-reports.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-audit-dispatch',
  templateUrl: './audit-dispatch.component.html',
  styleUrls: ['./audit-dispatch.component.scss']
})
export class AuditDispatchComponent implements OnInit {
  loading = false;
  auditDispatchList: AuditDispatch[] = [];
  subscription: Subscription = new Subscription();
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  total = 10;
  viewPage = FILTER_PAGE_NAME.CM_TICKET_AUDIT_LISTING;
  filterList = AuditDispatchFilterList;
  viewFilterSection = 'auditDispatchFilterSection';
  sortOptionList = {
    Number: 'asc',
    Priority: 'asc',
    Site: 'asc',
    Device: 'asc',
    Close: 'asc',
    Open: 'desc',
    RegionName: 'desc',
    SubRegionName: 'desc',
    CustomerPortfolio: 'asc',
    TicketType: 'asc'
  };
  fullDateFormat = AppConstants.fullDateFormat;
  filterModelDetail: AuditDispatchDetailFilter = new AuditDispatchDetailFilter();
  ticketIds: number[] = [];
  isMasterSel = false;
  allReportDropdown = new AllReportDropdown();
  filterDetails: FilterDetails = new FilterDetails();
  userRole = this.storageService.get('user').authorities;

  constructor(
    private readonly router: Router,
    private readonly datePipe: DatePipe,
    private readonly storageService: StorageService,
    private readonly cmReportsService: CmReportsService
  ) {}

  ngOnInit() {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection),
      sharedFilter = this.storageService.get(AppConstants.SHARED_FILTER_KEY);
    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.initFilterDetails();
    this.filterModel.direction = 'desc';
    this.filterModel.itemsCount = this.pageSize;
    this.isFilterDisplay = filterSection;

    this.filterModel.portfolioIds = sharedFilter?.portfolioIds || this.filterModel.portfolioIds;
    this.filterModel.siteIds = sharedFilter?.siteIds || this.filterModel.siteIds;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    model.isExport = false;

    if (model.closeDate && model.closeDate.start && model.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(model?.closeDate?.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(model?.closeDate?.end, AppConstants.fullDateFormat);
    }

    if (
      this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, sharedFilter, cmTicketAuditReportPageFilterKeys)
    ) {
      const tempArray = [this.cmReportsService.getAllAuditDispatchList(model)],
        tempArrayObj = ['bindAuditDispatchList'];

      this.getAllLists(tempArray, tempArrayObj);
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.CLOSE_DATE.show = true;
    filterItem.TICKET_TYPE.show = true;
    filterItem.TICKET_TYPE.multi = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.default_direction = 'desc';
    this.filterDetails.filter_item = filterItem;
  }

  getAllLists(apiArray: any, mapResultList: string[], filterArrayObj = []) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          if (value === 'bindAuditDispatchList') {
            this.bindAuditDispatch(res[index]);
          } else {
            this[value] = res[index];
          }
        }
        setTimeout(() => {
          this.loading = false;
        }, 1000);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getAllAuditDispatchList(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.closeDate = null;
      model.closeDate = null;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    model.isExport = false;

    this.cmReportsService.getAllAuditDispatchList(model).subscribe({
      next: (res: AuditReportList) => {
        this.bindAuditDispatch(res);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  bindAuditDispatch(res: AuditReportList) {
    this.auditDispatchList = res && res.ticketLists ? res.ticketLists : [];
    this.total = res && res.totalTickets ? res.totalTickets : 0;
    setTimeout(() => {
      this.loading = false;
    }, 0);
  }

  closeDateChanged(event) {
    if ((event && event.start && event.end) || event === null) {
      this.resetPage();
      this.getAllAuditDispatchList();
    }
  }

  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
  }

  // Pagesize Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllAuditDispatchList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllAuditDispatchList();
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllAuditDispatchList();
  }

  clearFilter() {
    this.resetPage();
    this.filterModel = new CommonFilter();
    this.filterModel.itemsCount = this.pageSize;
    this.appliedFilter = [];
    this.getAllAuditDispatchList();
  }

  toggleFilter() {
    this.isFilterDisplay = !this.isFilterDisplay;
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
  }

  selectDeselectAll() {
    this.auditDispatchList.forEach(element => {
      element.isSelected = this.isMasterSel;
    });
    this.getCheckedItemList();
  }

  selectDeselectTicket() {
    this.isMasterSel = this.auditDispatchList.every(function (item: any) {
      return item.isSelected === true;
    });
    this.getCheckedItemList();
  }

  getCheckedItemList() {
    this.ticketIds = [];
    for (const auditDispatchItem of this.auditDispatchList) {
      if (auditDispatchItem.isSelected) {
        this.ticketIds.push(auditDispatchItem.id);
      }
    }
  }

  viewSend(isNewWindow?) {
    const model: AuditDispatchDetailFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.isMasterSel) {
      model.ticketIds = [];
      model.isSelectAll = true;
      this.filterModel.customerIds = [];
      if (this.filterModel.customerId) {
        this.filterModel.customerIds.push(this.filterModel.customerId);
      }
      if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
        model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
        model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
      }
      model.itemsCount = this.total;
      model.page = 0;
      this.cmReportsService.setValue(model);
    } else {
      model.ticketIds = this.ticketIds;
      model.isSelectAll = false;
      this.cmReportsService.setValue(model);
    }
    const url = this.router.serializeUrl(this.router.createUrlTree(['/entities/cm-reports/audit-dispatch/list']));
    if (isNewWindow) {
      window.open(url, '_blank', 'width=' + screen.availWidth + ',height=' + screen.availHeight);
    } else if (isNewWindow === false) {
      window.open(url, '_blank');
    } else if (isNewWindow === undefined) {
      this.router.navigateByUrl(url);
    }
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllAuditDispatchList(true, filterParams);
  }
}
