import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { DateToUsersTimezonePipe } from '../../../@shared/pipes/date-to-users-timezone.pipe';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { NavigationBack } from '../../ticket-management/ticket.model';
import { CmReportsService } from '../cm-reports.service';
import { cmRMAReportPage<PERSON>ilterKeys, RmaReportsList } from './rma-report.model';
import { ViewRmaDetailsComponent } from './view-rma-details/view-rma-details.component';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-rma-report',
  templateUrl: './rma-report.component.html',
  styleUrls: ['./rma-report.component.scss']
})
export class RMAReportComponent implements OnInit, OnDestroy {
  loading = false;
  currentPage = 1;
  isFilterDisplay = false;
  filterModel: CommonFilter = new CommonFilter();
  subscription: Subscription = new Subscription();
  RmaReports: RmaReportsList[] = [];
  filterDetails: FilterDetails = new FilterDetails();
  viewPage = FILTER_PAGE_NAME.CM_RMA_REPORT_LISTING;
  viewFilterSection = 'rmaReportSection';
  total = 10;
  modalRef: BsModalRef;
  pageSize = AppConstants.rowsPerPage;
  sortOptionList = {
    ticketId: 'asc',
    customerPortfolio: 'asc',
    siteName: 'asc',
    isReturnRequired: 'asc',
    deviceName: 'asc',
    deviceMFG: 'asc',
    rmaId: 'asc',
    deviceSerialNumber: 'asc',
    tracking: 'asc',
    startDate: 'asc',
    completeDate: 'asc',
    duration: 'asc',
    isRMAComplete: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc'
  };
  userRole = this.storageService.get('user').authorities;

  constructor(
    public readonly cmReportsService: CmReportsService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly dateToUsersTimezonePipe: DateToUsersTimezonePipe,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.initFilterDetails();
    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection);
    const localFilterData = this.storageService.get('userDefaultFilter');
    const defaultFilterData = this.storageService.get('user').userFilterSelection;
    this.isFilterDisplay = filterSection;
    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;

      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
    if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, cmRMAReportPageFilterKeys)) {
      this.getRmaReportDetail();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.SITE.show = true;
    filterItem.SITE.multi = true;
    filterItem.MFG.show = true;
    filterItem.SEARCH_BOX.show = true;
    filterItem.RMA_RETURN_REQUIRED.show = true;
    filterItem.RMA_COMPLETE.show = true;
    filterItem.RMA_TRACKING.show = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterModel.direction = 'desc';
    this.filterModel.sortBy = 'StartDate';
    this.filterDetails.default_direction = 'StartDate';
    this.filterDetails.default_sort = 'desc';
    this.filterDetails.filter_item = filterItem;
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getRmaReportDetail(true, filterParams);
  }

  getRmaReportDetail(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.cmReportsService.getRMAReportDetails(model).subscribe({
        next: res => {
          this.RmaReports = res.listData;
          this.total = res.totalRecords;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getRmaReportDetail();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getRmaReportDetail();
  }

  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getRmaReportDetail();
  }

  viewRmaDetails(rmaId: number) {
    this.loading = true;
    this.subscription.add(
      this.cmReportsService.getRmaDetailsById(rmaId).subscribe({
        next: (res: RmaReportsList) => {
          this.loading = false;
          const ngModalOptions: ModalOptions = {
            backdrop: 'static',
            keyboard: false,
            animated: true,
            class: 'modal-lg modal-dialog-right',
            initialState: {
              rmaDetails: res
            }
          };
          this.modalRef = this.modalService.show(ViewRmaDetailsComponent, ngModalOptions);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  exportData() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    model.itemsCount = this.total;
    model.page = 0;
    model.isExport = true;
    this.subscription.add(
      this.cmReportsService.getRMAReportDetails(model).subscribe({
        next: data => {
          const tittle = 'RMA Report';
          const rows: any = [
            [
              'Ticket Number',
              'Customer',
              'Portfolio',
              'Site',
              'Return Required',
              'Device',
              'MFG',
              'RMA Number',
              'SN',
              'Tracking',
              'Start Date',
              'Complete Date',
              'Duration',
              'Complete'
            ]
          ];
          for (const i of data.listData) {
            const tempData = [
              i.ticketNumber,
              i.customerName,
              i.portfolioName,
              i.siteName,
              i.isReturnRequired === 1 ? 'Yes' : 'No',
              i.deviceName,
              i.deviceMFG,
              i.rmaNumber,
              i.deviceSerialNumber,
              i.returnTrackingNumber,
              `${this.dateToUsersTimezonePipe.transform(i.startDate, AppConstants.momentDateFormat)}`,
              i.completeDate !== '-' ? `${this.dateToUsersTimezonePipe.transform(i.completeDate, AppConstants.momentDateFormat)}` : '-',
              i.duration,
              i.isRMAComplete ? 'Yes' : 'No'
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  openLink(id: number, inNewWindow: boolean) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['../entities/ticket/detail/view', `${id}`], { queryParams: { back: NavigationBack.RMA_REPORT } })
    );
    if (inNewWindow) {
      window.open(url, '_blank', 'width=' + screen.availWidth + ',height=' + screen.availHeight);
    } else {
      window.open(url, '_blank');
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
