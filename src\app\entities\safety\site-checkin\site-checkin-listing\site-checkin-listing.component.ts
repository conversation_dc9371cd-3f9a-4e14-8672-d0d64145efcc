import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../../@shared/constants';
import { AlertService } from '../../../../@shared/services';
import { CommonService } from '../../../../@shared/services/common.service';
import { StorageService } from '../../../../@shared/services/storage.service';
import { safetySiteCheckInListPageFilterKeys, SiteCheckIn, SiteCheckInFilterData } from '../site-checkin.model';
import { SiteCheckInService } from '../site-checkin.service';
import { ROLE_TYPE } from '../../../../@shared/enums';

@Component({
  selector: 'sfl-site-checkin-listing',
  templateUrl: './site-checkin-listing.component.html',
  styleUrls: ['./site-checkin-listing.component.scss']
})
export class SiteCheckInListingComponent implements OnInit {
  loading = false;
  filterDetails: FilterDetails = new FilterDetails();
  isFilterDisplay = false;
  CheckInData = [];
  currentPage = 1;
  siteCheckInList: SiteCheckIn[] = [];
  viewPage = FILTER_PAGE_NAME.SAFETY_SITE_CHECK_IN_LISTING;
  viewFilterSection = 'sitecheckInFilterSection';
  pageSize = AppConstants.rowsPerPage;
  fullDateFormat = AppConstants.fullDateFormat;
  filterModel: CommonFilter = new CommonFilter();
  total: number;
  user: string[];
  sortOptionList = {
    date: 'asc',
    userName: 'asc',
    Site: 'asc',
    totalDriveTime: 'asc',
    checkInTime: 'asc',
    checkOutTime: 'asc',
    siteCustomerPortfolios: 'asc',
    siteTimeZoneOffset: 'asc'
  };
  subscription: Subscription = new Subscription();
  modalRef: BsModalRef;
  roleType = ROLE_TYPE;

  constructor(
    private readonly storageService: StorageService,
    private readonly siteCheckInService: SiteCheckInService,
    private readonly route: ActivatedRoute,
    public datePipe: DatePipe,
    private readonly router: Router,
    private readonly commonService: CommonService,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService
  ) {}

  ngOnInit(): void {
    this.initFilterDetails();
    this.filterModel.direction = 'desc';
    this.filterModel.sortBy = 'date';
    this.filterModel.itemsCount = this.pageSize;
    let hasUrlFilter = false;
    this.user = this.storageService.get('user').authorities;
    this.route.queryParams.subscribe(params => {
      if (Object.keys(params).length) {
        this.filterModel.customerIds = params['customerId'] ? [Number(params['customerId'])] : [];
        this.filterModel.portfolioIds = params['portfolioId'] ? [Number(params['portfolioId'])] : [];
        this.filterModel.siteIds = params['siteId'] ? [Number(params['siteId'])] : [];
        this.filterModel.userIds = params['userId'] ? [Number(params['userId'])] : [];
        if (
          this.filterModel.portfolioIds.length ||
          this.filterModel.customerIds.length ||
          this.filterModel.siteIds.length ||
          this.filterModel.userIds.length
        ) {
          hasUrlFilter = true;
        }
      }
    });
    const filter = hasUrlFilter ? this.filterModel : this.storageService.get(this.viewPage);
    const localFilterData = this.storageService.get('userDefaultFilter');
    const defaultFilterData = this.storageService.get('user').userFilterSelection;
    const filterSection = this.storageService.get(this.viewFilterSection);
    this.isFilterDisplay = filterSection;
    if (filter) {
      this.filterModel = filter;
      const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
      if (this.filterModel.date && this.filterModel.date.start && this.filterModel.date.end) {
        model.date.start = this.datePipe.transform(this.filterModel.date.start, AppConstants.fullDateFormat);
        model.date.end = this.datePipe.transform(this.filterModel.date.end, AppConstants.fullDateFormat);
      }
      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
      if (this.filterModel.page) {
        this.currentPage = this.filterModel.page + 1;
      }
      if (this.filterModel.itemsCount) {
        this.pageSize = this.filterModel.itemsCount;
      }
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);

    if (
      this.storageService.shouldCallListApi(
        filter,
        defaultFilterData,
        localFilterData,
        this.filterModel,
        safetySiteCheckInListPageFilterKeys
      )
    ) {
      this.getAllCheckInData();
    }
  }

  getAllCheckInData(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.date && this.filterModel.date.start && this.filterModel.date.end) {
      model.date.start = this.datePipe.transform(this.filterModel.date.start, AppConstants.fullDateFormat);
      model.date.end = this.datePipe.transform(this.filterModel.date.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.date = null;
      model.date = null;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.siteCheckInService.getAllSiteCheckInListByFilter(model).subscribe({
      next: (data: SiteCheckInFilterData) => {
        this.allSiteCheckInList(data);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  allSiteCheckInList(data: SiteCheckInFilterData) {
    this.siteCheckInList = data.siteCheckiInOuts;
    this.total = data.count;
    this.loading = false;
  }

  exportData() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    this.subscription.add(
      this.siteCheckInService.getAllSiteCheckInListByFilter(filterModel).subscribe({
        next: (data: SiteCheckInFilterData) => {
          const title = 'Site Check-In';
          const rows: any = [
            [
              'UTC Date',
              'User',
              'Customer',
              'Portfolio',
              'Site',
              'Total Drive Time',
              'Check-In Time',
              'Check-out Time',
              'OffSet',
              'Reason For Visit',
              'Longitude',
              'Latitude'
            ]
          ];
          for (const i of data.siteCheckiInOuts) {
            const tempData = [
              i.date ? `="${this.datePipe.transform(i.date, this.fullDateFormat)}"` : '',
              i.userName,
              i.customer,
              i.portfolio,
              i.site,
              i.totalDriveTime,
              i.checkInTime ? i.checkInTime : '',
              i.checkOutTime ? i.checkOutTime : '',
              i.siteTimeZoneOffset,
              i.reasonForVisit,
              i.longitude,
              i.latitude
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, title);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.multi = true;
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.SITE.show = true;
    filterItem.SITE.multi = true;
    filterItem.USER.show = true;
    filterItem.DATE.show = true;
    filterItem.CHECK_IN_ONLY.show = true;
    this.filterDetails.default_sort = 'date';
    this.filterDetails.filter_item = filterItem;
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllCheckInData();
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllCheckInData(true, filterParams);
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllCheckInData();
  }

  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllCheckInData();
  }

  onDelete(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete this record?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {
          if (result) {
            this.subscription.add(
              this.siteCheckInService.deleteCheckInRecord(event).subscribe({
                next: res => {
                  if (res) {
                    if (this.currentPage !== 0 && this.siteCheckInList.length === 1) {
                      this.onChangeSize();
                    } else {
                      this.getAllCheckInData();
                      this.alertService.showSuccessToast(res.message);
                    }
                  }
                }
              })
            );
          }
        },
        err => {
          this.loading = false;
        }
      );
    }
  }

  ngOnDestroy() {
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
    this.subscription.unsubscribe();
  }
}
