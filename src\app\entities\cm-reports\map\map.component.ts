import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { debounceTime, forkJoin, Subject, Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { AllReportDropdown } from '../../../@shared/models/report.model';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter } from '../../site-device/site-device.model';
import { TicketPriorityMapping } from '../../ticket-management/ticket.model';
import { cmMapReportPageFilter<PERSON><PERSON><PERSON>, Employee<PERSON>ist, <PERSON><PERSON><PERSON>er<PERSON>ist, <PERSON>List, MapTicketDetail } from '../cm-reports.model';
import { CmReportsService } from '../cm-reports.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';
declare const google: any;

@Component({
  selector: 'sfl-map',
  templateUrl: './map.component.html',
  styleUrls: ['./map.component.scss']
})
export class MapComponent implements OnInit, OnDestroy {
  loading = false;
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  viewPage = FILTER_PAGE_NAME.CM_MAP_REPORT_LISTING;
  viewFilterSection = 'ticketsMapFilterSection';
  subscription: Subscription = new Subscription();
  ticketPriority = TicketPriorityMapping;
  filterModel: CommonFilter = new CommonFilter();
  filterList = MapFilterList;
  affectedKWModelChanged = new Subject<number>();
  ticketLocations: MapList[] = [];
  allReportDropdown = new AllReportDropdown();
  employeeList: EmployeeList[] = [];
  filterDetails: FilterDetails = new FilterDetails();
  userRoles: string;
  userRole = this.storageService.get('user').authorities;
  googleMapScriptTag = window.document.createElement('script');
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly storageService: StorageService,
    private readonly cmReportsService: CmReportsService,
    private readonly commonService: CommonService
  ) {
    this.affectedKWModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.getAllTicketList();
      if (this.filterModel.affectedkw === null || !this.filterModel.affectedkw) {
        const index = this.appliedFilter.findIndex(item => item.text === this.filterList.affectedkw);
        if (index > -1) {
          this.appliedFilter.splice(index, 1);
        }
      }
    });
  }

  ngOnInit(): void {
    this.initFilterDetails();
    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection);
    const localFilterData = this.storageService.get('userDefaultFilter');
    const defaultFilterData = this.storageService.get('user').userFilterSelection;
    this.isFilterDisplay = filterSection;
    this.userRoles = this.storageService.get('user').authorities;

    if (filter) {
      this.filterModel = filter;
      this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
      const tempArray = [this.cmReportsService.getAllTicketReport(this.filterModel)];
      const tempArrayObj = ['bindTicketList'];
      const filterArrayObj = [];
      if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, cmMapReportPageFilterKeys)) {
        this.getAllLists(tempArray, tempArrayObj, filterArrayObj);
      }
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.states = (localFilterData || defaultFilterData).states;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
      this.storageService.set(this.viewPage, this.filterModel);
      const tempArray = [this.cmReportsService.getAllTicketReport(this.filterModel)];
      const tempArrayObj = ['bindTicketList'];
      if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, cmMapReportPageFilterKeys)) {
        this.getAllLists(tempArray, tempArrayObj);
      }
    }
    // const script3 = window.document.createElement('script');
    this.googleMapScriptTag.src =
      'https://maps.googleapis.com/maps/api/js?key=AIzaSyAObJC1CaYEzUMUBnRUd8yLJlwNF78W5Uo&callback=Function.prototype';
    window.document.body.appendChild(this.googleMapScriptTag);
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.STATE.show = true;
    filterItem.MINIMUM_AFFECTED_KW.show = true;
    filterItem.PRIORITY.show = true;
    filterItem.CUSTOMER.multi = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.filter_item = filterItem;
  }

  getAllLists(apiArray: any, mapResultList: string[], filterArrayObj = []) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          if (value === 'bindTicketList') {
            this.ticketLocations = res[index];
            this.initDataForMap();
          } else {
            this[value] = res[index];
          }
        }
        setTimeout(() => {
          this.loading = false;
        }, 1000);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getAllTicketList(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    if (this.filterModel.affectedkw) {
      const filter = JSON.parse(JSON.stringify(this.filterModel));
      this.filterModel.affectedkw = parseInt(filter.affectedkw);
    }
    this.subscription.add(
      this.cmReportsService.getAllTicketReport(this.filterModel).subscribe({
        next: (res: MapList[]) => {
          this.ticketLocations = res;
          this.initDataForMap();
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  affectedkwChanged() {
    this.affectedKWModelChanged.next(null);
  }

  clearFilter() {
    this.filterModel = new CommonFilter();
    this.appliedFilter = [];
    this.getAllTicketList();
  }

  toggleFilter() {
    this.isFilterDisplay = !this.isFilterDisplay;
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
  }

  exportData() {
    this.loading = true;
    const tittle = 'Map_Report';
    const rows: any = [['Site', 'Number of Tickets', 'Total Affected kWac', 'Est. kWh Loss']];
    for (const i of this.ticketLocations) {
      const tempData = [i.site, i.numberOfTickets, i.totalAffectedKwac, i.totalAffectedHours];
      rows.push(tempData);
    }
    this.commonService.exportExcel(rows, tittle);
    this.loading = false;
  }

  changeTickets() {
    this.initDataForMap();
  }

  changeEmployees(event) {
    this.employeeList = [];
    if (event) {
      this.cmReportsService.getAllEmployeeReport().subscribe({
        next: (res: EmployeeList[]) => {
          for (const [i, v] of res.entries()) {
            if (v.zipCode) {
              const address = v.zipCode;
              const geocoder = new google.maps.Geocoder();
              geocoder.geocode({ address: address }, (results, status) => {
                if (status === google.maps.GeocoderStatus.OK) {
                  v.lat = results[0].geometry.location.lat();
                  v.lng = results[0].geometry.location.lng();
                  this.employeeList.push(v);
                  // this.initDataForMap();
                }
              });
            }
          }
        }
      });
    } else {
      this.initDataForMap();
    }
  }

  initDataForMap() {
    const tempData: MapList[] = [];
    if (this.filterModel.showTickets) {
      for (const i of this.ticketLocations) {
        i.isSite = true;
        tempData.push(i);
      }
    }
    this.initMap(tempData);
  }

  initMap(data: MapList[]): void {
    const map = new google.maps.Map(document.getElementById('map') as HTMLElement, {
      zoom: 3,
      center: { lat: 41.85, lng: -87.65 }
    });

    // Add some markers to the map.
    // Note: The code uses the JavaScript Array.prototype.map() method to
    // create an array of markers based on a given "locations" array.
    // The map() method here has nothing to do with the Google Maps API.
    const markers = data.map((location, i) => {
      const marker = new google.maps.Marker({
        position: location,
        map,
        title: i.toString()
      });

      const icon = location.isEmployee
        ? 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png'
        : 'http://maps.google.com/mapfiles/ms/icons/red-dot.png';

      marker.setIcon(icon);

      google.maps.event.addListener(marker, 'click', (cluster, event) => {
        this.getContent(location).then((val: string) => {
          const infowindow = new google.maps.InfoWindow({
            content: val
          });
          infowindow.open(map, marker);
        });
      });

      return marker;
    });

    // Add a marker clusterer to manage the markers.
    // @ts-ignore MarkerClusterer defined via script
    new MarkerClusterer(map, markers, {
      imagePath: 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'
    });
  }

  getContent(location: MapList): Promise<string> {
    const promise = new Promise<string>((resolve, reject) => {
      this.cmReportsService.getAllTicketDetailById(location.siteId).subscribe({
        next: (res: MapTicketDetail[]) => {
          let displayVal = '';
          for (const i of res) {
            displayVal += `<div><strong>Site Name : </strong> ${i.site}</div>
          <div><strong>Portfolio : </strong> ${i.portfolio}</div>
          <div><strong>Ticket :</strong> ${i.ticketNumber}</div>
          <div><strong>Device :</strong> ${i.device}</div>
          <div><strong>Affected KW :</strong> ${i.affectedKw}</div>
          <div><strong>Issue :</strong> ${i.issue}</div>
          <br>`;
          }
          resolve(displayVal);
        }
      });
    });
    return promise;
  }

  refreshList(filterParams: CommonFilter) {
    this.getAllTicketList(true, filterParams);
  }

  ngOnDestroy(): void {
    // Remove the script element from the DOM when the component is destroyed
    if (this.googleMapScriptTag && this.googleMapScriptTag.parentNode) {
      this.googleMapScriptTag.parentNode.removeChild(this.googleMapScriptTag);
    }
  }
}
