import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { Customer, CustomerFilterData, CustomerFilterList } from '../../../@shared/models/customer.model';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter } from '../../site-device/site-device.model';
import { CustomerSiteInfoArchiveComponent } from '../customer-add-edit/customer-site-info-archive/customer-site-info-archive.component';
import { CustomerService } from '../customer.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-customer-listing',
  templateUrl: './customer-listing.component.html',
  styleUrls: ['./customer-listing.component.scss']
})
export class CustomerListingComponent implements OnInit, OnDestroy {
  customers: Customer[];
  subscription: Subscription = new Subscription();
  loading = false;
  isDatahas: boolean;
  user: string;
  currentPage = 1;
  total: number;
  pageSize = AppConstants.rowsPerPage;
  modalRef: BsModalRef;
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  filterModel: CommonFilter = new CommonFilter();
  viewPage = FILTER_PAGE_NAME.SITE_INFO_CUSTOMER_LISTING;
  filterList = CustomerFilterList;
  viewFilterSection = 'customerFilterSection';
  sortOptionList = {
    CustomerName: 'asc'
  };
  filterDetails: FilterDetails = new FilterDetails();
  isArchivedModal = true;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly customerService: CustomerService,
    private readonly router: Router,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService
  ) {}

  ngOnInit() {
    this.initFilterDetails();
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = 'CustomerName';
    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection);
    this.user = this.storageService.get('user').authorities;
    this.isFilterDisplay = filterSection;
    if (filter) {
      this.filterModel = filter;
      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
      if (this.filterModel.page) {
        this.currentPage = this.filterModel.page + 1;
      }
      if (this.filterModel.itemsCount) {
        this.pageSize = this.filterModel.itemsCount;
      }
    }

    const pageFilterKeys = ['search', 'isActive', 'isArchive'];
    if (this.storageService.shouldCallListApi(filter, {}, {}, filter, pageFilterKeys)) {
      this.getAllCustomerList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.SHOW_STATUS.show = true;
    filterItem.SHOW_ARCHIVED.show = true;
    filterItem.SEARCH_BOX.show = true;

    this.filterDetails.default_sort = 'CustomerName';
    this.filterDetails.filter_item = filterItem;
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllCustomerList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllCustomerList();
  }

  // Bind Customer
  getAllCustomerList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.customerService.getAllCustomersByfilter(this.filterModel).subscribe({
        next: (data: CustomerFilterData) => {
          this.customerList(data);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  customerList(data: CustomerFilterData) {
    this.customers = data.customers;
    this.total = data.totalCustomer;
    this.loading = false;
  }

  // Pagesize Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllCustomerList();
  }

  // Go to site
  gotoSite(customerId, totalSites, isArchive) {
    if (totalSites > 0 && !isArchive) {
      this.router.navigate(['/entities/sites/'], { queryParams: { customerId, isArchive, isSiteCount: true } });
    }
  }

  exportData() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    this.subscription.add(
      this.customerService.getAllCustomersByfilter(filterModel).subscribe({
        next: (data: CustomerFilterData) => {
          const tittle = 'Customers';
          const rows: any = [['Customer', 'Number of Sites', 'kWdc']];
          for (const i of data.customers) {
            const tempData = [i.customerName, i.totalSites ? i.totalSites : 0, i.sumDCSize];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllCustomerList(true, filterParams);
  }

  archiveToggleChange(event, customer) {
    this.isArchivedModal = false;
    if (event === false) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-full-dialog',
        initialState: {
          customerId: customer.id,
          archiveModalFrom: 'customer',
          isFromListOrView: true
        }
      };
      this.modalRef = this.modalService.show(CustomerSiteInfoArchiveComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.getAllCustomerList();
        } else {
          const index = this.customers.findIndex(item => item.id === customer.id);
          this.customers[index].isArchive = true;
        }
        this.isArchivedModal = true;
      });
    }
  }

  ngOnDestroy() {
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
    this.subscription.unsubscribe();
  }
}
