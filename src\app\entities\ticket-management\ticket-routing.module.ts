import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModeWiseGuard } from '../../@shared/services/mode-wise.guard';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { TicketDetailComponent } from './ticket-detail/ticket-detail.component';
import { TicketListingComponent } from './ticket-listing/ticket-listing.component';
import { TicketManagementComponent } from './ticket-management.component';
import { TruckRollGalleryComponent } from './truck-roll-gallery/truck-roll-gallery.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: TicketManagementComponent,
    children: [
      {
        path: '',
        redirectTo: 'list',
        data: { pageTitle: 'CM All Tickets' }
      },
      {
        path: 'list',
        component: TicketListingComponent,
        data: { pageTitle: 'CM All Tickets' }
      },
      {
        path: 'add',
        component: TicketDetailComponent,
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
          pageTitle: 'Add Ticket'
        }
      },
      {
        path: 'truckroll-gallery/:truckRollNo',
        component: TruckRollGalleryComponent,
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: [
            ROLE_TYPE.ADMIN,
            ROLE_TYPE.PORTFOLIOMANAGER,
            ROLE_TYPE.MANAGER,
            ROLE_TYPE.FIELDTECH,
            ROLE_TYPE.ANALYST,
            ROLE_TYPE.CUSTOMER
          ],
          pageTitle: 'Truck Roll Gallery'
        }
      },
      {
        path: 'detail/:mode/:ticketNumber',
        component: TicketDetailComponent,
        canActivate: [ModeWiseGuard],
        data: {
          edit: [
            ROLE_TYPE.ADMIN,
            ROLE_TYPE.PORTFOLIOMANAGER,
            ROLE_TYPE.MANAGER,
            ROLE_TYPE.FIELDTECH,
            ROLE_TYPE.ANALYST,
            ROLE_TYPE.CUSTOMER
          ],
          detail: [
            ROLE_TYPE.ADMIN,
            ROLE_TYPE.PORTFOLIOMANAGER,
            ROLE_TYPE.MANAGER,
            ROLE_TYPE.FIELDTECH,
            ROLE_TYPE.ANALYST,
            ROLE_TYPE.CUSTOMER
          ],
          view: [
            ROLE_TYPE.ADMIN,
            ROLE_TYPE.PORTFOLIOMANAGER,
            ROLE_TYPE.MANAGER,
            ROLE_TYPE.FIELDTECH,
            ROLE_TYPE.ANALYST,
            ROLE_TYPE.CUSTOMER
          ],
          pageTitle: 'CM Ticket Details'
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TicketRoutingModule {}
