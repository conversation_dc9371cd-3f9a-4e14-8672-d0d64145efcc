import { Location } from '@angular/common';
import { Component, OnDestroy, OnInit, TemplateRef, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription, debounceTime, forkJoin } from 'rxjs';
import { CommonDropboxFileUploadComponent } from '../../../@shared/components/common-dropbox-file-upload/common-dropbox-file-upload.component';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { TagListResponseModel } from '../../../@shared/components/image-dropbox-gallery/drop-box.model';
import { APP_ROUTES, AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { Month, Portfolio, PortfolioCustomerContact, PortfolioOutageResponse } from '../../../@shared/models/portfolio';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { DropboxImageGalleryService } from '../../../@shared/services/dropbox-image-gallery.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerSiteInfoArchiveComponent } from '../../customer-management/customer-add-edit/customer-site-info-archive/customer-site-info-archive.component';
import { CustomerService } from '../../customer-management/customer.service';
import { DataSharingService } from '../../customer-management/datasharing.service';
import { ProfileService } from '../../profile/profile.service';
import { AttachmentListResponse, FileListPaginationParams } from '../../ticket-management/ticket.model';
import { UserService } from '../../user-management/user.service';
import { PortfolioService } from '../portfolio.service';
import { NotesEntityName, NotesEntityType } from '../../notes-management/notes-management.model';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'qesolar-portfolio-add-edit',
  templateUrl: './portfolio-add-edit.component.html',
  styleUrls: ['./portfolio-add-edit.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PortfolioAddEditComponent implements OnInit, OnDestroy {
  portfolio: Portfolio = new Portfolio();
  portfolioOutage: PortfolioOutageResponse = new PortfolioOutageResponse();
  oldPortfolioOutageSetting: PortfolioOutageResponse = new PortfolioOutageResponse();
  isOutageAlert = false;
  isEdit = false;
  isDetail = false;
  isCreate = false;
  readonly ROUTES = APP_ROUTES;
  subscription: Subscription = new Subscription();
  loading = false;
  customers: Dropdown[];
  disabled = false;
  userEnable = true;
  qeUsersList: Dropdown[];
  qeCommercialAssetsManagerList: Dropdown[];
  contactNoFormat = AppConstants.phoneNumberMask;
  modalRef: BsModalRef;
  userRoles: string;
  checked = true;
  updatedData: any[] = [];
  currentIsActiveStatusInBE = false;
  filteredPortfolioManagerIds: number[] = [];
  filteredAnalystUserIds: number[] = [];
  filteredCommercialAssetsManagerIds: number[] = [];
  attachmentsLoading = false;
  createFileUploadList = [];
  fileAttachments: AttachmentListResponse = new AttachmentListResponse();
  filesPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  isSelectedAllFiles = false;
  filesTagList: TagListResponseModel[] = [];
  filteredAppliedTags = [];
  allSelectedFiles = [];
  fileTagIds = [];
  selectedFilesNamesString: string;
  fileSearchText = '';
  fileSearchModelChanged = new Subject<string>();
  addRemoveFilesTagsModalRef: BsModalRef;
  sortOptionList = {
    fileName: 'asc',
    createdDate: 'asc'
  };
  filterModel: { page: number; items: number; sortBy: string; direction: string } = { page: 0, items: 10, direction: '', sortBy: '' };
  fullDateFormat = AppConstants.fullDateFormat;
  portfolioNotesLoading = false;
  entityTypeName = NotesEntityName[NotesEntityType.PORTFOLIO];
  entityTypeId = NotesEntityType.PORTFOLIO;
  id: number = 0;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly portfolioService: PortfolioService,
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly route: ActivatedRoute,
    private readonly customerService: CustomerService,
    private readonly userService: UserService,
    private readonly modalService: BsModalService,
    private readonly _location: Location,
    private readonly profileService: ProfileService,
    private readonly storageService: StorageService,
    private readonly dataSharingService: DataSharingService,
    private readonly commonService: CommonService,
    private readonly dropBoxService: DropboxImageGalleryService
  ) {
    this.dataSharingService.updatedData$.subscribe(updatedData => {
      if (updatedData && updatedData.length) {
        this.updatedData = updatedData;
        this.portfolio.isArchive = updatedData[0].isArchive;
        this.portfolio.isActive = updatedData[0].isActive;
      }
    });
  }

  ngOnInit() {
    this.userRoles = this.storageService.get('user').authorities;
    this.route.params.subscribe(params => {
      if (params?.id) {
        this.id = Number(params.id);
        this.isOutageAlert = true;
        this.getPortfolio(params.id);
        if (params.mode == 'edit') {
          this.isEdit = true;
        } else {
          this.isDetail = true;
        }
      } else {
        this.isCreate = true;
        this.isDetail = false;
        this.isEdit = false;
      }
      this.getCustomerAccess();
      this.getPortfolioManagerList();
    });
    this.commonService.commonUploadFinish$.subscribe(res => {
      if (res) {
        this.getFilesAttachmentsList();
      }
    });

    this.fileSearchModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.getFilesAttachmentsList();
    });
  }

  customerdata: any;
  // get All Portfolio Access
  getCustomerAccess() {
    this.loading = true;
    this.subscription.add(
      this.customerService.getAllCustomer().subscribe({
        next: res => {
          setTimeout(() => {
            if (!this.isEdit) {
              const cusList = res.filter(p => p.isActive);
              this.customerdata = cusList;
            } else {
              this.customerdata = res;
            }
          }, 0);
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  onCustomerSelect(customerId: number) {
    if (this.isCreate && customerId) {
      this.isOutageAlert = true;
      const getOutageParams = {
        customerId: customerId,
        portfolioId: 0,
        siteId: null,
        settingType: 2
      };
      this.getPortfolioOutage(getOutageParams, false);
    }
  }

  getPortfolioOutage(getOutageParams, isParentSetting) {
    this.loading = true;
    this.subscription.add(
      this.customerService.getAlertOutageDetail(getOutageParams).subscribe({
        next: res => {
          if (isParentSetting) {
            this.portfolioOutage = {
              ...res,
              isParentSetting: true,
              customerId: this.oldPortfolioOutageSetting.customerId,
              outageId: this.oldPortfolioOutageSetting.outageId,
              parentSettingId: this.oldPortfolioOutageSetting.parentSettingId,
              portfolioId: this.oldPortfolioOutageSetting.portfolioId,
              siteId: this.oldPortfolioOutageSetting.siteId,
              settingType: this.oldPortfolioOutageSetting.settingType,
              zeroGeneration: this.oldPortfolioOutageSetting.zeroGeneration,
              portfoliosSites: this.oldPortfolioOutageSetting.portfoliosSites,
              sites: this.oldPortfolioOutageSetting.sites
            };
          } else {
            this.portfolioOutage = res;
            this.oldPortfolioOutageSetting = res;
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  useParentSetting(isParentSetting) {
    if (isParentSetting) {
      const getOutageParams = {
        customerId: this.portfolio.customerId,
        portfolioId: null,
        siteId: null,
        settingType: 1
      };
      this.getPortfolioOutage(getOutageParams, true);
      this.portfolioOutage.isParentSetting = true;
    }
  }

  getMonthName(month: number): string {
    switch (month) {
      case 1:
        return Month.JANUARY;
      case 2:
        return Month.FEBRUARY;
      case 3:
        return Month.MARCH;
      case 4:
        return Month.APRIL;
      case 5:
        return Month.MAY;
      case 6:
        return Month.JUNE;
      case 7:
        return Month.JULY;
      case 8:
        return Month.AUGUST;
      case 9:
        return Month.SEPTEMBER;
      case 10:
        return Month.OCTOBER;
      case 11:
        return Month.NOVEMBER;
      case 12:
        return Month.DECEMBER;
    }
  }

  formatTime(time: string, index: number, fieldToUpdate: string) {
    const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (timePattern.test(time)) {
      this.portfolioOutage.timeSetting[index][fieldToUpdate] = time;
    } else if (time.charAt(2) === ':') {
      this.portfolioOutage.timeSetting[index][fieldToUpdate] = time;
    } else if (time.length === 4) {
      const hours = time.substring(0, 2);
      const minutes = time.substring(2, 4);
      const formattedTime = `${hours}:${minutes}`;
      this.portfolioOutage.timeSetting[index][fieldToUpdate] = formattedTime;
    }
  }

  getPortfolioManagerList() {
    this.loading = true;
    this.subscription.add(
      this.userService.getAllQEUsers().subscribe({
        next: res => {
          this.qeUsersList = res;
          this.qeCommercialAssetsManagerList = res.filter(item => item.userRoleId === 7);
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  selectAndDeselectAll(isSelect, userType: string, filteredListType: string, fromListType: string) {
    if (isSelect) {
      if (this[filteredListType].length) {
        this.portfolio[userType] = [...new Set([...this.portfolio[userType], ...JSON.parse(JSON.stringify(this[filteredListType]))])];
      } else {
        this.portfolio[userType] = this[fromListType].map(element => element.id);
      }
    } else {
      if (this[filteredListType].length) {
        this.portfolio[userType] = this.portfolio[userType].filter(x => !this[filteredListType].includes(x));
      } else {
        this.portfolio[userType] = [];
      }
    }
  }

  getPortfolio(id) {
    this.subscription.add(
      this.portfolioService.getById(id).subscribe({
        next: (res: Portfolio) => {
          this.portfolio = res;
          this.portfolioOutage = res.outageSetting;
          this.oldPortfolioOutageSetting = res.outageSetting;
          this.currentIsActiveStatusInBE = res.isActive;
          const getOutageParams = {
            customerId: this.portfolio.customerId,
            portfolioId: this.portfolio.id,
            siteId: null,
            settingType: 2
          };
          this.getPortfolioOutage(getOutageParams, false);
          this.getFilesAttachmentsList();
        }
      })
    );
  }

  uploadFiles(id, msg?: string) {
    const tempArray = [];
    if (this.createFileUploadList.length) {
      for (const fileObj of this.createFileUploadList) {
        const formData: FormData = new FormData();
        formData.append('files', fileObj.file as File);
        if (fileObj.fileTag.length) {
          for (const tag of fileObj.fileTag) {
            formData.append('fileTagIds', `${tag}`);
          }
        }
        formData.append('customerId', `${this.portfolio.customerId}`);
        formData.append('id', '0');
        formData.append('portfolioId', `${id}`);
        formData.append('siteId', null);
        formData.append('entityId', `${id}`);
        formData.append('entityNumber', '');
        formData.append('moduleType', '10');
        formData.append('fileType', `${fileObj.fileType}`);
        if (fileObj.notes) {
          formData.append('notes', `${fileObj.notes}`);
        }
        tempArray.push(this.dropBoxService.uploadFilesToGallery(formData));
      }
    }
    this.subscription.add(
      forkJoin(tempArray).subscribe({
        next: res => {
          this.createFileUploadList = [];
          this.router.navigateByUrl(APP_ROUTES.PORTFOLIOS);
          this.alertService.showSuccessToast(msg);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  createPortfolio() {
    this.userEnable = true;
    this.portfolio.phone = Number(this.portfolio.phone);
    this.portfolio.outageSetting = this.portfolioOutage;
    this.portfolio.archiveUpdatedData = this.updatedData;
    if (this.portfolio.customerId) {
      if (!this.portfolio.id) {
        this.loading = true;
        this.portfolio.noOfSite = 0;
        this.portfolio.id = 0;
        this.portfolio.isActive = true;
        this.portfolio.isArchive = false;

        this.subscription.add(
          this.portfolioService.createPortfolio(this.portfolio).subscribe({
            next: res => {
              if (this.createFileUploadList.length) {
                this.uploadFiles(res.entryid, res.message);
              } else {
                this.router.navigateByUrl(APP_ROUTES.PORTFOLIOS);
                this.alertService.showSuccessToast(res.message);
              }
              this.loading = false;
            },
            error: e => {
              this.loading = false;
            }
          })
        );
      } else {
        this.portfolio.noOfSite = 0;
        if (this.portfolio.isArchive) {
          const ngModalOptions: ModalOptions = {
            backdrop: 'static',
            keyboard: false,
            animated: true,
            initialState: {
              message: `${this.portfolio.name} will be archived along with all ${this.portfolio.name}'s sites and devices. Are you sure you want to save?`
            }
          };
          this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
          this.modalRef.content.onClose.subscribe(result => {
            if (result) {
              this.updatePortfolioAfterArchiveConfirmation();
            }
          });
        } else {
          this.updatePortfolioAfterArchiveConfirmation();
        }
      }
    } else {
      this.alertService.showErrorToast('Please select customer');
    }
  }

  updatePortfolioAfterArchiveConfirmation() {
    this.loading = true;
    this.subscription.add(
      this.portfolioService.updatePortfolio(this.portfolio).subscribe({
        next: res => {
          // this.preserveOutageDetails(res);
          this.updatedData = [];
          this.dataSharingService.updateData(this.updatedData);
          this.dataSharingService.clearCachedPortSites();
          this.loading = false;
          this.router.navigateByUrl(APP_ROUTES.PORTFOLIOS);
          if (res.message === 'Portfolio already exist with same portfolio name') {
            this.alertService.showErrorToast(res.message);
          } else {
            this.alertService.showSuccessToast(res.message);
          }
          this.userEnable = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  activeToggleChange(event) {
    if (event === true && !this.currentIsActiveStatusInBE) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-full-dialog',

        initialState: {
          portfolioId: this.portfolio.id,
          archiveModalFrom: 'portfolio'
        }
      };
      this.modalRef = this.modalService.show(CustomerSiteInfoArchiveComponent, ngModalOptions);
    }
  }

  getComponentTitle() {
    let result = 'Add Portfolio';
    if (!this.portfolio.id) {
      return result;
    }
    if (this.isDetail) {
      result = 'Portfolio Details';
      return result;
    } else {
      result = `Edit Portfolio`;
      return result;
    }
  }

  addContact() {
    const newContact = new PortfolioCustomerContact();
    this.portfolio.customerEmails.push(newContact);
  }

  deleteContact(id: number, index: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (id) {
          this.subscription.add(
            this.portfolioService.deletePortfolioContact(id).subscribe({
              next: res => {
                this.portfolio.customerEmails = res;
                this.alertService.showSuccessToast('Contact deleted successfully.');
                this.loading = false;
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.portfolio.customerEmails.splice(index, 1);
        }
      }
    });
  }

  trackByIndex(index: number, obj: any): any {
    return index;
  }

  goBack() {
    this._location.back();
  }

  onDelete(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete this portfolio?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.loading = true;
          this.subscription.add(
            this.portfolioService.deletePortfolio(event).subscribe({
              next: res => {
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.profileService.setDefaultFilterSelection();
                  this.loading = false;
                  this.goBack();
                }
              },
              error: () => (this.loading = false)
            })
          );
        }
      });
    }
  }

  onDropdownSearchFilter(event: any, filteredListType: string) {
    if (event.term) {
      this[filteredListType] = event.items?.map(element => element.id);
    } else {
      this[filteredListType] = [];
    }
  }

  //Below code is for Site Files dropbox 2890 jira ticket

  getFilesAttachmentsList(requestParams = null) {
    this.attachmentsLoading = true;
    const getListingParams = {
      siteId: null,
      customerId: this.portfolio.customerId,
      portfolioId: this.portfolio.id,
      entityId: this.portfolio.id,
      entityNumber: '',
      parentId: null,
      fileType: 'document',
      imagePreviewId: 0,
      isCustomerFacing: false,
      moduleType: 10,
      page: this.filterModel.page,
      sortBy: this.filterModel.sortBy,
      direction: this.filterModel.direction,
      itemsCount: this.filterModel.items,
      search: this.fileSearchText
    };
    this.subscription.add(
      this.dropBoxService.getGalleryImageFiles(getListingParams).subscribe({
        next: (res: AttachmentListResponse) => {
          let selectedForPreviewCount = 0;
          this.isSelectedAllFiles = false;
          console.log(this.allSelectedFiles, 'this.allSelectedFiles');
          const updatedFileGallery = res.fileGallery.map(file => {
            const isSelected = this.allSelectedFiles.some(selectedFile => selectedFile.id === file.id);
            if (isSelected) {
              selectedForPreviewCount++;
            }
            return {
              ...file,
              isSelectedForPreview: isSelected
            };
          });

          this.fileAttachments = {
            totalCount: res.totalCount,
            fileGallery: [...updatedFileGallery]
          };
          if (checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
            this.fileAttachments.fileGallery.forEach(file => {
              file.fileTagTxt = file.fileTagTxt.filter(tag => tag !== 'Customer Facing');
            });
          }
          this.isSelectedAllFiles = this.fileAttachments.fileGallery.length === selectedForPreviewCount;
          // this.cdr.detectChanges();
          this.attachmentsLoading = false;
        },
        error: err => {
          this.attachmentsLoading = false;
        }
      })
    );
  }

  openFileUploadSidePanel(mode, fileItem) {
    const entityDetails = {
      customerId: this.portfolio.customerId,
      portfolioId: this.portfolio.id,
      siteId: null,
      entityId: this.portfolio.id,
      entityNumber: '',
      moduleType: 10
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        isFileEditMode: mode,
        fileItemObj: fileItem,
        entityDetails: entityDetails,
        parentModuleName: 'Site',
        isParentCreateMode: this.isCreate ? true : false
      }
    };
    this.modalRef = this.modalService.show(CommonDropboxFileUploadComponent, ngModalOptions);
    if (this.isCreate) {
      this.modalRef.content.fileUploadList.subscribe(res => {
        if (res && res.length) {
          this.createFileUploadList.push(...res);
        }
      });
    } else {
      this.modalRef.content.isParentRefresh.subscribe(res => {
        if (res) {
          this.getFilesAttachmentsList();
        }
      });
    }
  }

  downloadDropBoxFile(fileId, fileName) {
    this.loading = true;
    this.dropBoxService.downloadPreviewedImage(fileId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, data.type);
          link.download = fileName;
          link.click();
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  deleteDropBoxFile(fileId, isCreateMode = false) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (isCreateMode) {
          this.createFileUploadList = this.createFileUploadList.filter(item => item.id !== fileId);
        } else {
          this.subscription.add(
            this.dropBoxService.deleteImageGalleryFiles(fileId).subscribe({
              next: data => {
                this.alertService.showSuccessToast(`file deleted Successfully.`);
                this.getFilesAttachmentsList();
                this.loading = false;
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      }
    });
  }

  onPageChange(obj) {
    this.filesPaginationParams.currentPage = obj;
    this.filterModel.items = this.filesPaginationParams.itemsCount;
    this.filterModel.page = obj - 1;
    this.getFilesAttachmentsList();
  }

  onChangeSize() {
    this.filesPaginationParams.itemsCount = Number(this.filesPaginationParams.pageSize);
    this.filterModel.page = this.filesPaginationParams.currentPage = 0;
    this.filterModel.items = this.filesPaginationParams.itemsCount;
    this.getFilesAttachmentsList();
  }

  selectAllFiles() {
    if (this.isSelectedAllFiles) {
      // If selecting all files
      for (const element of this.fileAttachments.fileGallery) {
        element.isSelectedForPreview = true; // Mark as selected
        if (!this.allSelectedFiles.some(file => file.id === element.id)) {
          this.allSelectedFiles.push(element);
        }
      }
    } else {
      // If deselecting all files
      for (const element of this.fileAttachments.fileGallery) {
        element.isSelectedForPreview = false; // Mark as deselected
        // Remove from allSelectedFiles if it exists
        const index = this.allSelectedFiles.findIndex(file => file.id === element.id);
        if (index !== -1) {
          this.allSelectedFiles.splice(index, 1);
        }
      }
    }
  }

  singleFilesCheckChanged(files) {
    if (files.isSelectedForPreview) {
      // Add the file only if it's not already in allSelectedFiles
      if (!this.allSelectedFiles.includes(files)) {
        this.allSelectedFiles.push(files);
      }
    } else {
      // If the file is not selected for preview, remove it from allSelectedFiles
      const index = this.allSelectedFiles.findIndex(file => file.id === files.id);
      if (index !== -1) {
        this.allSelectedFiles.splice(index, 1);
      }
    }

    this.isSelectedAllFiles = this.fileAttachments.fileGallery.every(file => this.allSelectedFiles.includes(file));
  }

  fileSearchChanged() {
    this.fileSearchModelChanged.next(null);
  }

  getFilesTagList() {
    this.loading = true;
    this.dropBoxService.getFileTagList().subscribe({
      next: data => {
        if (data) {
          this.filesTagList = data;
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  openEditTagsModal(template: TemplateRef<any>) {
    if (this.allSelectedFiles) {
      this.getFilesTagList();
      this.selectedFilesNamesString = this.allSelectedFiles.map(item => item.fileName).join(', ');
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-md'
      };
      setTimeout(() => {
        this.addRemoveFilesTagsModalRef = this.modalService.show(template, ngModalOptions);
      }, 0);
    } else {
      this.alertService.showWarningToast('Please select at least one file.');
    }
  }

  addRemoveMultipleFilesTags(isApplyTags: boolean) {
    const filesIds: number[] = this.allSelectedFiles.filter(item => item.isSelectedForPreview === true).map(item => item.id);
    const multipleImagesTags = {
      filesIds: filesIds,
      fileTagIds: this.fileTagIds,
      isApplyTags: isApplyTags
    };
    if (filesIds && filesIds.length && this.fileTagIds && this.fileTagIds.length) {
      this.addRemoveTags(multipleImagesTags);
    } else {
      this.alertService.showWarningToast(`Please select at least one file tag.`);
    }
  }

  addRemoveTags(filesTagsParams) {
    this.loading = true;
    this.subscription.add(
      this.dropBoxService.applyTagsToFiles(filesTagsParams).subscribe({
        next: res => {
          this.addRemoveFilesTagsModalRef.hide();
          this.loading = false;
          this.allSelectedFiles = [];
          this.fileTagIds = [];
          this.isSelectedAllFiles = false;
          this.filesPaginationParams.currentPage = 1;
          this.alertService.showSuccessToast(`Tags updated successfully.`);
          this.getFilesAttachmentsList();
        },
        error: err => {
          this.loading = false;
          this.isSelectedAllFiles = false;
          this.allSelectedFiles = [];
        }
      })
    );
  }

  onFilter(event: any) {
    if (event.term) {
      this.filteredAppliedTags = event.items?.map(element => element.id);
    } else {
      this.filteredAppliedTags = [];
    }
  }

  toggleSelectUnselectAllTags(isSelect = false) {
    if (isSelect) {
      if (!this.filteredAppliedTags.length) {
        this.fileTagIds = this.filesTagList.map(site => site.id);
      } else {
        if (!Array.isArray(this.fileTagIds)) {
          this.fileTagIds = [];
        }
        this.fileTagIds = [...new Set([...this.fileTagIds, ...JSON.parse(JSON.stringify(this.filteredAppliedTags))])];
      }
    } else {
      if (this.filteredAppliedTags.length) {
        this.fileTagIds = this.fileTagIds.filter(x => !this.filteredAppliedTags.includes(x));
      } else {
        this.fileTagIds = [];
      }
    }
  }

  reorderTags() {
    const selectedTags = this.filesTagList.filter(tag => this.fileTagIds.includes(tag.id));
    const unselectedTags = this.filesTagList.filter(tag => !this.fileTagIds.includes(tag.id));
    this.filesTagList = [...selectedTags, ...unselectedTags];
  }

  sortFiles(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.filterModel.items = this.filesPaginationParams.pageSize;
    this.filterModel.page = this.filesPaginationParams.currentPage - 1;
    const params = {
      sortBy: this.filterModel.sortBy,
      direction: this.filterModel.direction,
      itemsCount: this.filterModel.items,
      page: this.filterModel.page
    };
    this.getFilesAttachmentsList(params);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
