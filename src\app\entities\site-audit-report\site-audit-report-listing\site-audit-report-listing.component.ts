import { Date<PERSON>ip<PERSON> } from '@angular/common';
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { forkJoin, Subscription } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import {
  AllReportDropdown,
  ReportType,
  SA_REPORT_STATUS_ID_ENUMS,
  SA_REPORT_STATUS_LABELS,
  SiteAuditCPSListDataClass,
  SiteAuditReportModel,
  SiteAuditReportModelDTO,
  SITEAUDITREPORTSFILTERLIST,
  StartNewSiteAuditReportModel
} from '../../../@shared/models/report.model';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerService } from '../../customer-management/customer.service';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { ReportService } from '../../report/report.service';
import { ShareModelComponent } from '../../report/share-model/share-model.component';
import { ViewReportDetailComponent } from '../../reports/view-report-detail/view-report-detail.component';
import { AppliedFilter } from '../../site-device/site-device.model';
import { SiteService } from '../../site-management/site.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-site-audit-report-listing',
  templateUrl: './site-audit-report-listing.component.html',
  styleUrls: ['./site-audit-report-listing.component.scss']
})
export class SiteAuditReportListingComponent implements OnInit, OnDestroy {
  modalRef: BsModalRef;
  reportsData: SiteAuditReportModel[] = [];
  siteAuditNewReportModel = new StartNewSiteAuditReportModel({});
  total: number;
  currentPage = 1;
  isDatahas: boolean;
  pageSize = AppConstants.rowsPerPage;
  subscription: Subscription = new Subscription();
  loading = false;
  newSAReportLoading = false;
  disabled = false;
  reportTypeList: ReportType[];
  allReportDropdown = new AllReportDropdown();
  temp: number[] = [];
  viewdeletetedbutton = false;
  viewArchUnarchbutton = false;
  user: string;
  viewPage = FILTER_PAGE_NAME.PM_SITE_AUDIT_LISTING;
  viewFilterSection = 'siteAuditReportFilterSection';
  fullDateFormat = AppConstants.fullDateFormat;
  filter = {
    startYear: []
  };
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  filterList = SITEAUDITREPORTSFILTERLIST;
  sortOptionList = {
    SiteName: 'asc',
    Year: 'desc',
    CustomerPortfolio: 'asc'
  };
  filterDetails: FilterDetails = new FilterDetails();
  siteLayoutImg: File[] = [];
  userRoles: string[];
  clonedSiteAuditCPSListData = new SiteAuditCPSListDataClass();
  siteAuditCPSData = new SiteAuditCPSListDataClass();
  isCusPortSiteLoading = false;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly commonService: CommonService,
    private readonly reportService: ReportService,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly datePipe: DatePipe,
    private readonly router: Router,
    private readonly customerService: CustomerService,
    private readonly siteService: SiteService,
    private readonly portfolioService: PortfolioService
  ) {}

  ngOnInit() {
    this.userRoles = this.storageService.get('user').authorities;
    const filter = this.storageService.get(this.viewPage),
      filterSection = this.storageService.get(this.viewFilterSection),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection;

    if (filter) {
      this.filterModel = filter;
      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
      if (this.filterModel.page) {
        this.currentPage = this.filterModel.page + 1;
      }
      if (this.filterModel.itemsCount) {
        this.pageSize = this.filterModel.itemsCount;
      }
      if (this.filterModel.isDelete) {
        this.viewdeletetedbutton = this.filterModel.isDelete;
      }
      if (this.filterModel.isArchive) {
        this.viewArchUnarchbutton = this.filterModel.isArchive;
      }
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;

      this.storageService.set(this.viewPage, this.filterModel);
    }

    if (!this.filterModel.years.length) {
      this.filterModel.years = [this.commonService.getCurrentYear()];
    }

    this.initFilterDetails();
    this.filterModel.direction = 'desc';
    this.filterModel.sortBy = 'Year';
    this.user = this.storageService.get('user').authorities;
    this.filterModel.itemsCount = this.pageSize;
    this.getAllSiteAuditReportsList();
    this.isFilterDisplay = filterSection;
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.SITE_AUDIT_REPORT_CUSTOMER.show = true;
    filterItem.SITE_AUDIT_REPORT_PORTFOLIO.show = true;
    filterItem.SITE_AUDIT_REPORT_SITE.show = true;
    filterItem.SITE_AUDIT_REPORT_PORTFOLIO.multi = true;
    filterItem.SITE_AUDIT_REPORT_CUSTOMER.multi = true;
    filterItem.SITE_AUDIT_REPORT_SITE.multi = true;
    filterItem.START_YEAR.show = true;
    filterItem.START_YEAR.multi = true;
    filterItem.SITE_AUDIT_REPORT_STATUS.show = true;
    this.filterDetails.default_sort = 'Year';
    this.filterDetails.filter_item = filterItem;
  }

  getAllSiteAuditCusPortSiteInit(): void {
    const tempArray = [
      this.customerService.getSiteAuditJHACustomersAll(),
      this.portfolioService.getAllSiteAuditJHAPortfoliosbycustomerId(),
      this.siteService.getAllSiteAuditJHASitesByPortfolioId()
    ];
    const tempArrayObj = ['customerList', 'portfolioList', 'siteList'];
    this.clonedSiteAuditCPSListData = this.siteAuditCPSData = new SiteAuditCPSListDataClass();
    this.getAllSiteAuditCusPortSite(tempArray, tempArrayObj);
  }

  getAllSiteAuditCusPortSite(apiArray, mapResultList): void {
    this.isCusPortSiteLoading = true;
    if (apiArray.length) {
      forkJoin(apiArray).subscribe({
        next: (res: any) => {
          for (const [index, value] of mapResultList.entries()) {
            if (res[index] && res[index].length) {
              console.log(res[index]);
              this.siteAuditCPSData[value] = res[index].filter(item => item.name?.trim()).map(item => item.name);
            }
          }
          const { customerList, portfolioList, siteList } = JSON.parse(JSON.stringify(this.siteAuditCPSData));
          this.clonedSiteAuditCPSListData = new SiteAuditCPSListDataClass(customerList, portfolioList, siteList);
          this.isCusPortSiteLoading = false;
        },
        error: () => {
          this.isCusPortSiteLoading = false;
        }
      });
    }
  }

  onCPSModelChange(event: string, variableName: string): void {
    const searchRegex = new RegExp(event, 'i');
    this.clonedSiteAuditCPSListData[variableName] = this.siteAuditCPSData[variableName].filter(item => searchRegex.test(item));
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllSiteAuditReportsList(true, filterParams);
    setTimeout(() => {
      this.viewdeletetedbutton = filterParams.isDelete;
      this.viewArchUnarchbutton = filterParams.isArchive;
    }, 0);
  }

  // sorting
  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllSiteAuditReportsList();
  }

  // Bind Site Audit Report Data
  getAllSiteAuditReportsList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.reportService.getAllSiteAuditReportListing(this.filterModel).subscribe({
        next: (res: SiteAuditReportModelDTO) => {
          this.reportsList(res);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  reportsList(data: SiteAuditReportModelDTO) {
    const setReportStatusId = (siteAuditReportItem: SiteAuditReportModel): SiteAuditReportModel => {
      const reportStatusId =
        siteAuditReportItem?.pdfUrl === null ? SA_REPORT_STATUS_ID_ENUMS.IN_PROGRESS : SA_REPORT_STATUS_ID_ENUMS.COMPLETE;
      return {
        ...siteAuditReportItem,
        reportStatusId,
        reportStatusLabel: SA_REPORT_STATUS_LABELS[reportStatusId]
      };
    };

    this.reportsData = data.siteAuditReports.map(setReportStatusId);
    this.total = data.totalReport;
    this.loading = false;
  }

  // Page Change
  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllSiteAuditReportsList();
  }

  // Page Size Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllSiteAuditReportsList();
  }

  // Clear workorder, site, portfolio and customer
  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = this.pageSize;
  }

  imageJhaCopyUrl(workOrderId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-content',
      initialState: {
        message: environment.baseUrl + '/entities/site-audit-report/image-gallery/' + workOrderId
      }
    };
    this.modalRef = this.modalService.show(ShareModelComponent, ngModalOptions);
  }

  deleteJHAReport(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this report?' }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        this.loading = true;
        if (result) {
          this.subscription.add(
            this.reportService.deleteJhaReport(event).subscribe({
              next: res => {
                this.loading = false;
                if (res) {
                  if (this.currentPage !== 0 && this.reportsData.length === 1) {
                    this.onChangeSize();
                  } else {
                    this.getAllSiteAuditReportsList();
                    this.alertService.showSuccessToast(res.message);
                  }
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = false;
        }
      });
    }
  }
  addExport() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    this.subscription.add(
      this.reportService.getAllSiteAuditReportListing(this.filterModel).subscribe({
        next: (data: SiteAuditReportModelDTO) => {
          const tittle = 'Site-Audit-Report';
          const rows: any = [['Customer', 'Site', 'Work Order', 'Year', 'Last uploaded']];
          for (const i of data.siteAuditReports) {
            const tempData = [
              i.customerName,
              i.siteName,
              i.workorderName,
              i.year,
              i.createdDate ? `${this.formateDate(i.createdDate)}` : ''
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.reportsList(data);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  formateDate(date: Date | string) {
    return this.datePipe.transform(date, AppConstants.fullDateFormat);
  }

  // view report detail
  viewReport(reportId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        reportId: reportId,
        viewdeletetedbutton: this.viewdeletetedbutton
      }
    };
    this.modalRef = this.modalService.show(ViewReportDetailComponent, ngModalOptions);
  }

  createSiteLayoutAndPhotoGalleryFiles(
    siteAuditNewReportDetails: StartNewSiteAuditReportModel,
    id: string,
    msg: string,
    isSiteAuditReportCreate: boolean
  ): void {
    this.newSAReportLoading = true;
    const tempArray = [];

    if (this.siteLayoutImg.length) {
      for (const i of this.siteLayoutImg) {
        const formData: FormData = new FormData();
        formData.append('fileImage', i as File);
        formData.append('reportId', id.toString());
        formData.append('customerName', siteAuditNewReportDetails.customerName);
        formData.append('portfolioName', siteAuditNewReportDetails.portfolioName);
        formData.append('siteName', siteAuditNewReportDetails.siteName);
        formData.append('reportName', siteAuditNewReportDetails.reportName);
        tempArray.push(this.reportService.getUploadedImages(formData));
      }
    }

    this.subscription.add(
      forkJoin(tempArray).subscribe({
        next: res => {
          this.siteLayoutImg = [];
          if (isSiteAuditReportCreate) {
            this.alertService.showSuccessToast(msg);
            this.modalRef.hide();
            this.router.navigate(['entities', 'site-audit-report', 'edit', id.toString(), 'siteauditreport']);
          }
          this.newSAReportLoading = false;
        },
        error: e => {
          this.newSAReportLoading = false;
        }
      })
    );
  }

  createNewSiteAuditReport(siteAuditNewReportDetails: StartNewSiteAuditReportModel) {
    this.newSAReportLoading = true;
    this.subscription.add(
      this.reportService.createNewSiteAuditReportWeb(siteAuditNewReportDetails).subscribe({
        next: res => {
          this.newSAReportLoading = false;
          if (this.siteLayoutImg.length) {
            this.createSiteLayoutAndPhotoGalleryFiles(siteAuditNewReportDetails, res.id, res.message, true);
          } else {
            this.alertService.showSuccessToast(res.message);
            this.modalRef.hide();
            this.router.navigate(['entities', 'site-audit-report', 'edit', res.id, 'siteauditreport']);
          }
        },
        error: e => {
          this.newSAReportLoading = false;
        }
      })
    );
  }

  addNewReport(startNewReportTemp): void {
    this.siteAuditNewReportModel = new StartNewSiteAuditReportModel({});
    this.getAllSiteAuditCusPortSiteInit();

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    this.modalRef = this.modalService.show(startNewReportTemp, ngModalOptions);
  }

  saveNewReportDetails(startNewReportForm: NgForm): void {
    if (startNewReportForm.valid) {
      const siteAuditNewReportDetails = new StartNewSiteAuditReportModel(this.siteAuditNewReportModel);
      this.createNewSiteAuditReport(siteAuditNewReportDetails);
    } else {
      startNewReportForm.form.markAsTouched();
    }
  }

  getUploadedFiles(files: File[], fileInput: HTMLInputElement) {
    if (files.length) {
      for (const fileItem of files) {
        const siteLayoutImgMimeType = fileItem.type;
        if (siteLayoutImgMimeType.match(/image\/*/) === null) {
          this.alertService.showErrorToast(`The image (${fileItem.name}) is invalid, or not supported. Allowed types: png, jpg, jpeg`);
        } else {
          this.siteLayoutImg.push(fileItem);
        }
      }
    }
    fileInput.value = '';
  }

  deleteFile(index: number) {
    this.siteLayoutImg.splice(index, 1);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
