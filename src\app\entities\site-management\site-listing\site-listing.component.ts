import { DatePipe, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { SITEFILTERLIST, Site, SiteExport, SiteFilterData, SitePageFilterKeys } from '../../../@shared/models/site.model';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerSiteInfoArchiveComponent } from '../../customer-management/customer-add-edit/customer-site-info-archive/customer-site-info-archive.component';
import { AppliedFilter } from '../../site-device/site-device.model';
import { SiteService } from '../site.service';
import { ViewSiteDetailsComponent } from '../view-site-details/view-site-details.component';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-site-listing',
  templateUrl: './site-listing.component.html',
  styleUrls: ['./site-listing.component.scss']
})
export class SiteListingComponent implements OnInit {
  sites: Site[] = [];
  subscription: Subscription = new Subscription();
  modalRef: BsModalRef;
  regex = AppConstants.regex;
  pageSize = AppConstants.rowsPerPage;
  loading = false;
  currentPage = 1;
  siteSearch = true;
  total: number;
  filterModel: CommonFilter = new CommonFilter();
  viewPage = FILTER_PAGE_NAME.SITE_INFO_SITES_LISTING;
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  filterList = SITEFILTERLIST;
  viewFilterSection = 'siteFilterSection';
  user: string;
  stateList: Dropdown[] = [];
  sortOptionList = {
    QeSiteId: 'asc',
    SiteName: 'asc',
    ACSize: 'asc',
    SiteDeviceCount: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    XFMR: 'asc',
    INV: 'asc',
    DCSize: 'asc'
  };
  filterDetails: FilterDetails = new FilterDetails();
  showBackButton = false;
  isArchivedModal = true;
  userRole = this.storageService.get('user').authorities;

  constructor(
    private readonly siteService: SiteService,
    private readonly modalService: BsModalService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    public datePipe: DatePipe,
    private readonly location: Location
  ) {}

  ngOnInit() {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection),
      sharedFilter = this.storageService.get(AppConstants.SHARED_FILTER_KEY);

    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.states = (localFilterData || defaultFilterData).states;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.initFilterDetails();
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = 'SiteName';
    let hasUrlFilter = false;
    this.user = this.storageService.get('user').authorities;
    this.route.queryParams.subscribe(params => {
      if (params['isSiteCount']) {
        this.filterModel.subregionIds = [];
        this.filterModel.regionIds = [];
        this.filterModel.states = [];
      }
      if (Object.keys(params).length) {
        if (params['customerId'] || params['portfolioId']) {
          this.showBackButton = true;
        }
        this.filterModel.customerIds = params['customerId'] ? [Number(params['customerId'])] : [];
        this.filterModel.portfolioIds = params['portfolioId'] ? [Number(params['portfolioId'])] : [];
        this.filterModel.isArchive = params['isArchive'] === 'true';
        this.filterModel.isActive = params['isArchive'] === 'false' ? 'Active' : '';
        this.filterModel.automationPartnerIds = params['automationPartnerId'] ? [Number(params['automationPartnerId'])] : [];
        this.filterModel.automationSiteIds = params['automationSiteId'] ? [Number(params['automationSiteId'])] : [];
        if (
          this.filterModel.portfolioIds.length ||
          this.filterModel.customerIds.length ||
          this.filterModel.automationPartnerIds.length ||
          this.filterModel.automationSiteIds.length
        ) {
          hasUrlFilter = true;
        }
      }
    });
    this.isFilterDisplay = filterSection ? filterSection : false;

    if (this.filterModel.direction && this.filterModel.sortBy) {
      this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
    }
    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }
    this.filterModel.customerIds = sharedFilter?.customerIds.length ? sharedFilter.customerIds : this.filterModel.customerIds || [];
    this.filterModel.portfolioIds = sharedFilter?.portfolioIds.length ? sharedFilter.portfolioIds : this.filterModel.portfolioIds || [];
    this.filterModel.siteIds = [];
    if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, sharedFilter, SitePageFilterKeys)) {
      this.getAllSiteList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.STATE.show = true;
    filterItem.STATE.multi = true;
    filterItem.SEARCH_BOX.show = true;
    filterItem.SHOW_STATUS.show = true;
    // filterItem.SHOW_NERC.show = true;
    filterItem.SHOW_NERC_SITE_TYPE.show = true;
    filterItem.AUTOMATION_DATA_SOURCE.show = true;
    filterItem.AUTOMATION_SITE.show = true;
    filterItem.SHOW_ARCHIVED.show = true;
    filterItem.QE_SERVICE_TYPE.show = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.default_sort = 'SiteName';
    this.filterDetails.filter_item = filterItem;
  }

  GotoSiteFilterByCustomer(customerdata) {
    const index = customerdata.findIndex(x => x.item_id === this.filterModel.customerId);
  }

  GotoSiteFilterByPortfolio(portdata) {
    const index = portdata.findIndex(x => x.item_id === this.filterModel.portfolioId);
  }

  // sorting
  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllSiteList();
  }

  gotoDevice(customerId, portfolioId, siteId, isArchive) {
    if (!isArchive) {
      this.router.navigate(['entities/site-device/list'], { queryParams: { customerId, portfolioId, siteId, isArchive } });
    }
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllSiteList();
  }

  Search() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.customerId = this.filterModel.customerId ? Number(this.filterModel.customerId) : null;
    this.filterModel.portfolioId = this.filterModel.portfolioId ? Number(this.filterModel.portfolioId) : null;
    this.getAllSiteList();
  }

  // Bind Sites
  getAllSiteList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.siteService.getAllSitesByfilter(this.filterModel).subscribe({
      next: (data: SiteFilterData) => {
        this.allsiteList(data);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  viewSite(siteId) {
    const viewsite = true;
    this.router.navigate(['edit/' + siteId], { queryParams: { viewsite } });
  }

  editSite(siteId) {
    const viewsite = false;
    this.router.navigate(['edit/' + siteId], { queryParams: { viewsite } });
  }

  allsiteList(data: SiteFilterData) {
    this.calculateTotalXfmrs(data);
    this.sites = data.sites;
    this.total = data.totalSite;
    this.loading = false;
  }

  private calculateTotalXfmrs(data: SiteFilterData | SiteExport) {
    data.sites.forEach(site => {
      site.totalXfmr = (site.xfmr ?? 0) + (site.dryXFMR ?? 0) + (site.bessxfmr ?? 0);
    });
  }

  // Go to Work Order
  GotoWO(customerId, portfolioId, siteId, noOfWOCount) {
    if (noOfWOCount > 0) {
      this.router.navigate(['/entities/workorders/'], { queryParams: { customerId, portfolioId, siteId } });
    }
  }

  // Pagesize Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllSiteList();
  }

  exportData() {
    this.loading = true;
    const filterModel: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    filterModel.IsExportExcel = true;
    this.subscription.add(
      this.siteService.getAllSitesByfilter(filterModel).subscribe({
        next: (data: SiteExport) => {
          this.calculateTotalXfmrs(data);
          const tittle = 'Sites';
          const rows: any = [
            [
              'Customer',
              'Portfolio',
              'Site Name',
              'Site ID',
              'NTP Date',
              'Site Address',
              'City',
              'State',
              'Zip Code',
              'Latitude',
              'Longitude',
              'Google Map Link',
              'Array Type',
              'AC Size (kW)',
              'DC Size (kW)',
              'Site Az (180 South)',
              'Tilt',
              'INV Type',
              '#of INV',
              '# of modules',
              '# of Combiners',
              '# of panelboards',
              '# of transformers',
              'Dry Customer XFMR',
              'Oil Filled Customer XFMR',
              'BESS Customer XFMR',
              'Active',
              'NERC',
              'NERC Site Type',
              'POI KV'
            ]
          ];
          for (const i of data.sites) {
            const tempData = [
              i.customerName,
              i.portfolioName,
              i.siteName,
              i.qeSiteId ? i.qeSiteId : '',
              i.contractStartDate ? this.datePipe.transform(i.contractStartDate, AppConstants.fullDateFormat) : '',
              i.address,
              i.city,
              i.state,
              `="${i.zipCode}"`,
              i.latitude ? i.latitude : '',
              i.logitude ? i.logitude : '',
              i.googleMapLink ? i.googleMapLink : '',
              i.siteTypeStr ? i.siteTypeStr : '',
              i.acSize ? i.acSize : '',
              i.dcSize ? i.dcSize : '',
              i.siteAz ? i.siteAz : '',
              i.tilt ? i.tilt : '',
              i.inverterTypeStr ? i.inverterTypeStr : '',
              i.inv ? i.inv : '',
              i.numberofModules ? i.numberofModules : '',
              i.numberofCombiners ? i.numberofCombiners : '',
              i.numberofPanelboards ? i.numberofPanelboards : '',
              i.totalXfmr ?? '',
              i.dryXFMR ? (i.isUtilityDryXFMR ? 'No' : 'Yes') : '',
              i.xfmr ? (i.utilityOwned ? 'No' : 'Yes') : '',
              i.bessxfmr ? (i.isUtilityBessXFMR ? 'No' : 'Yes') : '',
              i.isActive ? 'Yes' : 'No',
              i.isNERC ? 'Yes' : 'No',
              i.nercSiteTypeStr ? i.nercSiteTypeStr : '',
              i.poIkV ? i.poIkV : ''
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllSiteList(true, filterParams);
  }

  gotoMap(mapLink) {
    window.open(mapLink, '_blank');
  }

  viewSiteDetails(siteId: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        siteId: siteId
      }
    };
    this.modalRef = this.modalService.show(ViewSiteDetailsComponent, ngModalOptions);
  }

  goBack() {
    this.location.back();
  }

  archiveToggleChange(event, site) {
    this.isArchivedModal = false;
    if (event === false) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-full-dialog',
        initialState: {
          siteId: site.id,
          archiveModalFrom: 'sites',
          isFromListOrView: true
        }
      };
      this.modalRef = this.modalService.show(CustomerSiteInfoArchiveComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.getAllSiteList();
        } else {
          const index = this.sites.findIndex(item => item.id === site.id);
          this.sites[index].isArchive = true;
        }
        this.isArchivedModal = true;
      });
    }
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
    this.subscription.unsubscribe();
  }
}
