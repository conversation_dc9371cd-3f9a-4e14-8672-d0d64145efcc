import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter } from '../../site-device/site-device.model';
import { cmTruckRollReportPageFilterKeys, TruckRoll, TruckRollList } from '../cm-reports.model';
import { CmReportsService } from '../cm-reports.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-truck-roll',
  templateUrl: './truck-roll.component.html',
  styleUrls: ['./truck-roll.component.scss']
})
export class TruckRollComponent implements OnInit {
  loading = false;
  truckRolls: TruckRollList[] = [];
  subscription: Subscription = new Subscription();
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  total: number;
  viewPage = FILTER_PAGE_NAME.CM_TRUCK_ROLL_REPORT_LISTING;
  viewFilterSection = 'truckRollFilterSection';
  filterDetails: FilterDetails = new FilterDetails();
  truckRollTotalCountMonthWise: TruckRollList;
  sortOptionList = {
    CustomerPortfolio: 'asc',
    SiteName: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    Total: 'asc'
  };
  userRole = this.storageService.get('user').authorities;

  constructor(
    private readonly storageService: StorageService,
    private readonly cmService: CmReportsService,
    private readonly commonService: CommonService
  ) {}

  ngOnInit() {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection);

    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.states = (localFilterData || defaultFilterData).states;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.initFilterDetails();
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = 'CustomerPortfolio';
    this.isFilterDisplay = filterSection;

    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }

    if (!this.filterModel.year) {
      this.filterModel.year = this.commonService.getCurrentYear();
    }
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);

    if (
      this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, this.filterModel, cmTruckRollReportPageFilterKeys)
    ) {
      this.getTruckRollLists();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.SITE.show = true;
    filterItem.SITE.multi = true;
    filterItem.STATE.show = true;
    filterItem.START_YEAR.show = true;
    filterItem.FREQUENCY_TYPE.show = true;
    filterItem.FREQUENCY_TYPE.multi = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.default_direction = 'asc';
    this.filterDetails.default_sort = 'CustomerPortfolio';
    this.filterDetails.filter_item = filterItem;
  }

  getTruckRollLists(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.cmService.getAllTruckRollList(this.filterModel).subscribe({
      next: (res: TruckRoll) => {
        if (res) {
          setTimeout(() => {
            this.loading = false;
            this.truckRolls = res && res.truckRolls ? res.truckRolls : [];
            this.total = res && res.totalCount ? res.totalCount : 0;
            this.truckRollTotalCountMonthWise = res.truckRollTotalCountMonthWise;
          }, 0);
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getTruckRollLists(true, filterParams);
  }

  exportData() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    model.page = 0;
    model.itemsCount = this.total;
    this.subscription.add(
      this.cmService.getAllTruckRollList(model).subscribe({
        next: (data: TruckRoll) => {
          const tittle = 'Truck_Roll';
          const rows: any = [
            ['Customer', 'Portfolio', 'Site', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Total']
          ];
          for (const i of data.truckRolls) {
            const tempData = [
              i.customer,
              i.portfolio,
              i.site,
              i.jan ? i.jan : '-',
              i.feb ? i.feb : '-',
              i.mar ? i.mar : '-',
              i.apr ? i.apr : '-',
              i.may ? i.may : '-',
              i.jun ? i.jun : '-',
              i.jul ? i.jul : '-',
              i.aug ? i.aug : '-',
              i.sep ? i.sep : '-',
              i.oct ? i.oct : '-',
              i.nov ? i.nov : '-',
              i.dec ? i.dec : '-',
              i.total
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  // Pagesize Change
  onChangeSize() {
    this.currentPage = 0;
    this.filterModel.page = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getTruckRollLists();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getTruckRollLists();
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getTruckRollLists();
  }
}
