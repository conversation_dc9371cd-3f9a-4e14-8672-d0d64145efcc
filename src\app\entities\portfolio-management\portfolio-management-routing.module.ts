import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModeWiseGuard } from '../../@shared/services/mode-wise.guard';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { PortfolioAddEditComponent } from './portfolio-add-edit/portfolio-add-edit.component';
import { PortfolioListingComponent } from './portfolio-listing/portfolio-listing.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: PortfolioListingComponent,
    data: { pageTitle: 'Portfolios' }
  },
  {
    path: 'add',
    component: PortfolioAddEditComponent,
    canActivate: [PermissionGuard],
    data: { permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER], pageTitle: 'Add Portfolio' }
  },
  {
    path: ':mode/:id',
    component: PortfolioAddEditComponent,
    canActivate: [ModeWiseGuard],
    data: {
      edit: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.ANALYST, ROLE_TYPE.CUSTOMER],
      detail: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST, ROLE_TYPE.CUSTOMER],
      view: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST, ROLE_TYPE.CUSTOMER],
      pageTitle: 'Update Portfolio'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PortfolioManagementRoutingModule {}
