import { Component, OnInit } from '@angular/core';
import { NbThemeService } from '@nebular/theme';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FILTER_SECTION_ENUM, FilterDetails } from '../../@shared/components/filter/filter.model';
import { AppConstants } from '../../@shared/constants';
import { AllReportDropdown } from '../../@shared/models/report.model';
import { CommonService } from '../../@shared/services/common.service';
import { StorageService } from '../../@shared/services/storage.service';
import { AppliedFilter } from '../site-device/site-device.model';
import { CMDASHBOARDFILTERLIST, CMDashboard, CMDashboardData } from './cm-dashboard.model';
import { CmDashboardService } from './cm-dashboard.service';
import { ROLE_TYPE } from '../../@shared/enums';
import { checkAuthorisations } from '../../@shared/utils';

@Component({
  selector: 'sfl-cm-dashboard',
  templateUrl: './cm-dashboard.component.html',
  styleUrls: ['./cm-dashboard.component.scss']
})
export class CmDashboardComponent implements OnInit {
  loading = false;
  appliedFilter: AppliedFilter[] = [];
  filterModel: CommonFilter = new CommonFilter();
  isFilterDisplay = false;
  viewPage = FILTER_PAGE_NAME.CM_DASHBOARD;
  filterList = CMDASHBOARDFILTERLIST;
  viewFilterSection = 'cmDashBoardFilterSection';
  subscription: Subscription = new Subscription();
  allReportDropdown = new AllReportDropdown();
  cmTicketData: CMDashboard = new CMDashboard();
  filterDetails: FilterDetails = new FilterDetails();
  currentTheme = 'dark';
  pieChartData: any;
  initOpts = {
    renderer: 'svg',
    height: 230
  };
  productionLossChart: any;
  truckRollsChart: any;
  topSiteTruckRollsChart: any;
  ticketChart: any;
  userRole = this.storageService.get('user').authorities;

  constructor(
    private readonly storageService: StorageService,
    private readonly cmDashboardService: CmDashboardService,
    private readonly commonService: CommonService,
    private readonly themeService: NbThemeService
  ) {}

  ngOnInit(): void {
    this.themeService.onThemeChange().subscribe(themeName => {
      this.currentTheme = themeName.name;
    });
    this.initFilterDetails();
    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection);
    const localFilterData = this.storageService.get('userDefaultFilter');
    const defaultFilterData = this.storageService.get('user').userFilterSelection;
    this.isFilterDisplay = filterSection;
    if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
    }
    if (!this.filterModel.year) {
      this.filterModel.year = this.commonService.getCurrentYear();
    }
    this.getAllList();
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    this.filterDetails.filterSectionEnum = FILTER_SECTION_ENUM.CM_DASHBOARD;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.START_YEAR.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.SITE.multi = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }
    this.filterDetails.filter_item = filterItem;
  }

  getAllList(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    this.cmTicketData = new CMDashboard();
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.cmDashboardService.getTicketDashboardList(this.filterModel).subscribe({
        next: (res: CMDashboard) => {
          if (res) {
            this.pieChartData = this.getChartOption(res.ticketsList);
            this.ticketChart = this.getGroupBarChart(res.tickets, 'TICKETS', ['#0F9D58', '#F47242']);
            this.productionLossChart = this.getBarChart(res.productionLoss, 'PRODUCTION LOSS TICKETS', ['#4285F4']);
            this.truckRollsChart = this.getBarChart(res.truckRolls, 'TRUCK ROLLS', ['#1297AC']);
            this.topSiteTruckRollsChart = this.getBarChart(res.topSiteTruckRolls, 'TOP SITE TRUCK ROLLS', ['#1297AC'], true);
            setTimeout(() => {
              this.cmTicketData = res;
              this.loading = false;
            }, 0);
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  refreshList(filterParams: CommonFilter) {
    this.getAllList(true, filterParams);
  }

  getChartOption(data: CMDashboardData[]) {
    return {
      legend: {
        bottom: 'bottom',
        type: 'scroll',
        padding: [60, 0, 20, 0]
      },
      tooltip: {
        trigger: 'item'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: data,
          color: ['#9BB7C2', '#D8B106', '#008F26']
        }
      ]
    };
  }

  getBarChart(data, title, color, isSiteTruckRoll = false) {
    if (isSiteTruckRoll) {
      return {
        title: {
          text: title,
          left: 'center',
          top: 45,
          textStyle: {
            fontSize: '0.9375rem'
          }
        },
        color: color,
        grid: {
          y: 100,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          show: true,
          orient: 'horizontal',
          feature: {
            dataZoom: { show: true },
            dataView: {
              show: false
            },
            magicType: { type: ['line', 'bar'] },
            restore: {},
            saveAsImage: {}
          },
          top: 7,
          right: 40
        },
        xAxis: [
          {
            type: 'value'
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: data.map(e => e.name),
            axisLabel: {
              show: true,
              interval: 0
            }
          }
        ],
        series: [
          {
            type: 'bar',
            emphasis: {
              focus: 'series'
            },
            barGap: 0,
            tooltip: {
              valueFormatter: function (value: any) {
                return (value as number).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                });
              }
            },
            data: data.map(e => e.value)
          }
        ]
      };
    }

    return {
      title: {
        text: title,
        left: 'center',
        top: 45,
        textStyle: {
          fontSize: '0.9375rem'
        }
      },
      color: color,
      grid: {
        y: 100,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        }
      },
      toolbox: {
        show: true,
        orient: 'horizontal',
        feature: {
          dataZoom: { show: true },
          dataView: {
            show: false
          },
          magicType: { type: ['line', 'bar'] },
          restore: {},
          saveAsImage: {}
        },
        top: 7,
        right: 40
      },
      xAxis: [
        {
          type: 'category',
          data: data.map(e => e.name)
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          type: 'bar',
          emphasis: {
            focus: 'series'
          },
          barGap: 0,
          tooltip: {
            valueFormatter: function (value: any) {
              return (value as number).toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          },
          data: data.map(e => e.value)
        }
      ]
    };
  }

  getGroupBarChart(data, title, color) {
    const closed = [];
    const open = [];
    data.forEach(i => {
      closed.push(i.series[0].value);
      open.push(i.series[1].value);
    });
    return {
      legend: {
        bottom: 'bottom',
        type: 'scroll',
        padding: [60, 0, 20, 0]
      },
      title: {
        text: title,
        left: 'center',
        top: 45,
        textStyle: {
          fontSize: '0.9375rem'
        }
      },
      color: color,
      grid: {
        y: 100,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        }
      },
      toolbox: {
        show: true,
        orient: 'horizontal',
        feature: {
          dataZoom: { show: true },
          dataView: {
            show: false
          },
          magicType: { type: ['line', 'bar'] },
          restore: {},
          saveAsImage: {}
        },
        top: 7,
        right: 40
      },
      xAxis: [
        {
          type: 'category',
          data: data.map(e => e.name)
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          type: 'bar',
          name: 'Closed',
          emphasis: {
            focus: 'series'
          },
          barGap: 0,
          tooltip: {
            valueFormatter: function (value: any) {
              return (value as number).toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          },
          data: closed
        },
        {
          type: 'bar',
          name: 'Open',
          emphasis: {
            focus: 'series'
          },
          barGap: 0,
          tooltip: {
            valueFormatter: function (value: any) {
              return (value as number).toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          },
          data: open
        }
      ]
    };
  }
}
