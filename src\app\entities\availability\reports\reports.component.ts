import { DatePip<PERSON>, DecimalPipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { NbDateService, NbThemeService } from '@nebular/theme';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Subscription, forkJoin } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerService } from '../../customer-management/customer.service';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { SiteService } from '../../site-management/site.service';
import {
  AutomationSitePortfolio,
  Directions,
  ExclusionReports,
  FilterByList,
  IntervalChildList,
  IntervalList,
  OperatorsList,
  ReportFilter,
  ReportsDataList
} from './report.model';
import { ReportsService } from './reports.service';
import { checkAuthorisations } from '../../../@shared/utils/roles-permission.utils';
import { ROLE_TYPE } from '../../../@shared/enums/roles.enum';

@Component({
  selector: 'sfl-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss'],
  providers: [DecimalPipe]
})
export class ReportsComponent implements OnInit {
  loading = false;
  reportsData: ReportsDataList = new ReportsDataList();
  exclusionReportsData: ExclusionReports = new ExclusionReports();
  filterModel: ReportFilter = new ReportFilter();
  subscription: Subscription = new Subscription();
  customerList: Dropdown[];
  portfolioList: Dropdown[];
  siteList: Dropdown[];
  max: Date;
  years = [];
  isDaily = false;
  intervalType: IntervalList[] = [
    { name: 'Annual', intervalChildList: [{ name: 'Annual' }] },
    { name: 'Semi-Annual', intervalChildList: [{ name: 'H1' }, { name: 'H2' }] },
    {
      name: 'Quarterly',
      intervalChildList: [{ name: 'Q1' }, { name: 'Q2' }, { name: 'Q3' }, { name: 'Q4' }]
    },
    {
      name: 'Monthly',
      intervalChildList: [
        { name: 'JAN' },
        { name: 'FEB' },
        { name: 'MAR' },
        { name: 'APR' },
        { name: 'MAY' },
        { name: 'JUN' },
        { name: 'JUL' },
        { name: 'AUG' },
        { name: 'SEP' },
        { name: 'OCT' },
        { name: 'NOV' },
        { name: 'DEC' }
      ]
    },
    {
      name: 'Daily',
      intervalChildList: [
        { name: 'JAN' },
        { name: 'FEB' },
        { name: 'MAR' },
        { name: 'APR' },
        { name: 'MAY' },
        { name: 'JUN' },
        { name: 'JUL' },
        { name: 'AUG' },
        { name: 'SEP' },
        { name: 'OCT' },
        { name: 'NOV' },
        { name: 'DEC' }
      ]
    }
  ];
  periodLists: IntervalChildList[] = [{ name: 'Annual' }];
  reportTypeLists: IntervalChildList[] = [{ name: 'Availability Report' }, { name: 'Exclusion Report' }];
  periodDateRange = { start: new Date(), end: new Date() };
  exclusionAccordion = true;
  generated = false;
  filterByList = FilterByList.filter(x => x.reportType === 'both' || x.reportType === 'availabilityReport');
  operatorsList = OperatorsList;
  cusSort = { key: 'name', direction: 'asc' };
  cusDirection = JSON.parse(JSON.stringify(Directions));
  portfolioSort = { key: 'name', direction: 'asc' };
  portfolioDirection = JSON.parse(JSON.stringify(Directions));
  siteSort = { key: 'name', direction: 'asc' };
  siteDirection = JSON.parse(JSON.stringify(Directions));
  userRoles: string;
  currentTheme = 'dark';
  filteredCustomerIds: number[] = [];
  filteredPortfolioIds: number[] = [];
  filteredSiteIds: number[] = [];
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly datePipe: DatePipe,
    private readonly siteService: SiteService,
    private readonly _decimalPipe: DecimalPipe,
    private readonly commonService: CommonService,
    private readonly themeService: NbThemeService,
    private readonly reportsService: ReportsService,
    private readonly storageService: StorageService,
    private readonly customerService: CustomerService,
    private readonly dateService: NbDateService<Date>,
    private readonly portfolioService: PortfolioService
  ) {}

  ngOnInit() {
    this.themeService.onThemeChange().subscribe(themeName => {
      this.currentTheme = themeName.name;
    });

    const localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      shared = this.storageService.get(AppConstants.SHARED_FILTER_KEY),
      data: AutomationSitePortfolio = new AutomationSitePortfolio();

    this.userRoles = this.storageService.get('user').authorities;
    this.max = this.dateService.addDay(this.dateService.today(), -1);
    this.years = this.commonService.getYear(false, true, true);

    data.isAvailabilityCheck = true;

    const tempArrayObj = ['customerList', 'portfolioList'],
      tempArray = [this.customerService.getAvailCustomer(), this.portfolioService.getAllAutomationPortfoliosByCustomerId(data)];

    const activeFilterData = shared || localFilterData || defaultFilterData;
    this.filterModel.customerIds = activeFilterData.customerIds || [];
    // to be used
    // this.filterModel.customerId = activeFilterData.customerId || null;
    this.filterModel.portfolioIds = activeFilterData.portfolioIds || [];
    this.filterModel.siteIds = activeFilterData.siteIds || [];
    this.getAllLists(tempArray, tempArrayObj, {
      portfolioIds: activeFilterData.portfolioIds || [],
      siteIds: activeFilterData.siteIds || []
    });
  }
  // not enough list getting for customers as BNRG shared but there is not bnrg in list
  getAllLists(apiArray: any, mapResultList: string[], filterData?: { portfolioIds: number[]; siteIds: number[] }) {
    this.loading = true;
    if (apiArray.length) {
      forkJoin(apiArray).subscribe({
        next: (res: any) => {
          for (const [index, value] of mapResultList.entries()) {
            if (value === 'customerList') {
              this[value] = res[index].filter(item => item.isActive);
            } else {
              this[value] = res[index];
              this.filterModel.portfolioIds = this[value]
                .filter(obj => filterData.portfolioIds.includes(obj.id))
                .map(element => element.id);

              this.getSiteDropdownValues(
                {
                  customerIds: this.filterModel.customerIds,
                  // to be used
                  // customerIds: this.filterModel.customerId ? [this.filterModel.customerId] : [],
                  portfolioIds: this.filterModel.portfolioIds,
                  isAvailabilityCheck: true
                },
                filterData
              );
            }
          }
        },
        error: () => (this.loading = false)
      });
    }
  }

  getSiteDropdownValues(data: AutomationSitePortfolio, filterData) {
    this.siteService.getAllAutomationSiteById(data).subscribe({
      next: res => {
        this.siteList = res;
        this.filterModel.siteIds = this.siteList.filter(obj => filterData.siteIds.includes(obj.id)).map(element => element.id);
        if (this.siteList.length && this.filterModel.siteIds && this.filterModel.siteIds.length) {
          const bindIds: any[] = [];
          for (const id of this.filterModel.siteIds) {
            const filteredIds = this.siteList.filter(x => x.id === id);
            if (filteredIds.length) {
              bindIds.push(filteredIds[0].id);
            }
          }
          this.filterModel.siteIds = [...bindIds];
        }

        this.loading = false;

        if (this.filterModel.siteIds && this.filterModel.siteIds.length) {
          this.getReportData();
          this.generated = true;
        }
      }
    });
  }

  onCustomerChange() {
    this.filterModel.portfolioIds = [];
    this.filterModel.siteIds = [];

    this.portfolioList = [];
    this.siteList = [];
    this.reportsData = null;
    this.exclusionReportsData = null;
    this.reportsData = null;
    this.exclusionReportsData = null;
    this.getAllPortfolioByCustomer();
    this.getAllSiteByPortfolio();
    this.setAnnuallyOption();

    // to be used
    // if (!this.filterModel.customerIds.length) {
    //   this.filterModel.portfolioIds = [];
    //   this.filterModel.siteIds = [];
    // }
  }

  selectUnselectAllCustomer(isSelect: boolean) {
    if (isSelect) {
      if (this.filteredCustomerIds.length) {
        this.filterModel.customerIds = [
          ...new Set([...this.filterModel.customerIds, ...JSON.parse(JSON.stringify(this.filteredCustomerIds))])
        ];
      } else {
        this.filterModel.customerIds = this.customerList.map(customer => customer.id);
      }
    } else {
      if (this.filteredCustomerIds.length) {
        this.filterModel.customerIds = this.filterModel.customerIds.filter(x => !this.filteredCustomerIds.includes(x));
      } else {
        this.filterModel.customerIds = [];
      }
    }
    this.onCustomerChange();
  }

  setAnnuallyOption() {
    const data = JSON.parse(JSON.stringify(this.periodLists));
    if (data.length) {
      this.periodLists = [];
      if (
        !this.filterModel.customerId ||
        this.filterModel.customerIds.length !== 1 ||
        this.filterModel.portfolioIds.length !== 1 ||
        this.filterModel.siteIds.length !== 1 ||
        this.filterModel.reportType === 'Exclusion Report'
      ) {
        data.forEach(x => {
          if (x.name === 'Annually') {
            x.disabled = true;
          }
        });
        if (this.filterModel.period === 'Annually') {
          this.filterModel.period = data[0].name;
        }
        if (!this.periodDateRange) {
          this.filterModel.period = null;
          this.periodDateRange = {
            start: this.dateService.addDay(this.dateService.today(), -1),
            end: this.dateService.addDay(this.dateService.today(), -1)
          };
        }
      } else {
        data.forEach(x => (x.disabled = false));
      }
      setTimeout(() => {
        this.periodLists = data;
      }, 0);
    }
  }

  getAllPortfolioByCustomer() {
    this.loading = true;
    const data: AutomationSitePortfolio = new AutomationSitePortfolio();
    // to be used
    // data.customerIds = [this.filterModel.customerId];
    data.isAvailabilityCheck = true;
    this.subscription.add(
      this.portfolioService.getAllAutomationPortfoliosByCustomerId(data).subscribe({
        next: async (res: Dropdown[]) => {
          this.portfolioList = res;
          this.filterModel.portfolioIds = await this.validateSelectedPortfolios();
          this.onPortfolioChange();
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  validateSelectedPortfolios(): Promise<number[]> {
    return new Promise(resolve => {
      resolve(this.portfolioList.filter(o1 => this.filterModel?.portfolioIds?.some(o2 => o1.id === o2)).map(item => item.id));
    });
  }

  onPortfolioChange() {
    this.siteList = [];
    this.reportsData = null;
    this.exclusionReportsData = null;
    this.reportsData = null;
    this.exclusionReportsData = null;
    this.setAnnuallyOption();

    if (this.filterModel.portfolioIds.length) {
      this.getAllSiteByPortfolio();
    } else {
      this.filterModel.siteIds = [];
    }
  }

  selectUnselectAllPortfolio(isSelect) {
    if (isSelect) {
      if (this.filteredPortfolioIds.length) {
        this.filterModel.portfolioIds = [
          ...new Set([...this.filterModel.portfolioIds, ...JSON.parse(JSON.stringify(this.filteredPortfolioIds))])
        ];
      } else {
        this.filterModel.portfolioIds = this.portfolioList.map(portfolio => portfolio.id);
      }
    } else {
      if (this.filteredPortfolioIds.length) {
        this.filterModel.portfolioIds = this.filterModel.portfolioIds.filter(x => !this.filteredPortfolioIds.includes(x));
      } else {
        this.filterModel.portfolioIds = [];
      }
    }
    this.onPortfolioChange();
  }

  selectUnselectAllSite(isSelect: boolean) {
    if (isSelect) {
      if (this.filteredSiteIds.length) {
        this.filterModel.siteIds = [...new Set([...this.filterModel.siteIds, ...JSON.parse(JSON.stringify(this.filteredSiteIds))])];
      } else {
        this.filterModel.siteIds = this.siteList.map(site => site.id);
      }
    } else {
      if (this.filteredSiteIds.length) {
        this.filterModel.siteIds = this.filterModel.siteIds.filter(x => !this.filteredSiteIds.includes(x));
      } else {
        this.filterModel.siteIds = [];
      }
    }
  }

  getAllSiteByPortfolio() {
    this.loading = true;
    const data: AutomationSitePortfolio = new AutomationSitePortfolio();
    // to be used
    // data.customerIds = [this.filterModel.customerId];
    data.portfolioIds = this.filterModel.portfolioIds;
    data.customerIds = this.filterModel.customerIds;
    data.isAvailabilityCheck = true;
    this.subscription.add(
      this.siteService.getAllAutomationSiteById(data).subscribe({
        next: async (res: Dropdown[]) => {
          this.siteList = res;
          this.filterModel.siteIds = await this.validateSelectedSites();
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  validateSelectedSites(): Promise<number[]> {
    return new Promise(resolve => {
      resolve(this.siteList.filter(o1 => this.filterModel?.siteIds?.some(o2 => o1.id === o2)).map(item => item.id));
    });
  }

  onSiteChange() {
    this.reportsData = null;
    this.exclusionReportsData = null;
    this.setAnnuallyOption();
  }

  selectAndDeselectAll(array: Dropdown[], forValue: string, isSelect = false, addValue = 'id') {
    const tempData: number[] = [];
    if (isSelect) {
      for (const i of array) {
        tempData.push(i[addValue]);
      }
    }
    this.filterModel[forValue] = tempData;
    if (forValue === 'customerId' || forValue === 'customerIds') {
      this.onCustomerChange();
    } else if (forValue === 'portfolioIds') {
      this.onPortfolioChange();
    } else {
      this.onSiteChange();
    }
  }

  onYearSelect(e) {
    this.reportsData = null;
    this.exclusionReportsData = null;
  }

  onIntervalSelect(e: IntervalList) {
    this.reportsData = null;
    this.exclusionReportsData = null;
    this.filterModel.period = null;
    this.periodLists = e.intervalChildList;
    if (e.name !== 'Daily') {
      this.filterModel.period = this.periodLists[0].name;
    } else {
      this.periodDateRange = {
        start: this.dateService.addDay(this.dateService.today(), -1),
        end: this.dateService.addDay(this.dateService.today(), -1)
      };
    }
    this.setAnnuallyOption();
  }

  getReportData() {
    this.loading = true;
    const model = JSON.parse(JSON.stringify(this.filterModel));
    model.thresholdValue = model.thresholdValue ? Number(model.thresholdValue) : null;
    // to be used
    // model.customerIds = model.customerId ? [Number(model.customerId)] : [];
    // delete model.customerId;
    if (model.interval === 'Daily' && !model.period) {
      if (this.periodDateRange) {
        if (this.periodDateRange.start && this.periodDateRange.end) {
          model.period = `${this.datePipe.transform(this.periodDateRange.start, AppConstants.fullDateFormat)} - ${this.datePipe.transform(
            this.periodDateRange.end,
            AppConstants.fullDateFormat
          )}`;
        } else if (this.periodDateRange.start) {
          model.period = `${this.datePipe.transform(this.periodDateRange.start, AppConstants.fullDateFormat)} - ${this.datePipe.transform(
            this.periodDateRange.start,
            AppConstants.fullDateFormat
          )}`;
        }
      }
    }
    if (model.reportType === 'Availability Report') {
      this.getAvailabilityReportData(model);
    } else {
      this.getExclusionReportData(model);
    }
  }

  getAvailabilityReportData(model: ReportFilter) {
    this.loading = true;
    this.subscription.add(
      this.reportsService.getAvailabilityReportData(model).subscribe({
        next: (res: ReportsDataList) => {
          this.reportsData = res;
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  getExclusionReportData(model: ReportFilter) {
    this.loading = true;
    this.subscription.add(
      this.reportsService.getExclusionReportData(model).subscribe({
        next: (res: ExclusionReports) => {
          this.exclusionReportsData = res;
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  toggleChange(event, key: string) {
    this.filterModel[key] = event;
    if (
      (event === true && this.reportsData) ||
      (!this.filterModel.isCustomerSelected && !this.filterModel.isPortfolioSelected && !this.filterModel.isSiteSelected)
    ) {
      this.getReportData();
    }
  }

  clearFilter() {
    this.filterModel = new ReportFilter();
    this.reportsData = null;
    this.exclusionReportsData = null;
  }

  toolTipLabelFormat = (data): string => {
    return `<strong>${data.data.label} :  </strong> ${this._decimalPipe.transform(data.data.value)}%`;
  };

  exportToPdf() {
    this.loading = true;
    const logo = document.createElement('img');
    logo.src = './../../../../assets/images/LogoNew.png';
    const data: any = document.getElementById('exportSection');
    html2canvas(data).then(canvas => {
      const fileWidth = 208;
      const fileHeight = (canvas.height * fileWidth) / canvas.width;
      const fileUri = canvas.toDataURL('image/png');
      const PDF = new jsPDF('p', 'mm', 'a4');
      const position = 0;
      PDF.addImage(logo, 'PNG', 88.5, 5, 35, 20);
      PDF.addImage(fileUri, 'PNG', 1, 30, fileWidth, fileHeight);
      PDF.save(`${this.filterModel.reportType}.pdf`);
      this.loading = false;
    });
  }

  exportToExcel() {
    this.loading = true;
    const model = JSON.parse(JSON.stringify(this.filterModel));
    if (model.interval === 'Daily' && !model.period) {
      if (this.periodDateRange) {
        if (this.periodDateRange.start && this.periodDateRange.end) {
          model.period = `${this.datePipe.transform(this.periodDateRange.start, AppConstants.fullDateFormat)} - ${this.datePipe.transform(
            this.periodDateRange.end,
            AppConstants.fullDateFormat
          )}`;
        } else if (this.periodDateRange.start) {
          model.period = `${this.datePipe.transform(this.periodDateRange.start, AppConstants.fullDateFormat)} - ${this.datePipe.transform(
            this.periodDateRange.start,
            AppConstants.fullDateFormat
          )}`;
        }
      }
    }
    model.thresholdValue = model.thresholdValue ? Number(model.thresholdValue) : null;
    if (model.reportType === 'Availability Report') {
      this.subscription.add(
        this.reportsService.exportAvailabilityData(model).subscribe({
          next: (res: Blob) => {
            if (res) {
              this.downloadFile(res);
            }
          },
          error: _e => {
            this.loading = false;
          }
        })
      );
    } else {
      this.subscription.add(
        this.reportsService.exportExclusionData(model).subscribe({
          next: (res: Blob) => {
            if (res) {
              this.downloadFile(res);
            }
          },
          error: _e => {
            this.loading = false;
          }
        })
      );
    }
  }

  downloadFile(res: Blob) {
    const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
    link.download = `${this.filterModel.reportType}.xlsx`;
    link.click();
    this.loading = false;
  }

  reportTypeChange() {
    this.reportsData = null;
    this.exclusionReportsData = null;
    this.filterModel.filterBy = null;
    if (this.filterModel.reportType === 'Exclusion Report' && this.filterModel.period === 'Annually' && this.periodLists.length) {
      this.filterModel.period = this.periodLists[0].name;
    }
    if (this.filterModel.reportType === 'Exclusion Report') {
      this.filterByList = FilterByList.filter(x => x.reportType === 'both' || x.reportType === 'exclusionReport');
    } else {
      this.filterByList = FilterByList.filter(x => x.reportType === 'both' || x.reportType === 'availabilityReport');
    }
    this.setAnnuallyOption();
  }

  getChartOption(data) {
    return {
      title: {
        text: 'Exclusion Types',
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: '0.9375rem'
        }
      },
      legend: {
        bottom: 'bottom',
        type: 'scroll',
        padding: [60, 0, 20, 0]
      },
      tooltip: {
        trigger: 'item'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: data
        }
      ]
    };
  }

  onDropdownSearchFilter(event: any, filterListType: string) {
    if (event.term) {
      this[filterListType] = event.items?.map(element => element.id);
    } else {
      this[filterListType] = [];
    }
  }
}
