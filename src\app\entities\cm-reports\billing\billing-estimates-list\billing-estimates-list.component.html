<div class="row" *ngIf="billingEstimatesList.length && checkAuthorisationsFn([roleType.MANAGER, roleType.ADMIN])">
  <div class="col-md-4">
    <div class="d-flex align-items-center pb-3">
      <ng-select
        class="sfl-track-dropdown me-3"
        id="bulkActionName"
        name="bulkAction"
        [(ngModel)]="bulkActionName"
        placeholder="Select bulk action"
        [closeOnSelect]="true"
        appendTo="body"
      >
        <ng-option value="6"> Mark as Billed</ng-option>
        <ng-option value="5"> Mark as Billing</ng-option>
      </ng-select>
      <p class="mb-0">
        <button
          nbButton
          id="performance-view-data-btn"
          class="w-100"
          status="primary"
          size="small"
          type="button"
          (click)="applyBulkAction()"
        >
          Apply
        </button>
      </p>
    </div>
  </div>
</div>
<div id="fixed-table" class="col-12 table-responsive table-card-view">
  <table class="table table-hover table-bordered" aria-describedby="Ticket List">
    <thead>
      <tr>
        <th scope="col" class="text-center">
          <div>
            <nb-checkbox [(ngModel)]="isMasterSel" (change)="selectDeselectAllEstimates()"></nb-checkbox>
          </div>
        </th>
        <th>Customer</th>
        <th>Portfolio</th>
        <th>Site</th>
        <th>Site Id</th>
        <th>Ticket Number</th>
        <th>Estimate Number</th>
        <th>Estimate Status</th>
        <th>Approved By</th>
        <th>Approved On</th>
        <th>Additional Hours</th>
        <th>Estimate Total</th>
        <th>Attachment</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngIf="billingEstimatesList?.length">
        <tr
          *ngFor="
            let rowData of billingEstimatesList
              | paginate
                : {
                    id: 'estimateTable',
                    itemsPerPage: estimatePageSize,
                    currentPage: estimateCurrentPage,
                    totalItems: estimateTotalRecords
                  }
          "
        >
          <td data-title="Select" class="text-center">
            <nb-checkbox
              [checked]="rowData.isSelected"
              (change)="selectDeselectEstimates()"
              [(ngModel)]="rowData.isSelected"
              [disabled]="
                rowData.estimateStatus === 'Pending Approval' ||
                rowData.estimateStatus === 'Declined' ||
                (rowData.estimateStatus === 'Approved' && !rowData.customerPO)
              "
            ></nb-checkbox>
          </td>
          <td data-title="Customer">{{ rowData.customerName }}</td>
          <td data-title="Portfolio">{{ rowData.portfolioName }}</td>
          <td data-title="Site">{{ rowData.siteName }}</td>
          <td data-title="Site Id">{{ rowData.qeSiteId }}</td>
          <td data-title="Ticket Number">
            <a [href]="'../entities/ticket/detail/view/' + rowData.ticketNumber">
              {{ rowData?.ticketNumber }}
            </a>
          </td>
          <td data-title="Estimate Number">{{ rowData.estimateNumber }}</td>
          <td data-title="Estimate Status">{{ rowData.estimateStatus }}</td>
          <td data-title="Approved By">{{ rowData.approvedByName }}</td>
          <td data-title="Approved On">{{ rowData.approvedOn | date : dateFormat }}</td>
          <td data-title="Additional Hours">{{ rowData.additionalHours }}</td>
          <td data-title="Estimate Total">{{ rowData.estimateTotal | currency : 'USD' }}</td>
          <td data-title="Attachment">
            <p class="mb-0" *ngFor="let attachment of rowData.ticketEstimateAttachments">
              <a [href]="attachment?.documentUrl" target="_blank">
                {{ attachment?.fileName }}
              </a>
            </p>
          </td>
        </tr>
      </ng-container>
      <ng-container *ngIf="!billingEstimatesList?.length">
        <tr>
          <td colspan="13" class="no-record text-center">No Data Found</td>
        </tr>
      </ng-container>
    </tbody>
  </table>
</div>
<div class="mt-2 d-md-flex align-items-center" *ngIf="billingEstimatesList?.length">
  <div class="d-flex align-items-center">
    <label class="mb-0">Items per page: </label>
    <ng-select
      class="ms-2"
      [(ngModel)]="estimatePageSize"
      [clearable]="false"
      [searchable]="false"
      (change)="onChangeSize()"
      appendTo="body"
    >
      <ng-option value="5">5</ng-option>
      <ng-option value="10">10</ng-option>
      <ng-option value="50">50</ng-option>
      <ng-option value="100">100</ng-option>
    </ng-select>
  </div>
  <strong class="ms-md-3">Total: {{ estimateTotalRecords }}</strong>
  <div class="ms-md-auto ms-sm-0">
    <pagination-controls id="estimateTable" (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
  </div>
</div>
