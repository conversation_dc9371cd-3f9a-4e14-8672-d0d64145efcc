<nb-card class="customerSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Customers</h6>
        <div class="ms-auto">
          <button
            *ngIf="customers?.length"
            class="linear-mode-button"
            nbButton
            size="small"
            type="button"
            status="primary"
            (click)="exportData()"
            [disabled]="loading"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
          <button
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="small"
            routerLink="add"
            type="button"
            [disabled]="loading"
            *ngIf="user[0] === 'admin'"
          >
            <span class="d-none d-lg-inline-block">Add Customer</span>
            <i class="d-inline-block d-lg-none fa-solid fa-user-plus"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div class="col-12 customerFilter appFilter">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive mt-3 table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Customer List">
          <thead>
            <tr>
              <th (click)="sort('CustomerName', sortOptionList['CustomerName'])" id="customerName">
                <div class="d-flex align-items-center">
                  Customer
                  <span
                    class="fa text-center cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['CustomerName'] === 'desc',
                      'fa-arrow-down': sortOptionList['CustomerName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'CustomerName'
                    }"
                  ></span>
                </div>
              </th>
              <th class="text-end" id="noOfSite">Number of Sites</th>
              <th class="text-end" id="dcSize">kWdc</th>
              <th class="text-center" id="active">Active</th>
              <th class="text-center" id="action" *ngIf="!checkAuthorisationsFn([roleType.FIELDTECH, roleType.CUSTOMER])">
                {{ filterModel?.isArchive ? 'Archive' : 'Action' }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let customer of customers
                  | paginate
                    : {
                        itemsPerPage: filterModel.itemsCount,
                        currentPage: currentPage,
                        totalItems: total
                      };
                let i = index
              "
            >
              <td data-title="Customer" class="td-custom-width">
                <a [routerLink]="['detail/' + customer.id]">{{ customer.customerName }}</a>
              </td>
              <td data-title="Number of Sites	" class="text-end">
                <a
                  (click)="gotoSite(customer?.id, customer?.totalSites, customer?.isArchive)"
                  class="text-primary"
                  [ngClass]="{
                    'cursor-pointer': customer?.totalSites > 0,
                    'not-allowed': customer?.totalSites === 0 || customer?.isArchive
                  }"
                  >{{ customer?.totalSites }}{{ customer?.totalSites.length }}</a
                >
              </td>
              <td data-title="kWdc" class="text-end">{{ customer?.sumDCSize | sflRound | sflNumberWithCommas }}</td>
              <td data-title="Active" class="text-center"><nb-toggle [(checked)]="customer.isActive" disabled></nb-toggle></td>
              <td
                data-title="Action"
                class="text-center customer-action"
                *ngIf="!checkAuthorisationsFn([roleType.FIELDTECH, roleType.CUSTOMER])"
              >
                <div class="d-md-flex justify-content-center" *ngIf="!customer.isArchive && isArchivedModal">
                  <a class="px-2 listgrid-icon" [routerLink]="['edit/' + customer.id]">
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                </div>
                <div class="d-flex justify-content-center" *ngIf="customer.isArchive">
                  <nb-toggle
                    (checkedChange)="archiveToggleChange($event, customer)"
                    [(checked)]="customer.isArchive"
                    status="primary"
                    nbTooltip="UnArchive"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    class="me-3"
                  ></nb-toggle>
                </div>
              </td>
            </tr>
            <tr *ngIf="!customers?.length">
              <td colspan="5" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="customers?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
