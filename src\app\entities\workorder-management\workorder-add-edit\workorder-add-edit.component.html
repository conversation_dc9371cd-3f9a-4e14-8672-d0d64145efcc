<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row align-items-center">
      <div class="col-4">
        <h6>Work Orders</h6>
      </div>
      <div class="col-8 text-end">
        <button
          nbButton
          [status]="deleteAllWorkOrder ? 'danger' : 'primary'"
          size="medium"
          type="button"
          class="m-1"
          (click)="createWO()"
          *ngIf="isWOGenerated || deleteAllWorkOrder"
          [disabled]="isWOBeingEdited"
        >
          <span *ngIf="!deleteAllWorkOrder">Generate Work Orders</span>
          <span *ngIf="deleteAllWorkOrder">Generate Replacement Work Order</span>
        </button>
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          class="m-1"
          (click)="createWO(true)"
          *ngIf="!isWOGenerated && IsMLT"
          [disabled]="isWOBeingEdited"
        >
          <em class="fa fa-plus-circle pe-2 icon_btn"></em>
          Add New Work Order
        </button>
        <button
          class="linear-mode-button m-1"
          nbButton
          status="danger"
          size="medium"
          (click)="onDelete()"
          *ngIf="!isWOGenerated && !deleteAllWorkOrder"
          [disabled]="isWOBeingEdited"
        >
          <em class="fa fa-trash pe-2 icon_btn"></em>
          Cancel All Work Order
        </button>
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          class="m-1"
          (click)="restoreAllWO()"
          *ngIf="deleteAllWorkOrder"
          [disabled]="isWOBeingEdited"
        >
          Restore Deleted Work Order
        </button>
        <button nbButton status="basic" size="medium" class="linear-mode-button float-end m-1" (click)="onBack()">Back</button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="form-control-group">
      <div class="col-12 table-responsive table-card-view">
        <table class="table table-bordered table-header-rotated">
          <thead>
            <tr>
              <th class="text-center" id="customerName">Customer Name</th>
              <th class="text-center" id="portfolioName">Portfolio Name</th>
              <th class="text-center" id="siteName">Site Name</th>
              <th class="text-center" id="assesType">Assessment Type</th>
              <th class="text-center" id="freType">Frequency Type</th>
              <th class="text-end" id="totalWO">Total WO</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-title="Customer Name" class="text-center">
                <a [routerLink]="['/entities/customers/edit/' + customerId]"> {{ customerNames }}</a>
              </td>
              <td data-title="Portfolio Name" class="text-center">
                <a [routerLink]="['/entities/portfolios/edit/' + portfolioId]">
                  {{ portfolioNames }}
                </a>
              </td>
              <td data-title="Site Name" class="text-center">
                <a [routerLink]="['/entities/sites/view/' + siteId]">
                  {{ siteNames }}
                </a>
              </td>
              <td data-title="Assessment Type" class="text-center">
                <label class="table-label">{{ assessmentType }}</label>
              </td>
              <td data-title="Frequency Type" class="text-center">
                <label class="table-label">{{ frequncyType }}</label>
              </td>
              <td data-title="Total WO" class="text-end">
                <label class="table-label">{{ totalWO }}</label>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <nb-accordion *ngFor="let workOrderData of workOrderDatas; index as i" class="mb-2">
      <nb-accordion-item [expanded]="workOrderData.expanded" (collapsedChange)="workorderAccordionChange($event, workOrderData)">
        <nb-accordion-item-header class="bg-light" [id]="workOrderData.workOrderNumber">
          <div class="col-md-6">
            <h6>{{ workOrderData.workOrderNumber }}</h6>
          </div>
          <div class="col-md-6 me-2 text-end">
            <button
              *ngIf="!workOrderData.isDeleted"
              nbButton
              status="danger"
              size="small"
              type="submit"
              class="linear-mode-button me-4 delete_icon"
              (click)="DeleteWO(workOrderData); $event.stopPropagation()"
              nbTooltip="Delete Work Order"
              nbTooltipPlacement="top"
              nbTooltipStatus="danger"
              [disabled]="isWOBeingEdited"
            >
              <em class="fa fa-trash p-2 icon_btn"></em>
            </button>
            <button
              *ngIf="workOrderData.isDeleted && !deleteAllWorkOrder"
              nbButton
              status="primary"
              size="small"
              type="submit"
              class="linear-mode-button me-2 delete_icon"
              (click)="RestoreWO(workOrderData.id); $event.stopPropagation()"
              nbTooltip="Restore Work Order"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              [disabled]="isWOBeingEdited"
            >
              <em class="fa fa-undo p-2 icon_btn"></em>
            </button>
            <button
              *ngIf="workOrderData.isDeleted && !deleteAllWorkOrder"
              nbButton
              status="primary"
              size="small"
              type="submit"
              class="linear-mode-button me-4 delete_icon"
              (click)="RecreateWO(workOrderData.id); $event.stopPropagation()"
              nbTooltip="Recreate Work Order"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              [disabled]="isWOBeingEdited"
            >
              <em class="fa fa-plus-circle p-2 icon_btn"></em>
            </button>
          </div>
        </nb-accordion-item-header>
        <nb-accordion-item-body>
          <div class="row">
            <div class="col-md-6" *ngIf="assessmentType !== 'Performance Report' && assessmentType !== 'Vegetation'">
              <div class="form-group row mt-1">
                <div class="col-12">
                  <label class="fw-bold">Work Order Data</label>
                </div>
              </div>
              <div class="form-group row mt-3">
                <div class="col-md-12" [ngClass]="{ 'col-xl-6': workOrders.assementType === 'TRQ' }">
                  <label class="label" for="input-assesmentType1">Assessment Type</label><br />
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [ngModel]="assessmentType"
                    disabled="true"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                    name="assesmentType"
                    id="input-assesmentType1"
                  />
                </div>
                <div
                  class="col-md-12"
                  *ngIf="workOrders.assementType === 'TRQ'"
                  class=""
                  [ngClass]="{ 'col-xl-6 mt-xl-0 mt-2': workOrders.assementType === 'TRQ' }"
                >
                  <label class="label" for="input-fieldTech">Zone Selection</label>
                  <ng-select
                    name="Zone Selection"
                    [items]="zoneListData"
                    [multiple]="true"
                    bindLabel="zoneName"
                    bindValue="id"
                    [(ngModel)]="workOrderData.zoneIds"
                    notFoundText="No Zones Found"
                    placeholder="Select Zones"
                    [closeOnSelect]="false"
                    [disabled]="disabled || isWOBeingEdited || workOrderData.isDeleted"
                    appendTo="body"
                  >
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.zoneName }}
                    </ng-template>
                    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                      <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                        <span class="ng-value-label">{{ item.zoneName }}</span>
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                      </div>
                      <div class="ng-value" *ngIf="items.length > 2">
                        <span class="ng-value-label">+{{ items.length - 2 }} </span>
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12" [ngClass]="{ 'col-xl-6': workOrders.assementType === 'TRQ' }">
                  <label class="label" for="input-tentativeMonth">Tentative Month</label>
                  <ng-select
                    [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                    fullWidth
                    id="tentativeMonth"
                    name="tentativeMonth"
                    required
                    [clearable]="false"
                    [(ngModel)]="workOrderData.tentativeMonth"
                    #tentativeMonth="ngModel"
                    size="large"
                    appendTo="body"
                    placeholder="Select Tentative Month"
                  >
                    <ng-option *ngFor="let month of tentativeMonthList" [value]="month.value">
                      {{ month?.text }}
                    </ng-option>
                  </ng-select>
                </div>
                <div
                  class="col-md-12"
                  *ngIf="workOrders.assementType === 'TRQ'"
                  class=""
                  [ngClass]="{ 'col-xl-6 mt-xl-0 mt-2': workOrders.assementType === 'TRQ' }"
                >
                  <label class="label" for="input-failureRate">Failure Rate</label><br />
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [ngModel]="workOrderData.failureRate ? workOrderData.failureRate + '%' : '-'"
                    disabled="true"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                    name="failureRate"
                    id="input-failureRate"
                  />
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-dateScheduled">Date Scheduled</label>
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [nbDatepicker]="dateScheduled"
                    [(ngModel)]="workOrderData.dateScheduled"
                    name="dateScheduled"
                    #dateScheduledModel="ngModel"
                    id="input-dateScheduled"
                    readonly
                    autocomplete="off"
                    (ngModelChange)="scheduledDateChanges(workOrderData)"
                    [disabled]="workOrderData.rescheduleCount || workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.rescheduleCount || workOrderData.isDeleted }"
                  />
                  <nb-datepicker #dateScheduled></nb-datepicker>
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-rescheduleDate">Re-Scheduled Date </label>
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [nbDatepicker]="rescheduleDate"
                    [(ngModel)]="workOrderData.rescheduleDate"
                    name="rescheduleDate"
                    id="input-rescheduleDate"
                    readonly
                    autocomplete="off"
                    [attr.disabled]="true"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  />
                  <nb-datepicker #rescheduleDate></nb-datepicker>
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-rescheduleDate">Re-Scheduled Count </label>
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [(ngModel)]="workOrderData.rescheduleCount"
                    name="rescheduleDate"
                    id="input-rescheduleDate"
                    readonly
                    autocomplete="off"
                    [attr.disabled]="true"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  />
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-dtPerform">Work Started Date</label>
                  <!-- TODO Will me remove once verify -->
                  <!-- <label class="label" *ngIf="assessmentType !== 'Site Visit'" for="input-dtPerform">Work Started Date</label>
                  <div
                    *ngIf="
                      assessmentType === 'Site Visit' &&
                      (workOrderData?.datePerformeds?.length !== 0 || workOrderData.datePerformeds !== null)
                    "
                  >
                    <span
                      *ngFor="let item of workOrderData.datePerformeds"
                      class="badge"
                      style="padding: 6px; background-color: #eaeef4 !important; margin: 2px"
                      >{{ item | date : fullDateFormat }}
                    </span>
                  </div>
                  <div
                    *ngIf="
                      assessmentType === 'Site Visit' &&
                      (workOrderData?.datePerformeds?.length === 0 || workOrderData?.datePerformeds === null) &&
                      workOrderData.datePerformed === null
                    "
                  >
                    <input
                      nbInput
                      fullWidth
                      class="form-control"
                      [nbDatepicker]="dtPerform"
                      [(ngModel)]="workOrderData.datePerformed"
                      name="dtPerform"
                      id="input-dtPerform"
                      readonly
                      autocomplete="off"
                    />
                    <nb-datepicker #dtPerform></nb-datepicker>
                  </div>
                  <div *ngIf="assessmentType === 'Site Visit' && workOrderData.datePerformed !== null">
                    <input
                      nbInput
                      fullWidth
                      class="form-control"
                      [nbDatepicker]="dtPerform"
                      [(ngModel)]="workOrderData.datePerformed"
                      name="dtPerform"
                      id="input-dtPerform"
                      readonly
                      autocomplete="off"
                    />
                    <nb-datepicker #dtPerform></nb-datepicker>
                  </div> -->
                  <div>
                    <input
                      nbInput
                      fullWidth
                      class="form-control"
                      [nbDatepicker]="dtPerform"
                      [(ngModel)]="workOrderData.datePerformed"
                      name="dtPerform"
                      id="input-dtPerform"
                      readonly
                      autocomplete="off"
                      [disabled]="workOrderData.isDeleted"
                      [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                    />
                    <nb-datepicker #dtPerform></nb-datepicker>
                  </div>
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-workCompleteDate">Work Complete Date </label>
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [nbDatepicker]="workCompleteDate"
                    [(ngModel)]="workOrderData.workCompleteDateDisplay"
                    name="workCompleteDate"
                    id="input-workCompleteDate"
                    readonly
                    autocomplete="off"
                    [attr.disabled]="true"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  />
                  <nb-datepicker #workCompleteDate></nb-datepicker>
                </div>
              </div>

              <div class="form-group row mt-2" *ngIf="assessmentType !== 'Aerial Scan'">
                <div class="col-md-12" *ngIf="assessmentType !== 'Vegetation'">
                  <label class="label" for="input-fieldTech">Field Tech</label>
                  <ng-select
                    name="Field Tech"
                    [items]="primaryFieldtTechdata"
                    [multiple]="true"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="workOrderData.fieldTechs"
                    notFoundText="No Field Tech Found"
                    placeholder="Select Field Tech"
                    [closeOnSelect]="false"
                    [disabled]="disabled || isWOBeingEdited || workOrderData.isDeleted"
                    appendTo="body"
                  >
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                    </ng-template>
                    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                      <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                        <span class="ng-value-label">{{ item.name }}</span>
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                      </div>
                      <div class="ng-value" *ngIf="items.length > 2">
                        <span class="ng-value-label">+{{ items.length - 2 }} </span>
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
                <div class="col-md-12 mt-2" *ngIf="assessmentType === 'Vegetation'">
                  <label class="label" for="input-fieldTech">Contractor</label>
                  <ng-select
                    name="Field Tech"
                    [items]="fieldTechsdata"
                    (change)="workOrderData.fieldTechs = []; onSelectContractor($event, i)"
                    (clear)="workOrderData.fieldTechs = []"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="workOrderData.fieldTech"
                    notFoundText="No Contractor Found"
                    placeholder="Select Contractor"
                    [closeOnSelect]="false"
                    [disabled]="disabled || isWOBeingEdited || workOrderData.isDeleted"
                    appendTo="body"
                  >
                  </ng-select>
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-woStatus">Work Order Status</label>
                  <ng-select
                    [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                    fullWidth
                    id="woStatus"
                    name="woStatus"
                    required
                    [clearable]="false"
                    [(ngModel)]="workOrderData.woStatus"
                    #woStatus="ngModel"
                    size="large"
                    appendTo="body"
                    (change)="woStatusChanges()"
                  >
                    <ng-option
                      *ngFor="let workOrderS of workOrderStatus"
                      [value]="workOrderS.id"
                      [disabled]="getDisableField(workOrderS, workOrderData, i)"
                    >
                      {{ workOrderS?.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div *ngIf="assessmentType === 'Site Visit' || assessmentType === 'Medium Voltage PM'" class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-woStatus">JHA</label>
                  <ng-select
                    [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                    name="JHA"
                    [multiple]="true"
                    [items]="jhaDetail"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="workOrderData.jhaMap"
                    #jhaMap="ngModel"
                    notFoundText="No JHA Found"
                    placeholder="Select JHA"
                    [closeOnSelect]="false"
                    [clearable]="false"
                    appendTo="body"
                    (search)="onJHASearchFilter($event)"
                    (close)="filteredJHAIds = []"
                  >
                    <ng-template ng-header-tmp *ngIf="jhaDetail && jhaDetail?.length">
                      <button type="button" (click)="selectAndDeselectAllJHA(workOrderData, true)" class="btn btn-sm btn-primary">
                        Select all
                      </button>
                      <button type="button" (click)="selectAndDeselectAllJHA(workOrderData, false)" class="btn btn-sm btn-primary ms-1">
                        Unselect all
                      </button>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" /> {{ item.name }}
                    </ng-template>
                    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                      <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                        <span class="ng-value-label">{{ item.name }}</span>
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                      </div>
                      <div class="ng-value" *ngIf="items.length > 1">
                        <span class="ng-value-label">+{{ items.length - 1 }} </span>
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
            </div>

            <div class="col-md-6" *ngIf="assessmentType !== 'Vegetation' && assessmentType !== 'Performance Report'">
              <div class="form-group row mt-1">
                <div class="col-12">
                  <label class="fw-bold">Report Detail</label>
                </div>
              </div>
              <div class="form-group row mt-3" *ngIf="assessmentType !== 'Performance Report'">
                <div class="col-md-12">
                  <label class="label" for="input-dueDate">Due Date</label>
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [nbDatepicker]="dueDate"
                    [(ngModel)]="workOrderData.dueDate"
                    name="dueDate"
                    id="input-dueDate"
                    readonly
                    autocomplete="off"
                    [disabled]="workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  />
                  <nb-datepicker #dueDate></nb-datepicker>
                </div>
              </div>
              <div class="form-group row mt-2">
                <div class="col-md-12">
                  <label class="label" for="input-tentativeMonth">Report Complete Date </label>
                  <div class="report-complete-date">
                    <input
                      type="text"
                      class="form-control input-full-width size-medium status-basic shape-rectangle"
                      nbInput
                      disabled
                      readonly
                      [value]="workOrderData.reportCompleteDate | date : fullDateFormat"
                    />
                  </div>
                </div>
              </div>
              <div class="form-group row mt-2" *ngIf="assessmentType === 'Aerial Scan'">
                <div class="col-md-12">
                  <label class="label" for="input-scanStatus">Scan Type</label>
                  <ng-select
                    [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                    fullWidth
                    id="scanStatus"
                    name="scanStatus"
                    required
                    [clearable]="false"
                    [(ngModel)]="workOrderData.scanType"
                    #scanStatus="ngModel"
                    size="large"
                    appendTo="body"
                  >
                    <ng-option *ngFor="let scanTypeS of scanTypeStatus" [value]="scanTypeS.value">
                      {{ scanTypeS?.text }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="form-group row mt-2" *ngIf="assessmentType !== 'Performance Report'">
                <div class="col-md-12" *ngIf="reportAutherdata.length > 0">
                  <label class="label" for="input-reportAuther">Report Author</label>
                  <ng-select
                    name="Report Author"
                    [items]="reportAutherdata"
                    [multiple]="true"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="workOrderData.reportAuthors"
                    notFoundText="No Report Author Found"
                    placeholder="Select Report Author"
                    [closeOnSelect]="false"
                    [disabled]="disabled || isWOBeingEdited || workOrderData.isDeleted"
                    appendTo="body"
                  >
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                    </ng-template>
                    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                      <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                        <span class="ng-value-label">{{ item.name }}</span>
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                      </div>
                      <div class="ng-value" *ngIf="items.length > 2">
                        <span class="ng-value-label">+{{ items.length - 2 }} </span>
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
              <div class="form-group row mt-2" *ngIf="assessmentType !== 'Performance Report'">
                <div class="col-md-12">
                  <label class="label" for="input-notes">Notes</label>
                  <textarea
                    nbInput
                    fullWidth
                    [(ngModel)]="workOrderData.notes"
                    #notes="ngModel"
                    spellcheck="true"
                    contenteditable="true"
                    name="notes"
                    id="input-notes"
                    class="form-control"
                    [disabled]="workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  ></textarea>
                </div>
              </div>
              <div class="form-group row mt-2" *ngIf="assessmentType === 'Site Visit'">
                <div class="col-md-12">
                  <label class="label" for="input-scope">Additional Scope</label>
                  <textarea
                    nbInput
                    fullWidth
                    [(ngModel)]="workOrderData.additionalScope"
                    #additionalScope="ngModel"
                    spellcheck="true"
                    contenteditable="true"
                    name="additionalScope"
                    id="input-additionalScope"
                    class="form-control"
                    [disabled]="workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  ></textarea>
                </div>
              </div>
              <div class="form-group m-form__group row mt-2" *ngIf="assessmentType === 'Site Visit'">
                <div class="col-md-12">
                  <label class="label" for="input-photoLink">Photos Link</label>
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [(ngModel)]="workOrderData.photosLink"
                    name="dueDate"
                    id="input-photoLink"
                    autocomplete="off"
                    (change)="checkPhotosLinkURL(workOrderData, i)"
                    [disabled]="workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  />
                  <small class="danger-text text-danger" *ngIf="workOrderData.inValidUrl">Invalid photos link URL</small>
                </div>
              </div>
              <div class="form-group row mt-3">
                <div class="mb-2" [ngClass]="assessmentType === 'Medium Voltage PM' ? 'col-md-6' : 'col-md-12'">
                  <label class="label" for="input-pdfReportLink">Report Link</label>
                  <div class="form-control-group reports-list appFilter">
                    <div class="table-responsive" id="fixed-table">
                      <table class="table table-hover table-bordered" aria-describedby="Work order pdf">
                        <thead>
                          <tr>
                            <th id="reportName">Report Name</th>
                            <th id="uploaded">Uploaded Date</th>
                            <th id="actions" class="text-center">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let report of workOrderData?.reportDocuments; let index = index">
                            <td class="td-custom-width">{{ report?.reportLabel }}</td>
                            <td class="td-custom-width">{{ report?.uploadedOn | date : 'MM/dd/yyyy' }}</td>
                            <td class="text-center">
                              <a
                                class="text-secondary icon-adjust-inline"
                                (click)="downloadReport(report?.fileId, report?.originalFileName)"
                              >
                                <em
                                  class="fa fa-download"
                                  nbTooltip="Download"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="text-secondary"
                                ></em>
                              </a>
                              <a class="text-primary icon-adjust-inline" (click)="copyText(report?.fileUrl)">
                                <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                              </a>
                              <a
                                class="text-danger icon-adjust-inline"
                                (click)="deleteReport(report.fileId, workOrderData?.reportDocuments, index, 'reportDocuments')"
                                *ngIf="selectedWoStatus[i] !== 1 || workOrderData?.reportDocuments?.length !== 1"
                              >
                                <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                              </a>
                            </td>
                          </tr>
                          <tr>
                            <td colspan="3" *ngIf="!workOrderData?.reportDocuments?.length" class="no-record text-center">
                              No Report Found
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 mb-2 mt-4" *ngIf="assessmentType === 'Medium Voltage PM'">
                  <div class="form-control-group reports-list appFilter">
                    <div class="table-responsive" id="fixed-table">
                      <table class="table table-hover table-bordered" aria-describedby="Work order pdf">
                        <thead>
                          <tr>
                            <th id="reportName">Oil/Electrical Test Result</th>
                            <th id="uploaded">Uploaded Date</th>
                            <th id="actions" class="text-center">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let report of oilElectricalReportDocuments; let index = index">
                            <td class="td-custom-width">{{ report?.reportLabel }}</td>
                            <td class="td-custom-width">{{ report?.uploadedOn | date : 'MM/dd/yyyy' }}</td>
                            <td class="text-center">
                              <a
                                class="text-secondary icon-adjust-inline"
                                (click)="
                                  downloadReportOilElectrical(report?.fileId, report?.originalFileName.split('.').slice(0, -1).join('.'))
                                "
                              >
                                <em
                                  class="fa fa-download"
                                  nbTooltip="Download"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="text-secondary"
                                ></em>
                              </a>
                              <a class="text-primary icon-adjust-inline" (click)="copyText(report?.fileUrl)">
                                <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                              </a>
                              <a
                                class="text-danger icon-adjust-inline"
                                (click)="deleteReport(report.fileId, oilElectricalReportDocuments, index, 'oilElectricalReport')"
                                *ngIf="selectedWoStatus[i] !== 1 || this.oilElectricalReportDocuments?.length !== 1"
                              >
                                <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                              </a>
                            </td>
                          </tr>
                          <tr>
                            <td colspan="3" *ngIf="!oilElectricalReportDocuments?.length" class="no-record text-center">No Report Found</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="form-group row mt-3" *ngIf="assessmentType === 'Inverter PM'">
                <div class="mb-2" [ngClass]="assessmentType === 'Medium Voltage PM' ? 'col-md-6' : 'col-md-12'">
                  <label class="label" for="input-pdfReportLink">OEM Document Link</label>
                  <div class="form-control-group reports-list appFilter">
                    <div class="table-responsive" id="fixed-table">
                      <table class="table table-hover table-bordered" aria-describedby="Work order pdf">
                        <thead>
                          <tr>
                            <th id="reportName">URL Name</th>
                            <th id="uploaded">Uploaded Date</th>
                            <th id="actions" class="text-center">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of workOrderData.listOfOEMDocument; let linkIndex = index">
                            <td class="td-custom-width">
                              <ng-container *ngIf="!item.isEditing; else editMode">
                                <a
                                  [href]="
                                    item.documentLink.startsWith('http://') || item.documentLink.startsWith('https://')
                                      ? item.documentLink
                                      : 'http://' + item.documentLink
                                  "
                                  target="_blank"
                                >
                                  {{ item.documentLink.length > 20 ? (item.documentLink | slice : 0 : 20) + '...' : item.documentLink }}
                                </a>
                              </ng-container>
                              <ng-template #editMode>
                                <input
                                  type="text"
                                  nbInput
                                  fullWidth
                                  [(ngModel)]="item.documentLink"
                                  class="form-control"
                                  pattern="^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:\d+)?(\/[^\s]*)?$"
                                  placeholder="Enter Report Link"
                                  #linkInput="ngModel"
                                  name="linkInput"
                                  [attr.aria-invalid]="linkInput.invalid && linkInput.touched ? true : null"
                                />
                                <ng-container *ngIf="linkInput.invalid && linkInput.touched">
                                  <p class="caption status-danger" *ngIf="linkInput.errors?.pattern">Link format is incorrect</p>
                                </ng-container>
                              </ng-template>
                            </td>
                            <td class="td-custom-width">
                              {{ item?.createdDate || (todayDate | date : 'MM/dd/yyyy') | date : 'MM/dd/yyyy' }}
                            </td>
                            <td class="text-center">
                              <span [ngClass]="{ 'disabled-cell': isWOBeingEdited || workOrderData.isDeleted }">
                                <a class="text-primary icon-adjust-inline" *ngIf="!item.isEditing" (click)="item.isEditing = true">
                                  <em class="fa fa-edit" nbTooltip="Edit Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                                </a>
                              </span>
                              <span
                                [ngClass]="{
                                  'disabled-cell': (item.isEditing && isLinkInvalid(item)) || isWOBeingEdited || workOrderData.isDeleted
                                }"
                              >
                                <a class="text-success icon-adjust-inline" *ngIf="item.isEditing" (click)="item.isEditing = false">
                                  <em class="fa fa-save" nbTooltip="Save" nbTooltipPlacement="top" nbTooltipStatus="success"></em>
                                </a>
                              </span>
                              <span [ngClass]="{ 'disabled-cell': isWOBeingEdited || workOrderData.isDeleted }">
                                <a class="text-danger icon-adjust-inline" (click)="deletelink(i, linkIndex, workOrderData.id, item.id)">
                                  <em class="fa fa-trash text-danger px-2 pointerReportLink"></em>
                                </a>
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td colspan="3" *ngIf="!workOrderData?.listOfOEMDocument?.length" class="no-record text-center">
                              No Document Link Found
                            </td>
                          </tr>
                          <span
                            class="text-primary pointerReportLink"
                            *ngIf="!isWOBeingEdited && !workOrderData.isDeleted"
                            [ngClass]="{ 'disabled-cell': isWOBeingEdited || workOrderData.isDeleted }"
                            (click)="addLink(i)"
                            >+ Add OEM Document Link</span
                          >
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 mb-2 mt-4" *ngIf="assessmentType === 'Medium Voltage PM'">
                  <div class="form-control-group reports-list appFilter">
                    <div class="table-responsive" id="fixed-table">
                      <table class="table table-hover table-bordered" aria-describedby="Work order pdf">
                        <thead>
                          <tr>
                            <th id="reportName">Oil/Electrical Test Result</th>
                            <th id="uploaded">Uploaded Date</th>
                            <th id="actions" class="text-center">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let report of oilElectricalReportDocuments; let index = index">
                            <td class="td-custom-width">{{ report?.reportLabel }}</td>
                            <td class="td-custom-width">{{ report?.uploadedOn | date : 'MM/dd/yyyy' }}</td>
                            <td class="text-center">
                              <a
                                class="text-secondary icon-adjust-inline"
                                (click)="
                                  downloadReportOilElectrical(report?.fileId, report?.originalFileName.split('.').slice(0, -1).join('.'))
                                "
                              >
                                <em
                                  class="fa fa-download"
                                  nbTooltip="Download"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="text-secondary"
                                ></em>
                              </a>
                              <a class="text-primary icon-adjust-inline" (click)="copyText(report?.fileUrl)">
                                <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                              </a>
                              <a
                                class="text-danger icon-adjust-inline"
                                (click)="deleteReport(report.fileId, oilElectricalReportDocuments, index, 'oilElectricalReport')"
                                *ngIf="selectedWoStatus[i] !== 1 || this.oilElectricalReportDocuments?.length !== 1"
                              >
                                <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                              </a>
                            </td>
                          </tr>
                          <tr>
                            <td colspan="3" *ngIf="!oilElectricalReportDocuments?.length" class="no-record text-center">No Report Found</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="form-group row mt-3">
                <div [ngClass]="assessmentType === 'Medium Voltage PM' ? 'col-md-6' : 'col-md-12'">
                  <div class="row">
                    <ng-container *ngIf="assessmentType === 'Inverter PM' || assessmentType === 'Module Torque'">
                      <div
                        class="col-12"
                        [ngClass]="assessmentType === 'Inverter PM' || assessmentType === 'Module Torque' ? 'col-md-6' : 'col-md-12'"
                      >
                        <div
                          class="work-order-dropZone d-flex align-items-center justify-content-center"
                          (click)="
                            isWOBeingEdited
                              ? ''
                              : openTemplateModal(
                                  assessmentType === 'Module Torque' ? availableModuleTorqueForms : availableFormList,
                                  assessmentType === 'Module Torque'
                                    ? woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS
                                    : woTemplateOpenModelForFormConstant.AVAILABLE_FORM,
                                  workOrderData.id
                                )
                          "
                        >
                          <div>
                            <em class="fa fa-edit cursor-pointer text-primary iconimageupload mb-2"></em>
                            <h6 class="fw-bold mb-1">Forms</h6>
                          </div>
                        </div>
                        <div>
                          <button
                            *ngIf="workOrderData.qestFormCount > 0"
                            class="linear-mode-button w-100 mt-3"
                            nbButton
                            id="viewForms_{{ workOrderData.id }}"
                            status="primary"
                            size="small"
                            [disabled]="isWOBeingEdited || loading || workOrderData.isDeleted"
                            [ngClass]="{ 'cursor-blocked': isWOBeingEdited || loading || workOrderData.isDeleted }"
                            (click)="
                              openTemplateModal(
                                assessmentType === 'Module Torque' ? viewModuleTorqueFormsTemplate : viewFormsTemplate,
                                assessmentType === 'Module Torque'
                                  ? woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL
                                  : woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL,
                                workOrderData.id
                              )
                            "
                            type="button"
                          >
                            View Forms ({{ workOrderData.qestFormCount }})
                          </button>
                        </div>
                      </div>
                    </ng-container>
                    <div
                      class="col-12 col-md-6"
                      [ngClass]="assessmentType === 'Inverter PM' || assessmentType === 'Module Torque' ? 'col-md-6' : 'col-md-12'"
                    >
                      <div
                        class="work-order-dropZone"
                        ngFileDragDrop
                        (fileDropped)="getUpload($event, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = false))"
                      >
                        <input
                          type="file"
                          #file
                          accept=".doc,.docx,.pdf,.txt,.xls,.xlsx,.ppt,.pptx,.csv,.json,.rtf,.pvapx"
                          multiple
                          (change)="
                            getUpload($event.target.files, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = false))
                          "
                          [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                        />
                        <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
                        <h6 class="fw-bold mb-1">Upload PDF</h6>
                        <label style="text-transform: none; font-size: 13px;" class="fw-semibold ">OR Click to Browse </label>
                      </div>
                      <div *ngFor="let item of pdfFileList; let i = index" class="mt-2">
                        <span class="d-flex align-items-center" *ngIf="!item?.isOilElectricalReport">
                          <input
                            nbInput
                            class="form-control"
                            name="{{ 'fileName-' + i }}"
                            id="{{ 'fileName-' + i }}"
                            name="fileName"
                            #fileName="ngModel"
                            fullWidth
                            [(ngModel)]="item.reportLabel"
                            required
                          />
                          <em
                            (click)="removeFile(i)"
                            nbtooltip="Delete"
                            nbtooltipplacement="top"
                            nbtooltipstatus="text-danger"
                            aria-hidden="true"
                            class="fa fa-times-circle text-danger mx-2 pointer"
                          ></em>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6" *ngIf="assessmentType === 'Medium Voltage PM' && !workOrderData.isDeleted">
                  <div
                    class="work-order-dropZone"
                    ngFileDragDrop
                    (fileDropped)="getUpload($event, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = true))"
                  >
                    <input
                      type="file"
                      #file
                      multiple
                      accept=".doc,.docx,.pdf,.txt,.xls,.xlsx,.ppt,.pptx,.csv,.json,.rtf,.pvapx"
                      (change)="getUpload($event.target.files, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = true))"
                      [disabled]="isWOBeingEdited"
                    />
                    <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
                    <h6 class="fw-bold mb-1">Upload Report</h6>
                    <label style="text-transform: none; font-size: 13px;" class="fw-semibold">OR Click to Browse </label>
                  </div>
                  <div *ngFor="let item of pdfFileList; let i = index" class="mt-2">
                    <span class="d-flex align-items-center" *ngIf="item?.isOilElectricalReport">
                      <input
                        nbInput
                        class="form-control"
                        name="{{ 'fileName-' + i }}"
                        id="{{ 'fileName-' + i }}"
                        name="fileName"
                        #fileName="ngModel"
                        fullWidth
                        [(ngModel)]="item.reportLabel"
                        required
                      />
                      <em
                        (click)="removeFile(i)"
                        nbtooltip="Delete"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-danger"
                        aria-hidden="true"
                        class="fa fa-times-circle text-danger mx-2 pointer"
                      ></em>
                    </span>
                  </div>
                </div>
              </div> -->
              <!-- <div [ngClass]="assessmentType === 'Medium Voltage PM' ? 'col-md-6' : 'col-md-12'">
                <div class="row">
                  <ng-container *ngIf="assessmentType === 'Inverter PM' || assessmentType === 'Module Torque'">
                    <div
                      class="col-12"
                      [ngClass]="assessmentType === 'Inverter PM' || assessmentType === 'Module Torque' ? 'col-md-6' : 'col-md-12'"
                    > -->
              <div class="form-group row mt-3">
                <div [ngClass]="assessmentType === 'Medium Voltage PM' ? 'col-md-6' : 'col-md-12'">
                  <div class="row">
                    <ng-container *ngIf="assessmentType === 'Inverter PM' || assessmentType === 'Module Torque'">
                      <div
                        class="col-12 col-md-6 pb-3"
                        [ngClass]="{
                          'col-xl-4':
                            assessmentType !== 'Medium Voltage PM' &&
                            (assessmentType === 'Inverter PM' || assessmentType === 'Module Torque')
                        }"
                      >
                        <div
                          class="work-order-dropZone d-flex align-items-center justify-content-center"
                          (click)="
                            isWOBeingEdited || workOrderData.isDeleted
                              ? ''
                              : openTemplateModal(
                                  assessmentType === 'Module Torque' ? availableModuleTorqueForms : availableFormList,
                                  assessmentType === 'Module Torque'
                                    ? woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS
                                    : woTemplateOpenModelForFormConstant.AVAILABLE_FORM,
                                  workOrderData.id
                                )
                          "
                        >
                          <div>
                            <em class="fa fa-edit cursor-pointer text-primary iconimageupload mb-2"></em>
                            <h6 class="fw-bold mb-1">Forms</h6>
                          </div>
                        </div>
                        <div>
                          <button
                            *ngIf="workOrderData.qestFormCount > 0 || workOrderData.moduleTorqueCount > 0"
                            class="linear-mode-button w-100 mt-3"
                            nbButton
                            id="viewForms_{{ workOrderData.id }}"
                            status="primary"
                            size="small"
                            [disabled]="isWOBeingEdited || loading || workOrderData.isDeleted"
                            [ngClass]="{ 'cursor-blocked': isWOBeingEdited || loading || workOrderData.isDeleted }"
                            (click)="
                              isWOBeingEdited || workOrderData.isDeleted
                                ? ''
                                : openTemplateModal(
                                    assessmentType === 'Module Torque' ? viewModuleTorqueFormsTemplate : viewFormsTemplate,
                                    assessmentType === 'Module Torque'
                                      ? woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL
                                      : woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL,
                                    workOrderData.id
                                  )
                            "
                            type="button"
                          >
                            View Forms ({{
                              assessmentType === 'Module Torque' ? workOrderData.moduleTorqueCount : workOrderData.qestFormCount
                            }})
                          </button>
                        </div>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="assessmentType === 'Tracker Preventative Maintenance'">
                      <div
                        class="col-12 col-md-6 pb-3"
                        [ngClass]="{
                          'col-xl-4': assessmentType !== 'Medium Voltage PM' && assessmentType === 'Tracker Preventative Maintenance'
                        }"
                      >
                        <div
                          class="work-order-dropZone d-flex align-items-center justify-content-center"
                          (click)="
                            isWOBeingEdited || workOrderData.isDeleted
                              ? ''
                              : goToFormFillingPage(
                                  workOrderData.tpmFormDetails,
                                  pageOpenFromConstant.FILL_INITIAL_TPM,
                                  workOrderData.isTPMFormActive
                                )
                          "
                        >
                          <div>
                            <em class="fa fa-edit cursor-pointer text-primary iconimageupload mb-2"></em>
                            <h6 class="fw-bold mb-1">Forms</h6>
                          </div>
                        </div>
                        <div>
                          <button
                            *ngIf="workOrderData.tpmFormCount > 0"
                            class="linear-mode-button w-100 mt-3"
                            nbButton
                            id="viewTPMForms_{{ workOrderData.id }}"
                            status="primary"
                            size="small"
                            [disabled]="isWOBeingEdited || loading || workOrderData.isDeleted"
                            [ngClass]="{ 'cursor-blocked': isWOBeingEdited || loading || workOrderData.isDeleted }"
                            (click)="
                              openTemplateModal(
                                viewTPMFormsTemplate,
                                woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL,
                                workOrderData.id
                              )
                            "
                            type="button"
                          >
                            View Forms ({{ workOrderData.tpmFormCount }})
                          </button>
                        </div>
                      </div>
                    </ng-container>
                    <div
                      class="col-12 col-md-6 pb-3"
                      [ngClass]="{
                        'col-xl-4':
                          assessmentType !== 'Medium Voltage PM' &&
                          (assessmentType === 'Inverter PM' ||
                            assessmentType === 'Module Torque' ||
                            assessmentType === 'Tracker Preventative Maintenance'),
                        'col-md-12 col-xl-6': assessmentType === 'Medium Voltage PM'
                      }"
                    >
                      <div
                        class="work-order-dropZone d-flex align-items-center justify-content-center"
                        (click)="
                          isWOBeingEdited || workOrderData.isDeleted
                            ? ''
                            : goToFormFillingPage(
                                workOrderData.summaryReportDetails,
                                pageOpenFromConstant.FILL_INITIAL_SR,
                                workOrderData.isSummaryReportActive
                              )
                        "
                      >
                        <div>
                          <em class="fa fa-check cursor-pointer text-primary iconimageupload mb-2"></em>
                          <h6 class="fw-bold mb-1">Summary Report</h6>
                        </div>
                      </div>
                      <div>
                        <button
                          *ngIf="workOrderData.summaryReportCount > 0"
                          class="linear-mode-button w-100 mt-3"
                          nbButton
                          id="viewSummaryReports_{{ workOrderData.id }}"
                          status="primary"
                          size="small"
                          [disabled]="isWOBeingEdited || loading || workOrderData.isDeleted"
                          [ngClass]="{ 'cursor-blocked': isWOBeingEdited || loading || workOrderData.isDeleted }"
                          (click)="
                            openTemplateModal(
                              viewSummaryFormsTemplate,
                              woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL,
                              workOrderData.id
                            )
                          "
                          type="button"
                        >
                          View Reports ({{ workOrderData.summaryReportCount }})
                        </button>
                      </div>
                    </div>
                    <div
                      class="col-12 col-md-6 pb-3"
                      [ngClass]="{
                        'col-xl-4':
                          assessmentType !== 'Medium Voltage PM' &&
                          (assessmentType === 'Inverter PM' ||
                            assessmentType === 'Module Torque' ||
                            assessmentType === 'Tracker Preventative Maintenance'),
                        'col-md-12 col-xl-6': assessmentType === 'Medium Voltage PM'
                      }"
                    >
                      <div
                        class="work-order-dropZone"
                        ngFileDragDrop
                        (fileDropped)="getUpload($event, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = false))"
                      >
                        <input
                          type="file"
                          #file
                          accept=".doc,.docx,.pdf,.txt,.xls,.xlsx,.ppt,.pptx,.csv,.json,.rtf,.pvapx"
                          multiple
                          (change)="
                            getUpload($event.target.files, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = false))
                          "
                          [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                        />
                        <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
                        <h6 class="fw-bold mb-1">Upload PDF</h6>
                        <label style="text-transform: none; font-size: 13px" class="fw-semibold">OR Click to Browse </label>
                      </div>
                      <div *ngFor="let item of pdfFileList; let i = index" class="mt-2">
                        <span class="d-flex align-items-center" *ngIf="!item?.isOilElectricalReport">
                          <input
                            nbInput
                            class="form-control"
                            name="{{ 'fileName-' + i }}"
                            id="{{ 'fileName-' + i }}"
                            name="fileName"
                            #fileName="ngModel"
                            fullWidth
                            [(ngModel)]="item.reportLabel"
                            required
                          />
                          <em
                            (click)="removeFile(i)"
                            nbtooltip="Delete"
                            nbtooltipplacement="top"
                            nbtooltipstatus="text-danger"
                            aria-hidden="true"
                            class="fa fa-times-circle text-danger mx-2 pointer"
                          ></em>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6" *ngIf="assessmentType === 'Medium Voltage PM' && !workOrderData.isDeleted">
                  <div
                    class="work-order-dropZone"
                    ngFileDragDrop
                    (fileDropped)="getUpload($event, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = true))"
                  >
                    <input
                      type="file"
                      #file
                      multiple
                      accept=".doc,.docx,.pdf,.txt,.xls,.xlsx,.ppt,.pptx,.csv,.json,.rtf,.pvapx"
                      (change)="getUpload($event.target.files, workOrderData.id, workOrderData.reportId, (isOilElectricalReport = true))"
                      [disabled]="isWOBeingEdited"
                    />
                    <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
                    <h6 class="fw-bold mb-1">Upload Report</h6>
                    <label style="text-transform: none; font-size: 13px" class="fw-semibold">OR Click to Browse </label>
                  </div>
                  <div *ngFor="let item of pdfFileList; let i = index" class="mt-2">
                    <span class="d-flex align-items-center" *ngIf="item?.isOilElectricalReport">
                      <input
                        nbInput
                        class="form-control"
                        name="{{ 'fileName-' + i }}"
                        id="{{ 'fileName-' + i }}"
                        name="fileName"
                        #fileName="ngModel"
                        fullWidth
                        [(ngModel)]="item.reportLabel"
                        required
                      />
                      <em
                        (click)="removeFile(i)"
                        nbtooltip="Delete"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-danger"
                        aria-hidden="true"
                        class="fa fa-times-circle text-danger mx-2 pointer"
                      ></em>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-md-12" *ngIf="assessmentType === 'Vegetation'">
              <div class="form-group row mt-1">
                <div class="col-12">
                  <label class="fw-bold">Work Order Data</label>
                </div>
              </div>
              <div class="form-group row mt-3">
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label class="label" for="input-assesmentType">Assessment Type</label><br />
                      <input
                        nbInput
                        fullWidth
                        class="form-control"
                        [ngModel]="assessmentType"
                        disabled="true"
                        name="assesmentType"
                        id="input-assesmentType"
                      />
                    </div>
                    <div class="col-md-3">
                      <label class="label" for="input-poNumber">PO Number</label><br />
                      <input
                        nbInput
                        fullWidth
                        class="form-control"
                        [ngModel]="workOrderData.poNumber"
                        disabled="true"
                        [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                        name="poNumber"
                        id="input-poNumber"
                      />
                    </div>
                    <div class="col-md-3">
                      <label class="label" for="input-invoiceNumber">Invoice Number</label><br />
                      <input
                        nbInput
                        fullWidth
                        class="form-control"
                        [(ngModel)]="workOrderData.invoiceNumber"
                        [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                        [disabled]="workOrderData.isDeleted"
                        name="invoiceNumber"
                        id="input-invoiceNumber"
                        maxlength="50"
                      />
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <label class="label" for="input-dateScheduled">Date Scheduled</label>
                  <input
                    nbInput
                    fullWidth
                    class="form-control"
                    [nbDatepicker]="dateScheduled"
                    [(ngModel)]="workOrderData.dateScheduled"
                    [disabled]="workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                    name="dateScheduled"
                    id="input-dateScheduled"
                    readonly
                    (ngModelChange)="scheduledDateChanges(workOrderData)"
                    autocomplete="off"
                  />
                  <nb-datepicker #dateScheduled></nb-datepicker>
                </div>
              </div>
              <div class="form-group row mt-3">
                <div class="col-md-6">
                  <div class="row">
                    <div class="col-md-6">
                      <label class="label" for="input-dtPerform">Work Started Date</label>
                      <!-- TODO Will me remove once verify -->
                      <!-- <div *ngIf="assessmentType === 'Vegetation' && workOrderData.datePerformeds !== null">
                      <span
                        *ngFor="let item of workOrderData.datePerformeds"
                        class="badge"
                        style="padding: 6px; background-color: #eaeef4 !important; margin: 2px"
                        >{{ item | date : fullDateFormat }}
                      </span>
                    </div> -->
                      <input
                        *ngIf="assessmentType === 'Vegetation'"
                        nbInput
                        fullWidth
                        class="form-control"
                        [nbDatepicker]="dtPerform"
                        [(ngModel)]="workOrderData.datePerformed"
                        [disabled]="workOrderData.isDeleted"
                        [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                        name="dtPerform"
                        id="input-dtPerform"
                        readonly
                        autocomplete="off"
                      />
                      <nb-datepicker #dtPerform></nb-datepicker>
                    </div>
                    <div class="col-md-3">
                      <label class="label" for="input-Cost">Cost</label><br />
                      <input
                        nbInput
                        fullWidth
                        class="form-control"
                        [(ngModel)]="workOrderData.cost"
                        [ngClass]="{
                          'cursor-blocked':
                            workOrderData.isDeleted ||
                            currentWOStatus === 1 ||
                            !(
                              (checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER]) && currentWOStatus !== 1) ||
                              (checkAuthorisationsFn([roleType.PORTFOLIOMANAGER]) && currentWOStatus === 4)
                            )
                        }"
                        [disabled]="
                          workOrderData.isDeleted ||
                          currentWOStatus === 1 ||
                          !(
                            (checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER]) && currentWOStatus !== 1) ||
                            (checkAuthorisationsFn([roleType.PORTFOLIOMANAGER]) && currentWOStatus === 4)
                          )
                        "
                        name="Cost"
                        id="input-Cost"
                        maxlength="12"
                        currencyMask
                        [options]="{ allowNegative: false }"
                      />
                    </div>
                    <div class="col-md-3">
                      <label class="label" for="input-addOnCost">Cost Adjustment</label><br />
                      <input
                        nbInput
                        fullWidth
                        class="form-control"
                        [(ngModel)]="workOrderData.addOnCost"
                        [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                        [disabled]="workOrderData.isDeleted"
                        name="addOnCost"
                        id="input-addOnCost"
                        maxlength="12"
                        currencyMask
                      />
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <label class="label" for="input-fieldTech">Contractor</label>
                  <ng-select
                    name="Field Tech"
                    [items]="fieldTechsdata"
                    (change)="workOrderData.fieldTechs = []; onSelectContractor($event, i)"
                    (clear)="workOrderData.fieldTechs = []"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="workOrderData.fieldTech"
                    notFoundText="No Contractor Found"
                    placeholder="Select Contractor"
                    [closeOnSelect]="false"
                    [disabled]="disabled || isWOBeingEdited || workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                    appendTo="body"
                  >
                  </ng-select>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-6">
                  <label class="label" for="input-notes">Notes</label>
                  <textarea
                    nbInput
                    fullWidth
                    [(ngModel)]="workOrderData.notes"
                    #notes="ngModel"
                    spellcheck="true"
                    contenteditable="true"
                    name="notes"
                    id="input-notes"
                    class="form-control"
                    [disabled]="workOrderData.isDeleted"
                    [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                  ></textarea>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-6">
                  <label class="label" for="input-woStatus">Work Order Status</label>
                  <ng-select
                    [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                    fullWidth
                    id="woStatus"
                    name="woStatus"
                    required
                    [clearable]="false"
                    [(ngModel)]="workOrderData.woStatus"
                    #woStatus="ngModel"
                    size="large"
                    appendTo="body"
                  >
                    <ng-option
                      *ngFor="let workOrderS of workOrderStatus"
                      [value]="workOrderS.id"
                      [disabled]="getDisableField(workOrderS, workOrderData, i)"
                    >
                      {{ workOrderS?.name }}
                    </ng-option>
                  </ng-select>
                </div>
                <div class="col-md-6">
                  <label class="label" for="input-woStatus">JHA</label>
                  <ng-select
                    [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                    name="JHA"
                    [multiple]="true"
                    [items]="jhaDetail"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="workOrderData.jhaMap"
                    #jhaMap="ngModel"
                    notFoundText="No JHA Found"
                    placeholder="Select JHA"
                    [closeOnSelect]="false"
                    [clearable]="false"
                    appendTo="body"
                    (search)="onJHASearchFilter($event)"
                    (close)="filteredJHAIds = []"
                  >
                    <ng-template ng-header-tmp *ngIf="jhaDetail && jhaDetail?.length">
                      <button type="button" (click)="selectAndDeselectAllJHA(workOrderData, true)" class="btn btn-sm btn-primary">
                        Select all
                      </button>
                      <button type="button" (click)="selectAndDeselectAllJHA(workOrderData, false)" class="btn btn-sm btn-primary ms-1">
                        Unselect all
                      </button>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" /> {{ item.name }}
                    </ng-template>
                    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                      <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                        <span class="ng-value-label">{{ item.name }}</span>
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                      </div>
                      <div class="ng-value" *ngIf="items.length > 1">
                        <span class="ng-value-label">+{{ items.length - 1 }} </span>
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-12 mb-2">
                  <label class="label" for="input-pdfReportLink">PDF Report Link</label>
                  <div class="form-control-group reports-list appFilter">
                    <div class="table-responsive" id="fixed-table">
                      <table class="table table-hover table-bordered" aria-describedby="Work order pdf">
                        <thead>
                          <tr>
                            <th id="reportName">Report Name</th>
                            <th id="uploaded">Uploaded Date</th>
                            <th id="actions" class="text-center">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let report of workOrderData?.reportDocuments; let index = index">
                            <td class="td-custom-width">{{ report?.reportLabel }}</td>
                            <td class="td-custom-width">{{ report?.uploadedOn | date : 'MM/dd/yyyy' }}</td>
                            <td class="text-center">
                              <a
                                class="text-secondary listgrid-icon px-3"
                                (click)="downloadReport(report?.fileId, report?.originalFileName.split('.').slice(0, -1).join('.'))"
                              >
                                <em
                                  class="fa fa-download"
                                  nbTooltip="Download"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="text-secondary"
                                ></em>
                              </a>
                              <a class="px-2 text-primary listgrid-icon px-3" (click)="copyText(report?.fileUrl)">
                                <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                              </a>
                              <a
                                class="text-danger listgrid-icon px-2"
                                (click)="deleteReport(report.fileId, workOrderData?.reportDocuments, index)"
                                *ngIf="selectedWoStatus[i] !== 1 || workOrderData?.reportDocuments?.length !== 1"
                              >
                                <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                              </a>
                            </td>
                          </tr>
                          <tr>
                            <td colspan="3" *ngIf="!workOrderData?.reportDocuments?.length" class="no-record text-center">
                              No Report Found
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-6 mb-2">
                  <div
                    class="work-order-dropZone d-flex align-items-center justify-content-center"
                    (click)="
                      isWOBeingEdited || workOrderData.isDeleted
                        ? ''
                        : goToFormFillingPage(
                            workOrderData.summaryReportDetails,
                            pageOpenFromConstant.FILL_INITIAL_SR,
                            workOrderData.isSummaryReportActive
                          )
                    "
                  >
                    <div>
                      <em class="fa fa-check cursor-pointer text-primary iconimageupload mb-2"></em>
                      <h6 class="fw-bold mb-1">Summary Report</h6>
                    </div>
                  </div>
                  <div>
                    <button
                      *ngIf="workOrderData.summaryReportCount > 0"
                      class="linear-mode-button w-100 mt-3"
                      nbButton
                      id="viewSummaryReports_{{ workOrderData.id }}"
                      status="primary"
                      size="small"
                      [disabled]="isWOBeingEdited || loading"
                      (click)="
                        openTemplateModal(
                          viewSummaryFormsTemplate,
                          woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL,
                          workOrderData.id
                        )
                      "
                      type="button"
                    >
                      View Reports ({{ workOrderData.summaryReportCount }})
                    </button>
                  </div>
                </div>
                <div class="col-md-6 mb-2">
                  <div
                    class="work-order-dropZone"
                    ngFileDragDrop
                    (fileDropped)="getUpload($event, workOrderData.id, workOrderData.reportId)"
                  >
                    <input
                      type="file"
                      #file
                      accept=".doc,.docx,.pdf,.txt,.xls,.xlsx,.ppt,.pptx,.csv,.json,.rtf,.pvapx"
                      multiple
                      (change)="getUpload($event.target.files, workOrderData.id, workOrderData.reportId)"
                    />
                    <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
                    <h6 class="fw-bold mb-1">Upload Report</h6>
                    <label style="text-transform: none; font-size: 13px" class="fw-semibold">OR Click to Browse </label>
                  </div>
                  <div *ngFor="let item of pdfFileList; let i = index" class="mt-2">
                    <span class="d-flex align-items-center">
                      <input
                        nbInput
                        class="form-control"
                        name="{{ 'fileName-' + i }}"
                        id="{{ 'fileName-' + i }}"
                        name="fileName"
                        #fileName="ngModel"
                        fullWidth
                        [(ngModel)]="item.reportLabel"
                        required
                      />
                      <em
                        (click)="removeFile(i)"
                        nbtooltip="Delete"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-danger"
                        aria-hidden="true"
                        class="fa fa-times-circle text-danger mx-2 pointer"
                      ></em>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12" *ngIf="assessmentType === 'Performance Report'">
              <div class="form-group row mt-1">
                <div class="col-12">
                  <label class="fw-bold">Report Detail</label>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-12 mb-2">
                  <label class="label col-12" for="input-pdfReportLink">PDF Report Link</label>
                  <div class="form-control-group reports-list appFilter">
                    <div class="table-responsive" id="fixed-table">
                      <table class="table table-hover table-bordered" aria-describedby="Work order pdf">
                        <thead>
                          <tr>
                            <th id="reportName">Report Name</th>
                            <th id="uploaded">Uploaded Date</th>
                            <th id="actions" class="text-center">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let report of workOrderData?.reportDocuments; let index = index">
                            <td class="td-custom-width">{{ report?.reportLabel }}</td>
                            <td class="td-custom-width">{{ report?.uploadedOn | date : 'MM/dd/yyyy' }}</td>
                            <td class="text-center">
                              <a
                                class="text-secondary listgrid-icon px-3"
                                (click)="downloadReport(report?.fileId, report?.originalFileName.split('.').slice(0, -1).join('.'))"
                              >
                                <em
                                  class="fa fa-download"
                                  nbTooltip="Download"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="text-secondary"
                                ></em>
                              </a>
                              <a class="px-2 text-primary listgrid-icon px-3" (click)="copyText(report?.fileUrl)">
                                <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                              </a>
                              <a
                                class="text-danger listgrid-icon px-2"
                                (click)="deleteReport(report.fileId, workOrderData?.reportDocuments, index, 'reportDocuments', i)"
                                *ngIf="workOrderDatas[i].woStatus !== 1 || workOrderData?.reportDocuments?.length !== 1"
                              >
                                <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                              </a>
                            </td>
                          </tr>
                          <tr>
                            <td colspan="3" *ngIf="!workOrderData?.reportDocuments?.length" class="no-record text-center">
                              No Report Found
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-6 mb-2">
                  <div
                    class="work-order-dropZone d-flex align-items-center justify-content-center"
                    (click)="
                      isWOBeingEdited || workOrderData.isDeleted
                        ? ''
                        : goToFormFillingPage(
                            workOrderData.summaryReportDetails,
                            pageOpenFromConstant.FILL_INITIAL_SR,
                            workOrderData.isSummaryReportActive
                          )
                    "
                  >
                    <div>
                      <em class="fa fa-check cursor-pointer text-primary iconimageupload mb-2"></em>
                      <h6 class="fw-bold mb-1">Summary Report</h6>
                    </div>
                  </div>
                  <div>
                    <button
                      *ngIf="workOrderData.summaryReportCount > 0"
                      class="linear-mode-button w-100 mt-3"
                      nbButton
                      id="viewSummaryReports_{{ workOrderData.id }}"
                      status="primary"
                      size="small"
                      [disabled]="isWOBeingEdited || loading"
                      (click)="
                        openTemplateModal(
                          viewSummaryFormsTemplate,
                          woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL,
                          workOrderData.id
                        )
                      "
                      type="button"
                    >
                      View Reports ({{ workOrderData.summaryReportCount }})
                    </button>
                  </div>
                </div>
                <div class="col-md-6 mb-2">
                  <div
                    class="work-order-dropZone"
                    ngFileDragDrop
                    (fileDropped)="getUpload($event, workOrderData.id, workOrderData.reportId)"
                  >
                    <input
                      type="file"
                      #file
                      accept=".doc,.docx,.pdf,.txt,.xls,.xlsx,.ppt,.pptx,.csv,.json,.rtf,.pvapx"
                      multiple
                      (change)="getUpload($event.target.files, workOrderData.id, workOrderData.reportId)"
                    />
                    <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
                    <h6 class="fw-bold mb-1">Upload Report</h6>
                    <label style="text-transform: none; font-size: 13px" class="fw-semibold">OR Click to Browse </label>
                  </div>
                  <div *ngFor="let item of pdfFileList; let i = index" class="mt-2">
                    <span class="d-flex align-items-center">
                      <input
                        nbInput
                        class="form-control"
                        name="{{ 'fileName-' + i }}"
                        id="{{ 'fileName-' + i }}"
                        name="fileName"
                        #fileName="ngModel"
                        fullWidth
                        [(ngModel)]="item.reportLabel"
                        required
                      />
                      <em
                        (click)="removeFile(i)"
                        nbtooltip="Delete"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-danger"
                        aria-hidden="true"
                        class="fa fa-times-circle text-danger mx-2 pointer"
                      ></em>
                    </span>
                  </div>
                </div>
              </div>
              <div class="form-group row mt-4">
                <div class="col-md-6">
                  <label class="label" for="input-woStatus">Work Order Status</label>
                  <ng-select
                    [disabled]="isWOBeingEdited || workOrderData.isDeleted"
                    fullWidth
                    id="woStatus"
                    name="woStatus"
                    required
                    [clearable]="false"
                    [(ngModel)]="workOrderData.woStatus"
                    #woStatus="ngModel"
                    size="large"
                    appendTo="body"
                  >
                    <ng-option
                      *ngFor="let workOrderS of workOrderStatus"
                      [value]="workOrderS.id"
                      [disabled]="getDisableField(workOrderS, workOrderData, i)"
                    >
                      {{ workOrderS?.name }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>
          <div class="gallery-slider">
            <div class="borders py-3">
              <div>
                <nb-tabset fullWidthv>
                  <nb-tab tabTitle="Photos" [nbSpinner]="exclusionsLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
                    <div class="photo-carousel">
                      <div class="d-flex align-items-start justify-content-end mb-3">
                        <button
                          nbButton
                          (click)="openDropBoxImageGallery(workOrderData)"
                          status="primary"
                          size="small"
                          id="DropBoxImageGallery"
                          class="me-2"
                          [disabled]="workOrderData.imageGalleryList.totalCount === 0 || workOrderData.isDeleted"
                          [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted || workOrderData.imageGalleryList.totalCount === 0 }"
                        >
                          <span class="d-flex"
                            ><em class="pi pi-images me-2"></em>GALLERY ({{ workOrderData.imageGalleryList.totalCount || 0 }})</span
                          >
                        </button>
                        <label
                          for="fileImageUpload_{{ workOrderData.id }}"
                          class="file-upload-btn float-end me-2"
                          *ngIf="userRole !== 'customer'"
                          [ngClass]="{ 'cursor-blocked': workOrderData.isDeleted }"
                        >
                          <span class="d-none d-lg-inline-block">
                            <em class="fa fa-upload"></em>
                            Upload
                          </span>
                          <em class="d-inline-block d-lg-none fa fa fa-upload"></em>
                          <input
                            id="fileImageUpload_{{ workOrderData.id }}"
                            type="file"
                            #imageInput
                            accept="image/*"
                            multiple
                            style="display: none"
                            [disabled]="workOrderData.isDeleted"
                            (change)="uploadFilesToGallery($event, workOrderData, 'image')"
                          />
                        </label>
                      </div>
                      <div class="main-image-gallery" *ngIf="workOrderData.imageGalleryList?.imageGallery?.length">
                        <p-carousel
                          [value]="workOrderData.imageGalleryList?.imageGallery"
                          [responsiveOptions]="responsiveOptions"
                          [showNavigators]="true"
                          [showIndicators]="false"
                          [numVisible]="8"
                          [numScroll]="1"
                          [circular]="false"
                        >
                          <ng-template pTemplate="item" let-item>
                            <div class="mb-3 photo-images grid grid-nogutter justify-content-center">
                              <img
                                appImageLoaderDir
                                [src]="item.thumbnailUrl ? item.thumbnailUrl : item.fileUrl"
                                [alt]="'image'"
                                onError="src='assets/images/no-image-found.jpg'"
                                (click)="openDropBoxImageGallery(workOrderData, item.id)"
                              />
                            </div>
                          </ng-template>
                        </p-carousel>
                      </div>
                      <div class="text-center" *ngIf="!workOrderData.imageGalleryList?.imageGallery?.length">No Data Found</div>
                    </div>
                  </nb-tab>
                  <nb-tab
                    tabTitle="Videos"
                    [nbSpinner]="exclusionsLoading"
                    nbSpinnerStatus="primary"
                    nbSpinnerSize="large"
                    *ngIf="workOrderData.assesmentType === 'AS'"
                  >
                    <div class="video-tab row">
                      <div class="text-end mb-3">
                        <label
                          for="fileVideoUpload{{ workOrderData.id }}"
                          class="file-upload-btn float-end me-2"
                          *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER, roleType.FIELDTECH])"
                        >
                          <span class="d-none d-lg-inline-block">
                            <em class="fa fa-upload"></em>
                            Upload
                          </span>
                          <em class="d-inline-block d-lg-none fa fa fa-upload"></em>
                          <input
                            id="fileVideoUpload{{ workOrderData.id }}"
                            #videoInput
                            type="file"
                            accept="video/*, .ats"
                            multiple
                            style="display: none"
                            (change)="uploadFilesToGallery($event, workOrderData, 'video')"
                          />
                        </label>
                      </div>
                      <div *ngIf="workOrderData.videoGalleryList?.videoGallery?.length">
                        <div
                          id="fixed-table"
                          setTableHeight
                          [isFilterDisplay]="isFilterDisplay"
                          class="col-12 table-responsive table-card-view"
                        >
                          <table class="table table-hover table-borderless" aria-describedby="Ticket List">
                            <thead>
                              <tr>
                                <th class="text-center">Video Name</th>
                                <th class="text-center">Uploaded By</th>
                                <th class="text-center">Date</th>
                                <th class="text-center">Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              <ng-container>
                                <tr
                                  *ngFor="
                                    let video of workOrderData.videoGalleryList?.videoGallery
                                      | paginate
                                        : {
                                            itemsPerPage: videoPaginationParams.itemsCount,
                                            currentPage: videoPaginationParams.currentPage,
                                            totalItems: workOrderData.videoGalleryList?.totalCount
                                          }
                                  "
                                >
                                  <td data-title="Video Name" class="text-center">{{ video.fileName }}</td>
                                  <td data-title="Uploaded By" class="text-center">{{ video.createdBy }}</td>
                                  <td data-title="Date" class="text-center">{{ video.createdDate | date : fullDateFormat }}</td>
                                  <td data-title="Action" class="text-end">
                                    <div class="d-flex align-items-center justify-content-center">
                                      <em
                                        class="pi pi-download text-primary me-3"
                                        (click)="downloadVideoFile(video.id, video.fileName)"
                                      ></em>
                                      <em
                                        *ngIf="userRole !== 'customer'"
                                        class="pi pi-trash text-danger"
                                        (click)="deleteGalleryItem(workOrderData.id, video.id)"
                                      ></em>
                                    </div>
                                  </td>
                                </tr>
                              </ng-container>
                            </tbody>
                          </table>
                        </div>
                        <div class="mt-2 d-md-flex align-items-center">
                          <div class="d-flex align-items-center">
                            <label class="mb-0">Items per page: </label>
                            <ng-select
                              class="ms-2"
                              [(ngModel)]="videoPaginationParams.pageSize"
                              [clearable]="false"
                              [searchable]="false"
                              (change)="onChangeSize(workOrderData.id)"
                              appendTo="body"
                            >
                              <ng-option value="5">5</ng-option>
                              <ng-option value="10">10</ng-option>
                              <ng-option value="50">50</ng-option>
                              <ng-option value="100">100</ng-option>
                            </ng-select>
                          </div>
                          <strong class="ms-md-3">Total: {{ workOrderData.videoGalleryList?.totalCount }}</strong>
                          <div class="ms-md-auto ms-sm-0">
                            <pagination-controls
                              (pageChange)="onPageChange($event, workOrderData.id)"
                              class="paginate"
                            ></pagination-controls>
                          </div>
                        </div>
                      </div>
                      <div class="text-center" *ngIf="!workOrderData.videoGalleryList?.videoGallery?.length">No Data Found</div>
                    </div>
                  </nb-tab>
                </nb-tabset>
              </div>
            </div>
          </div>
          <div class="row pt-3">
            <div>
              <button
                nbButton
                status="primary"
                size="medium"
                *ngIf="!deleteAllWorkOrder"
                type="submit"
                class="float-end m-1"
                (click)="UpdateWO(workOrderData.id)"
                [disabled]="isWOBeingEdited || workOrderData.isDeleted"
              >
                Save
              </button>
              <button
                class="reviewreportBtn m-1"
                nbButton
                status="primary"
                size="medium"
                *ngIf="
                  !alsoUpdateTheWO &&
                  (workOrderData.assesmentType === 'SV' || workOrderData.assesmentType === 'MVPM') &&
                  !(
                    workOrderData.woStatus === WO_STATUSES.REPORT_COMPLETE.id ||
                    workOrderData.woStatus === WO_STATUSES.FIELD_WORK_COMPLETE.id ||
                    workOrderData.woStatus === WO_STATUSES.REPORT_STARTED.id ||
                    workOrderData.woStatus === WO_STATUSES.REPORT_DRAFTED.id
                  )
                "
                (click)="startNewReport(workOrderData)"
                [disabled]="isWOBeingEdited"
              >
                {{ newReportBtnText }}
              </button>
              <button
                nbButton
                *ngIf="
                  (workOrderData.woStatus === 1 || workOrderData.woStatus === 2) &&
                  userRole !== 'customer' &&
                  (workOrders.assementType === 'SV' || workOrders.assementType === 'VGT' || workOrders.assementType === 'MVPM')
                "
                status="primary"
                size="medium"
                class="reviewreportBtn m-1"
                (click)="onViewReport(workOrderData.id)"
                [disabled]="isWOBeingEdited"
              >
                Review Report
              </button>
              <button
                nbButton
                status="primary"
                size="medium"
                type="submit"
                class="float-end m-1"
                *ngIf="workOrderData.reportStatus === 'Complete' && workOrderData.pdfReportLink && isportfolioUser"
                (click)="sendReportForReview(workOrderData.id)"
                [disabled]="isWOBeingEdited"
              >
                Submit To Customer
              </button>
            </div>
          </div>
          <div class="row my-3 g-0" *ngIf="workOrders.assementType !== 'PR'">
            <nb-accordion class="mb-3" [id]="workOrderData.workOrderNumber + 'reschedule'">
              <nb-accordion-item
                [expanded]="workOrderData.isScheduleExpanded"
                (collapsedChange)="rescheduleAccordionChange($event, workOrderData)"
              >
                <nb-accordion-item-header class="bg-light">
                  <div class="col-md-6">
                    <h6>Scheduling for: {{ workOrderData.workOrderNumber }}</h6>
                  </div>
                </nb-accordion-item-header>
                <nb-accordion-item-body>
                  <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
                    <table class="table table-bordered table-header-rotated">
                      <thead>
                        <tr>
                          <th scope="col" id="rescheduleNumber">
                            <div class="d-flex align-items-center"><span class="me-2">Reschedule #</span></div>
                          </th>
                          <th scope="col" id="originalDate">
                            <div class="d-flex align-items-center"><span class="me-2">Original Date</span></div>
                          </th>
                          <th scope="col" id="newDate">
                            <div class="d-flex align-items-center"><span class="me-2">New Date</span></div>
                          </th>
                          <th scope="col" id="scheduler">
                            <div class="d-flex align-items-center"><span class="me-2">Scheduler</span></div>
                          </th>
                          <th scope="col" id="fieldTech">
                            <div class="d-flex align-items-center"><span class="me-2">Field Tech</span></div>
                          </th>
                          <th scope="col" id="rescheduleCategory">
                            <div class="d-flex align-items-center"><span class="me-2">Reschedule Category</span></div>
                          </th>
                          <th scope="col" id="notes">
                            <div class="d-flex align-items-center"><span class="me-2">Notes</span></div>
                          </th>
                          <th scope="col" id="actions">
                            <div class="d-flex align-items-center"><span class="me-2">Actions</span></div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngIf="!workOrderData?.workOrderSchedules?.length">
                          <td class="text-center" colspan="8">No Reschedule Records Found</td>
                        </tr>
                        <tr *ngFor="let schedule of workOrderData.workOrderSchedules; let last = last; let index = index">
                          <td data-title="Reschedule #">
                            {{ index + 1 }}
                          </td>
                          <td data-title="Original Date">
                            {{ schedule.currentScheduleDate | date : 'MM/dd/yyyy' }}
                          </td>
                          <td data-title="New Date">
                            {{ schedule.scheduleDate | date : 'MM/dd/yyyy' }}
                          </td>
                          <td data-title="Scheduler">
                            <label class="table-label">{{ schedule.rescheduleBy }}</label>
                          </td>
                          <td data-title="Field Tech">
                            <label class="table-label">{{ schedule.fieldTechName }}</label>
                          </td>
                          <td data-title="Reschedule Category">
                            <label class="table-label">{{ schedule.rescheduleReason }}</label>
                          </td>
                          <td data-title="Notes" class="white-space-pre-wrap">
                            <label class="table-label">{{ schedule.notes }}</label>
                          </td>
                          <td
                            data-title="Actions"
                            *ngIf="
                              last &&
                              workOrderData?.dateScheduled &&
                              !workOrderData?.reportCompleteDate &&
                              workOrderData.woStatus !== 1 &&
                              checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER])
                            "
                            class="text-center"
                          >
                            <label class="table-label">
                              <div class="d-md-flex justify-content-center" *ngIf="!workOrderData.isDeleted">
                                <a
                                  *ngIf="schedule.scheduleDate"
                                  class="px-2 listgrid-icon"
                                  (click)="showRescheduler(workOrderData, schedule)"
                                >
                                  <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                                </a>
                                <a class="text-danger listgrid-icon px-2">
                                  <em
                                    class="fa fa-trash"
                                    nbTooltip="Delete"
                                    (click)="deleteReschedulesByWorkOrder(schedule.id, workOrderData)"
                                    nbTooltipPlacement="top"
                                    nbTooltipStatus="danger"
                                  ></em>
                                </a>
                              </div>
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="qe-accordion-footer">
                    <div class="completion-date">
                      <span *ngIf="workOrderData?.reportCompleteDate">
                        Actual completion date:
                        {{ workOrderData?.reportCompleteDate | date : 'MM/dd/yyyy' }}
                      </span>
                    </div>
                    <div class="reschedule-action">
                      <button
                        *ngIf="
                          workOrderData?.dateScheduled &&
                          !workOrderData?.reportCompleteDate &&
                          workOrderData.woStatus !== 1 &&
                          !alsoUpdateTheWO &&
                          checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER])
                        "
                        nbButton
                        status="primary"
                        size="medium"
                        type="submit"
                        class="float-end m-1"
                        (click)="showRescheduler(workOrderData)"
                      >
                        Reschedule
                      </button>
                    </div>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
            </nb-accordion>
          </div>
          <div class="row g-0">
            <nb-accordion class="mb-2">
              <nb-accordion-item class="border-bottom" (collapsedChange)="onChangeHistoryAccordian($event, workOrderData.id, i)">
                <nb-accordion-item-header class="accordion_head">
                  <strong>History</strong>
                </nb-accordion-item-header>
                <nb-accordion-item-body>
                  <div *ngIf="workOrderData?.workLogAuditHistory?.length > 0">
                    <nb-accordion class="mb-2" *ngFor="let item of workOrderData?.workLogAuditHistory">
                      <nb-accordion-item class="border-bottom">
                        <nb-accordion-item-header class="accordion_head">
                          <strong>
                            <strong>{{ item?.userName }}</strong>
                            <div class="label ms-2">{{ item?.action }}</div>
                            <span class="label ms-2"> - </span>
                            <span class="label ms-2">{{ item?.logDate }}</span>
                          </strong>
                        </nb-accordion-item-header>
                        <nb-accordion-item-body>
                          <div class="mt-2">
                            <div class="row">
                              <div class="col-2"><label class="label mb-0">Field</label></div>
                              <div class="col-5 text-center"><label class="label mb-0">Original Value</label></div>
                              <div class="col-5 text-center"><label class="label mb-0">New Value</label></div>
                            </div>
                            <div class="mt-2" *ngFor="let actionSummary of item?.auditLogDetails">
                              <div class="row">
                                <div class="col-2">{{ actionSummary?.fieldName }}</div>
                                <div class="col-5 text-center">{{ actionSummary?.oldValue || '-' }}</div>
                                <div class="col-5 text-center">{{ actionSummary?.newValue || '-' }}</div>
                              </div>
                            </div>
                          </div>
                        </nb-accordion-item-body>
                      </nb-accordion-item>
                    </nb-accordion>
                  </div>
                  <div *ngIf="workOrderData?.workLogAuditHistory?.length === 0" class="text-center">
                    <label>No Data Found</label>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
            </nb-accordion>
          </div>
        </nb-accordion-item-body>
      </nb-accordion-item>
    </nb-accordion>
  </nb-card-body>
</nb-card>

<button hidden type="button" #openModalButton (click)="openModal(template)"></button>
<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Warning...</h4>
  </div>
  <div class="modal-body">
    Your editing session will be time out in {{ counter }} seconds. To avoid being redirected to previous page press continue editing.
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="onBack()">Back</button>
    <button type="button" class="btn btn-primary" (click)="continueWorking(); modalRef.hide()">Continue Editing</button>
  </div>
</ng-template>

<p-toast [breakpoints]="{ '600px': { width: 'auto', margin: '0 1.25rem', right: '0', left: '0' } }" position="top-right" key="woToast">
  <ng-template let-message pTemplate="message">
    <div class="p-toast-detail mt-0">{{ message.detail }}</div>
    <button nbButton status="primary" size="small" (click)="hideToast()" class="m-2">OK</button>
  </ng-template>
</p-toast>

<ng-template #viewSummaryFormsTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">Summary Reports</h6>
      </div>
      <div>
        <button
          *ngIf="
            uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]?.length &&
            completedFormMapId[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL].length > 1
          "
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="downloadAllForms(woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL)"
          type="button"
        >
          Download All
        </button>
        <button
          type="button"
          class="close"
          aria-label="Close"
          (click)="viewFromModalRef.hide(); getCompletedFormCount(); getWorkOrder(); removeBackToWhereParam()"
        >
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Reports List">
          <thead>
            <tr>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="FormTitle">Form Title</th>
              <th class="text-center" id="UploadedDate">Uploaded Date</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let viewSummaryReports of uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]
                  | paginate
                    : {
                        id: woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL,
                        itemsPerPage: pagination[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL].itemsCount,
                        currentPage: pagination[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL].currentPage,
                        totalItems: pagination[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL].totalCount
                      }
              "
            >
              <td class="text-center" data-title="Form Name">{{ viewSummaryReports.formName }}</td>
              <td class="text-center" data-title="Form Title">{{ viewSummaryReports.formTitle }}</td>
              <td class="text-center" data-title="Uploaded Date">
                {{ viewSummaryReports.updatedDate ? (viewSummaryReports.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="viewSummaryReports.formStatus === 3"
                    class="fa fa-download cursor-pointer text-primary me-2"
                    nbTooltip="Download"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="downloadUploadedForm(viewSummaryReports, woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL)"
                  ></em>
                  <em
                    *ngIf="viewSummaryReports.isActiveSummaryReport"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="
                      this.qestFormId = viewSummaryReports.qestFormId;
                      goToFormFillingPage(viewSummaryReports, pageOpenFromConstant.VIEW_SUMMARY_MODEL_SCREEN)
                    "
                  ></em>
                  <em
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER]"
                    class="fa fa-trash text-danger cursor-pointer me-2"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="danger"
                    (click)="
                      onDeleteUploadedForm(viewSummaryReports?.qestwoMapId, woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL)
                    "
                  ></em>
                  <!-- <em
                    *ngIf="userRole !== 'customer' && viewSummaryReports.formStatus === 3"
                    class="fa-solid fa-clock text-primary cursor-pointer"
                    nbTooltip="Form History"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="getHistoryForQESTForms(historyModalTemplate, viewSummaryReports)"
                  ></em> -->
                </div>
              </td>
            </tr>
            <tr>
              <td
                colspan="4"
                *ngIf="!uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]?.length"
                class="no-record text-center"
              >
                No Data Found
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="mt-2 d-md-flex align-items-center"
        *ngIf="uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]?.length"
      >
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            [(ngModel)]="pagination[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL].pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSizeQEST(woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL)"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination[woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL].totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls
            [id]="woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL"
            (pageChange)="onPageChangeQEST($event, woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL)"
            class="paginate"
          ></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #viewTPMFormsTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">TPM Forms</h6>
      </div>
      <div>
        <button
          *ngIf="
            uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]?.length &&
            completedFormMapId[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL].length > 1
          "
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="downloadAllForms(woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL)"
          type="button"
        >
          Download All
        </button>
        <button
          type="button"
          class="close"
          aria-label="Close"
          (click)="viewFromModalRef.hide(); getCompletedFormCount(); getWorkOrder(); removeBackToWhereParam()"
        >
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Reports List">
          <thead>
            <tr>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="FormTitle">Form Title</th>
              <th class="text-center" id="UploadedDate">Uploaded Date</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let viewTPMForms of uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]
                  | paginate
                    : {
                        id: woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL,
                        itemsPerPage: pagination[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL].itemsCount,
                        currentPage: pagination[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL].currentPage,
                        totalItems: pagination[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL].totalCount
                      }
              "
            >
              <td class="text-center" data-title="Form Name">{{ viewTPMForms.formName }}</td>
              <td class="text-center" data-title="Form Title">{{ viewTPMForms.formTitle }}</td>
              <td class="text-center" data-title="Uploaded Date">
                {{ viewTPMForms.updatedDate ? (viewTPMForms.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="viewTPMForms.formStatus === 3"
                    class="fa fa-download cursor-pointer text-primary me-2"
                    nbTooltip="Download"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="downloadUploadedForm(viewTPMForms, woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL)"
                  ></em>
                  <em
                    *ngIf="viewTPMForms.isTPMFormActive"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="
                      this.qestFormId = viewTPMForms.qestFormId;
                      goToFormFillingPage(viewTPMForms, pageOpenFromConstant.VIEW_TPM_MODEL_SCREEN)
                    "
                  ></em>
                  <em
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER]"
                    class="fa fa-trash text-danger cursor-pointer me-2"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="danger"
                    (click)="onDeleteUploadedForm(viewTPMForms?.qestwoMapId, woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL)"
                  ></em>
                  <!-- <em
                    *ngIf="userRole !== 'customer' && viewTPMForms.formStatus === 3"
                    class="fa-solid fa-clock text-primary cursor-pointer"
                    nbTooltip="Form History"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="getHistoryForQESTForms(historyModalTemplate, viewTPMForms)"
                  ></em> -->
                </div>
              </td>
            </tr>
            <tr>
              <td
                colspan="4"
                *ngIf="!uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]?.length"
                class="no-record text-center"
              >
                No Data Found
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="mt-2 d-md-flex align-items-center"
        *ngIf="uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]?.length"
      >
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            [(ngModel)]="pagination[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL].pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSizeQEST(woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL)"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination[woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL].totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls
            [id]="woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL"
            (pageChange)="onPageChangeQEST($event, woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL)"
            class="paginate"
          ></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #availableFormList>
  <div class="alert-box">
    <div class="modal-header align-items-center">
      <h6 class="modal-title">Available Forms</h6>
      <div>
        <button
          *ngIf="formsListingData?.length"
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="
            availableFormModalRef.hide();
            isStartAll = true;
            openTemplateModal(selectInverterModal, woTemplateOpenModelForFormConstant.SELECT_INVERTER, qestWorkOrderId, null)
          "
          type="button"
        >
          Start All
        </button>
        <button type="button" class="close" aria-label="Close" (click)="availableFormModalRef.hide()">
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Forms List">
          <thead>
            <tr>
              <th class="text-center" id="TemplateType">Template Type</th>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="Equipment">Equipment</th>
              <th class="text-center" id="Status">Status</th>
              <th class="text-center" id="LastUpdatedOn">Last Updated On</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let formList of formsListingData
                  | paginate
                    : {
                        id: woTemplateOpenModelForFormConstant.AVAILABLE_FORM,
                        itemsPerPage: pagination[woTemplateOpenModelForFormConstant.AVAILABLE_FORM].itemsCount,
                        currentPage: pagination[woTemplateOpenModelForFormConstant.AVAILABLE_FORM].currentPage,
                        totalItems: pagination[woTemplateOpenModelForFormConstant.AVAILABLE_FORM].totalCount
                      }
              "
            >
              <td class="text-center" data-title="Template Type">{{ formList.templateTypeName }}</td>
              <td class="text-center" data-title="Form Name">{{ formList.formName }}</td>
              <td class="text-center" data-title="Equipment">
                <span
                  *ngIf="formList.equipmentName"
                  nbTooltip="{{ formList?.equipmentName }}"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                >
                  <sfl-read-more [content]="formList?.equipmentName"></sfl-read-more>
                </span>
              </td>
              <td class="text-center" data-title="Status">
                {{ getStatusLabel(formList.formStatus) }}
              </td>
              <td class="text-center" data-title="Last Updated On">
                {{ formList.updatedDate ? (formList.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="formList.formStatus !== 0"
                    class="fa fa-eye cursor-pointer text-primary me-2"
                    nbTooltip="View"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="availableFormModalRef.hide(); customFormService.previewFormOrTemplate(formList.qestFormId, true)"
                  ></em>
                  <em
                    *ngIf="formList.formStatus !== 0"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="availableFormModalRef.hide(); openModalForStartFormByInverter(startFormUsingInverter, formList.qestFormId)"
                  ></em>
                  <em
                    *ngIf="formList.formStatus === 0"
                    class="fa-solid fa-play text-primary cursor-pointer me-2"
                    nbTooltip="Start"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="
                      availableFormModalRef.hide();
                      openTemplateModal(
                        selectInverterModal,
                        woTemplateOpenModelForFormConstant.SELECT_INVERTER,
                        qestWorkOrderId,
                        formList.qestFormId
                      )
                    "
                  ></em>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="7" *ngIf="!formsListingData?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="formsListingData?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            [(ngModel)]="pagination[woTemplateOpenModelForFormConstant.AVAILABLE_FORM].pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSizeQEST(woTemplateOpenModelForFormConstant.AVAILABLE_FORM)"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination[woTemplateOpenModelForFormConstant.AVAILABLE_FORM].totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls
            [id]="woTemplateOpenModelForFormConstant.AVAILABLE_FORM"
            (pageChange)="onPageChangeQEST($event, woTemplateOpenModelForFormConstant.AVAILABLE_FORM)"
            class="paginate"
          ></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #availableModuleTorqueForms>
  <div class="alert-box">
    <div class="modal-header align-items-center">
      <h6 class="modal-title">Available Forms</h6>
      <div>
        <button type="button" class="close" aria-label="Close" (click)="availableModuleTorqueFormModalRef.hide(); getCompletedFormCount()">
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Forms List">
          <thead>
            <tr>
              <th class="text-center" id="TemplateType">Template Type</th>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="Status">Status</th>
              <th class="text-center" id="LastUpdatedOn">Last Updated On</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let formList of moduleTorqueFormsListingData?.listOfQESTForm
                  | paginate
                    : {
                        id: woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS,
                        itemsPerPage: pagination[woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS].itemsCount,
                        currentPage: pagination[woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS].currentPage,
                        totalItems: pagination[woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS].totalCount
                      }
              "
            >
              <td class="text-center" data-title="Template Type">{{ formList.templateTypeName }}</td>
              <td class="text-center" data-title="Form Name">{{ formList.formName }}</td>
              <td class="text-center" data-title="Status">
                {{ getStatusLabel(formList.formStatus) }}
              </td>
              <td class="text-center" data-title="Last Updated On">
                {{ formList.updatedDate ? (formList.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="formList.formStatus !== 0"
                    class="fa fa-eye cursor-pointer text-primary me-2"
                    nbTooltip="View"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="availableModuleTorqueFormModalRef.hide(); customFormService.previewFormOrTemplate(formList.qestFormId, true)"
                  ></em>
                  <em
                    *ngIf="formList.formStatus !== 0"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="availableModuleTorqueFormModalRef.hide(); openModalForStartFormByZone(startFormUsingZone, formList.qestFormId)"
                  ></em>
                  <em
                    *ngIf="formList.formStatus === 0"
                    class="fa-solid fa-play text-primary cursor-pointer me-2"
                    nbTooltip="Start"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="
                      availableModuleTorqueFormModalRef.hide();
                      openTemplateModal(
                        selectZoneModal,
                        woTemplateOpenModelForFormConstant.SELECT_ZONE,
                        qestWorkOrderId,
                        formList.qestFormId
                      )
                    "
                  ></em>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="7" *ngIf="!moduleTorqueFormsListingData?.listOfQESTForm?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="moduleTorqueFormsListingData?.listOfQESTForm?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            [(ngModel)]="pagination[woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS].pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSizeQEST(woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS)"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination[woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS].totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls
            [id]="woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS"
            (pageChange)="onPageChangeQEST($event, woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS)"
            class="paginate"
          ></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #selectInverterModal>
  <div class="selcet-inverter">
    <div class="modal-header align-items-center">
      <h6 class="modal-title">Select Inverter ({{ inverterListData.length }})</h6>
      <button type="button" class="close" aria-label="Close" (click)="selectInverterModalRef.hide(); searchText = ''">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="row align-items-center flex-wrap">
        <div class="col-12 col-md-6 mb-3">
          <input nbInput fullWidth class="form-control" [(ngModel)]="searchText" type="text" />
        </div>
        <div class="col-12 col-md-6 text-end mb-3">
          <div class="form-control-group accept-group">
            <nb-checkbox name="SelectAll" class="me-3" (change)="SelectDeSelectAll($event.target.checked)">Select All</nb-checkbox>
          </div>
        </div>
      </div>
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <div class="row g-0" *ngIf="inverterListData?.length">
          <div class="col-12 col-md-6" *ngFor="let inverter of inverterListData | searchFilter : searchText : 'name'">
            <p>
              <nb-checkbox name="DeselectAll" [(ngModel)]="inverter.isSelected">{{ inverter.name }}</nb-checkbox>
            </p>
          </div>
        </div>
        <div class="text-center" *ngIf="!inverterListData?.length">
          <p>No data found</p>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div class="d-flex justify-content-end align-items-center">
        <button
          class="linear-mode-button me-3"
          nbButton
          status="secondary"
          size="small"
          [disabled]="loading"
          (click)="selectInverterModalRef.hide(); searchText = ''"
          type="button"
        >
          Close
        </button>
        <button
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="AddSelectedInverter()"
          type="button"
        >
          {{ isAnyFormStartOrComplete ? 'Continue' : 'Add' }}
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #selectZoneModal>
  <div class="selcet-inverter">
    <div class="modal-header align-items-center">
      <h6 class="modal-title">Select Zone ({{ zoneListDataForSelection.length }})</h6>
      <button type="button" class="close" aria-label="Close" (click)="selectZoneModalRef.hide(); searchText = ''">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="row align-items-center flex-wrap">
        <div class="col-12 col-md-6 mb-3">
          <input nbInput fullWidth class="form-control" [(ngModel)]="searchText" type="text" />
        </div>
        <div class="col-12 col-md-6 text-end mb-3">
          <div class="form-control-group accept-group">
            <nb-checkbox name="SelectAll" class="me-3" (change)="SelectDeSelectAllZones($event.target.checked)">Select All</nb-checkbox>
          </div>
        </div>
      </div>
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <div class="row g-0" *ngIf="zoneListDataForSelection?.length">
          <div class="col-12 col-md-6" *ngFor="let zone of zoneListDataForSelection | searchFilter : searchText : 'name'">
            <p>
              <nb-checkbox name="DeselectAll" [(ngModel)]="zone.isSelected">{{ zone.zoneName }}</nb-checkbox>
            </p>
          </div>
        </div>
        <div class="text-center" *ngIf="!zoneListDataForSelection?.length">
          <p>No zone found</p>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div class="d-flex justify-content-end align-items-center">
        <button
          class="linear-mode-button me-3"
          nbButton
          status="secondary"
          size="small"
          [disabled]="loading"
          (click)="selectZoneModalRef.hide(); searchText = ''"
          type="button"
        >
          Close
        </button>
        <button
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="addSelectedZones()"
          type="button"
        >
          {{ isAnyFormStartOrComplete ? 'Continue' : 'Add' }}
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #viewFormsTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">Forms</h6>
      </div>
      <div>
        <button
          *ngIf="
            uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]?.length &&
            completedFormMapId[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL].length > 1
          "
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="downloadAllForms(woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL)"
          type="button"
        >
          Download All
        </button>
        <button
          type="button"
          class="close"
          aria-label="Close"
          (click)="viewFromModalRef.hide(); getCompletedFormCount(); removeBackToWhereParam()"
        >
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Forms List">
          <thead>
            <tr>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="InverterName">Inverter Name</th>
              <th class="text-center" id="Status">Status</th>
              <th class="text-center" id="UploadedDate">Uploaded Date</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let viewForms of uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]
                  | paginate
                    : {
                        id: woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL,
                        itemsPerPage: pagination[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL].itemsCount,
                        currentPage: pagination[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL].currentPage,
                        totalItems: pagination[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL].totalCount
                      }
              "
            >
              <td class="text-center" data-title="Form Name">{{ viewForms.formName }}</td>
              <td class="text-center" data-title="Inverter Name">{{ viewForms.deviceName }}</td>
              <td class="text-center" data-title="Status">
                {{ getStatusLabel(viewForms.formStatus) }}
              </td>
              <td class="text-center" data-title="Uploaded Date">
                {{ viewForms.updatedDate ? (viewForms.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="viewForms.formStatus === 3"
                    class="fa fa-download cursor-pointer text-primary me-2"
                    nbTooltip="Download"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="downloadUploadedForm(viewForms, woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL)"
                  ></em>
                  <em
                    *ngIf="userRole !== 'customer'"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="this.qestFormId = viewForms.qestFormId; goToFormFillingPage(viewForms, pageOpenFromConstant.VIEW_MODEL_SCREEN)"
                  ></em>
                  <!-- check this for summary report for above onclick (click)="this.qestFormId = viewForms.qestFormId; goToFormFillingPage(viewForms, pageOpenFromConstant.VIEW_MODEL_SCREEN)" -->
                  <em
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER]"
                    class="fa fa-trash text-danger cursor-pointer me-2"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="danger"
                    (click)="onDeleteUploadedForm(viewForms?.qestwoMapId, woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL)"
                  ></em>
                  <em
                    *ngIf="userRole !== 'customer' && viewForms.formStatus === 3"
                    class="fa-solid fa-clock text-primary cursor-pointer"
                    nbTooltip="Form History"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="getHistoryForQESTForms(historyModalTemplate, viewForms)"
                  ></em>
                </div>
              </td>
            </tr>
            <tr>
              <td
                colspan="4"
                *ngIf="!uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]?.length"
                class="no-record text-center"
              >
                No Data Found
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            [(ngModel)]="pagination[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL].pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSizeQEST(woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL)"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination[woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL].totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls
            [id]="woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL"
            (pageChange)="onPageChangeQEST($event, woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL)"
            class="paginate"
          ></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #viewModuleTorqueFormsTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">Forms</h6>
      </div>
      <div>
        <button
          *ngIf="
            uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]?.length &&
            completedFormMapId[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL].length > 1
          "
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="downloadAllForms(woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL)"
          type="button"
        >
          Download All
        </button>
        <button
          type="button"
          class="close"
          aria-label="Close"
          (click)="viewModuleTorqueFormsRef.hide(); getCompletedFormCount(); removeBackToWhereParam()"
        >
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Forms List">
          <thead>
            <tr>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="ZoneName">Zone Name</th>
              <th class="text-center" id="Status">Status</th>
              <th class="text-center" id="UploadedDate">Uploaded Date</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let viewForms of uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]
                  | paginate
                    : {
                        id: woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL,
                        itemsPerPage: pagination[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL].itemsCount,
                        currentPage: pagination[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL].currentPage,
                        totalItems: pagination[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL].totalCount
                      }
              "
            >
              <td class="text-center" data-title="Form Name">{{ viewForms.formName }}</td>
              <td class="text-center" data-title="Zone Name">{{ viewForms.zoneName }}</td>
              <td class="text-center" data-title="Status">
                {{ getStatusLabel(viewForms.formStatus) }}
              </td>
              <td class="text-center" data-title="Uploaded Date">
                {{ viewForms.updatedDate ? (viewForms.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="viewForms.formStatus === 3"
                    class="fa fa-download cursor-pointer text-primary me-2"
                    nbTooltip="Download"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="downloadUploadedForm(viewForms, woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL)"
                  ></em>
                  <em
                    *ngIf="userRole !== 'customer'"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="
                      this.qestFormId = viewForms.qestFormId;
                      goToFormFillingPage(viewForms, woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL)
                    "
                  ></em>
                  <!-- check this for summary report for above onclick (click)="this.qestFormId = viewForms.qestFormId; goToFormFillingPage(viewForms, pageOpenFromConstant.VIEW_MODEL_SCREEN)" -->
                  <em
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER]"
                    class="fa fa-trash text-danger cursor-pointer me-2"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="danger"
                    (click)="onDeleteUploadedForm(viewForms?.qestwoMapId, woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL)"
                  ></em>
                  <em
                    *ngIf="userRole !== 'customer' && viewForms.formStatus === 3"
                    class="fa-solid fa-clock text-primary cursor-pointer"
                    nbTooltip="Form History"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="getHistoryForQESTForms(historyModalTemplate, viewForms)"
                  ></em>
                </div>
              </td>
            </tr>
            <tr>
              <td
                colspan="4"
                *ngIf="!uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]?.length"
                class="no-record text-center"
              >
                No Data Found
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="mt-2 d-md-flex align-items-center"
        *ngIf="uploadedFormsData[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]?.length"
      >
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            [(ngModel)]="pagination[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL].pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSizeQEST(woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL)"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination[woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL].totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls
            [id]="woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL"
            (pageChange)="onPageChangeQEST($event, woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL)"
            class="paginate"
          ></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #startFormUsingInverter>
  <div class="start-form-by-inverter">
    <div class="modal-header align-items-center">
      <div class="col-6 col-md-6 mb-3">
        <h6 class="modal-title">Select Inverter(s) ({{ selectedInverters.length }})</h6>
      </div>
      <div class="col-6 col-md-6 mb-3 d-flex align-items-center justify-content-end">
        <div class="d-flex align-items-center">
          <nb-progress-bar
            [value]="progressBarCount | number : '1.0-0'"
            class="progress-bar"
            status="success"
            [displayValue]="progressBarCount"
          ></nb-progress-bar>
          <button
            nbButton
            status="primary"
            size="small"
            class="mx-2"
            (click)="
              startFormByInverterModalRef.hide();
              openTemplateModal(selectInverterModal, woTemplateOpenModelForFormConstant.SELECT_INVERTER, qestWorkOrderId, null)
            "
          >
            Edit Inverter
          </button>
          <button nbButton status="primary" size="small" (click)="onCloseOfStartFormUsingInverter()">Back</button>
        </div>
      </div>
    </div>
    <div class="modal-body">
      <div class="row mb-2">
        <div class="col-12 col-md-3 mt-3">
          <input nbInput fullWidth class="form-control" [(ngModel)]="searchInverter" type="text" placeholder="Search" />
        </div>
        <div class="col-12 col-md-9 mt-3 d-flex align-items-center justify-content-end">
          <span>
            <em class="fa-regular fa-circle-check text-success me-2" aria-hidden="true"></em>
            Completed ({{ countStatuses(3) }})
          </span>
          <span class="mx-3">
            <em class="fa-regular fa-clock text-warning me-2" aria-hidden="true"></em>
            In Progress ({{ countStatuses(1) }})
          </span>
          <span>
            <em class="fa-regular fa-circle-xmark text-danger me-2" aria-hidden="true"></em>
            Not Started ({{ countStatuses(0) }})
          </span>
        </div>
      </div>
      <ul class="inverters-listing">
        <li
          class="list-items d-flex justify-content-between align-items-center cursor-pointer"
          *ngFor="let inverter of selectedInverters | searchFilter : searchInverter : 'deviceName'"
          (click)="goToFormFillingPage(inverter, pageOpenFromConstant.INVERTER_MODEL_SCREEN)"
        >
          <!-- check this for summary report in above onclick (click)="goToFormFillingPage(inverter, pageOpenFromConstant.INVERTER_MODEL_SCREEN)" -->
          <div class="inverter-name">
            <span class="status-icon">
              <ng-container [ngSwitch]="inverter.formStatus">
                <em *ngSwitchCase="formStatuses.NotStarted" class="fa-regular fa-circle-xmark text-danger me-1" aria-hidden="true"></em>
                <em *ngSwitchCase="formStatuses.InProgress" class="fa-regular fa-clock text-warning me-1" aria-hidden="true"></em>
                <em *ngSwitchCase="formStatuses.Draft" class="fa-regular fa-clock text-warning me-1" aria-hidden="true"></em>
                <em *ngSwitchCase="formStatuses.Completed" class="fa-regular fa-circle-check text-success me-1" aria-hidden="true"></em>
              </ng-container>
            </span>
            <span class="inverter-name"> {{ inverter.deviceName }}</span>
            <span class="status-name ms-3" *ngIf="inverter.formStatus === 0"
              ><p-badge severity="danger" [value]="'NOT STARTED'"></p-badge
            ></span>
            <span class="status-name ms-3" *ngIf="inverter.formStatus === 1"
              ><p-badge severity="warning" [value]="'IN PROGRESS'"></p-badge
            ></span>
            <span class="status-name ms-3" *ngIf="inverter.formStatus === 2"><p-badge severity="info" [value]="'DRAFT'"></p-badge> </span>
            <span class="status-name ms-3" *ngIf="inverter.formStatus === 3"
              ><p-badge severity="success" [value]="'COMPLETED'"></p-badge
            ></span>
          </div>
          <div class="mx-2">
            <span
              ><em
                class="fa fa-trash text-danger"
                *ngIf="inverter.formStatus === 0"
                aria-hidden="true"
                (click)="$event.stopPropagation(); deleteSelectedInverter(inverter.qestwoMapId)"
              ></em
            ></span>
            <!-- <span><em *ngIf="inverter.formStatus === 3 || inverter.formStatus === 2" class="fa fa-eye" aria-hidden="true"></em></span>
            <span><em *ngIf="inverter.formStatus === 2" class="fa fa-edit" aria-hidden="true"></em></span> -->
          </div>
        </li>
      </ul>
    </div>
  </div>
</ng-template>

<ng-template #startFormUsingZone>
  <div class="start-form-by-inverter">
    <div class="modal-header align-items-center">
      <div class="col-6 col-md-6 mb-3">
        <h6 class="modal-title">Select Zone(s) ({{ selectedZones.length }})</h6>
      </div>
      <div class="col-6 col-md-6 mb-3 d-flex align-items-center justify-content-end">
        <div class="d-flex align-items-center">
          <nb-progress-bar
            [value]="MTRQprogressBarCount | number : '1.0-0'"
            class="progress-bar"
            status="success"
            [displayValue]="MTRQprogressBarCount"
          ></nb-progress-bar>
          <button
            nbButton
            status="primary"
            size="small"
            class="mx-2"
            (click)="
              startFormUsingZoneRef.hide();
              openTemplateModal(selectZoneModal, woTemplateOpenModelForFormConstant.SELECT_ZONE, qestWorkOrderId, null)
            "
          >
            Edit Zone
          </button>
          <button nbButton status="primary" size="small" (click)="onCloseOfStartFormUsingZone()">Back</button>
        </div>
      </div>
    </div>
    <div class="modal-body">
      <div class="row mb-2">
        <div class="col-12 col-md-3 mt-3">
          <input nbInput fullWidth class="form-control" [(ngModel)]="searchInverter" type="text" placeholder="Search" />
        </div>
        <div class="col-12 col-md-9 mt-3 d-flex align-items-center justify-content-end">
          <span>
            <em class="fa-regular fa-circle-check text-success me-2" aria-hidden="true"></em>
            Completed ({{ countModuleTorqueStatuses(3) }})
          </span>
          <span class="mx-3">
            <em class="fa-regular fa-clock text-warning me-2" aria-hidden="true"></em>
            In Progress ({{ countModuleTorqueStatuses(1) }})
          </span>
          <span>
            <em class="fa-regular fa-circle-xmark text-danger me-2" aria-hidden="true"></em>
            Not Started ({{ countModuleTorqueStatuses(0) }})
          </span>
        </div>
      </div>
      <ul class="inverters-listing">
        <li
          class="list-items d-flex justify-content-between align-items-center cursor-pointer"
          *ngFor="let zone of selectedZones | searchFilter : searchInverter : 'deviceName'"
          (click)="goToFormFillingPage(zone, pageOpenFromConstant.ZONE_MODEL_SCREEN)"
        >
          <!-- check this for summary report in above onclick (click)="goToFormFillingPage(inverter, pageOpenFromConstant.INVERTER_MODEL_SCREEN)" -->
          <div class="inverter-name">
            <span class="status-icon">
              <ng-container [ngSwitch]="zone.formStatus">
                <em *ngSwitchCase="formStatuses.NotStarted" class="fa-regular fa-circle-xmark text-danger me-1" aria-hidden="true"></em>
                <em *ngSwitchCase="formStatuses.InProgress" class="fa-regular fa-clock text-warning me-1" aria-hidden="true"></em>
                <em *ngSwitchCase="formStatuses.Draft" class="fa-regular fa-clock text-warning me-1" aria-hidden="true"></em>
                <em *ngSwitchCase="formStatuses.Completed" class="fa-regular fa-circle-check text-success me-1" aria-hidden="true"></em>
              </ng-container>
            </span>
            <span class="inverter-name"> {{ zone.zoneName }}</span>
            <span class="status-name ms-3" *ngIf="zone.formStatus === 0"
              ><p-badge severity="danger" [value]="'NOT STARTED'"></p-badge
            ></span>
            <span class="status-name ms-3" *ngIf="zone.formStatus === 1"
              ><p-badge severity="warning" [value]="'IN PROGRESS'"></p-badge
            ></span>
            <span class="status-name ms-3" *ngIf="zone.formStatus === 2"><p-badge severity="info" [value]="'DRAFT'"></p-badge> </span>
            <span class="status-name ms-3" *ngIf="zone.formStatus === 3"><p-badge severity="success" [value]="'COMPLETED'"></p-badge></span>
          </div>
          <div class="mx-2">
            <span
              ><em
                class="fa fa-trash text-danger"
                *ngIf="zone.formStatus === 0"
                aria-hidden="true"
                (click)="$event.stopPropagation(); deleteSelectedZone(zone.qestwoMapId)"
              ></em
            ></span>
            <!-- <span><em *ngIf="inverter.formStatus === 3 || inverter.formStatus === 2" class="fa fa-eye" aria-hidden="true"></em></span>
            <span><em *ngIf="inverter.formStatus === 2" class="fa fa-edit" aria-hidden="true"></em></span> -->
          </div>
        </li>
      </ul>
    </div>
  </div>
</ng-template>

<ng-template #historyModalTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">{{ viewFromInfo.formName | uppercase }} FORM HISTORY</h6>
      </div>
      <button
        type="button"
        class="close"
        aria-label="Close"
        (click)="
          historyModalRef.hide();
          openTemplateModal(
            assessmentType === 'Module Torque' ? viewModuleTorqueFormsTemplate : viewFormsTemplate,
            assessmentType === 'Module Torque'
              ? woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL
              : woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL,
            null
          )
        "
      >
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body px-3 py-1">
      <div id="fixed-table" setTableHeight class="table-card-view" *ngIf="formEditNoteHistory.length">
        <ul class="list-unstyled m-0" *ngFor="let history of formEditNoteHistory">
          <li class="py-2 list-border">
            <p class="mb-2" *ngFor="let note of history.auditLogDetails">
              <strong>{{ note.newValue }}</strong>
            </p>
            <small class="pb-2">{{ history.userName }} - {{ history.logDate }}</small>
          </li>
        </ul>
      </div>
      <div *ngIf="!formEditNoteHistory.length" class="text-center">
        <p>No changes found for this form.</p>
      </div>
    </div>
  </div>
</ng-template>
