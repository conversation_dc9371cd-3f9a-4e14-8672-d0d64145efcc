import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModeWiseGuard } from '../../../@shared/services/mode-wise.guard';
import { SiteCheckInAddEditComponent } from './site-check-in-add-edit/site-check-in-add-edit.component';
import { SiteCheckInListingComponent } from './site-checkin-listing/site-checkin-listing.component';
import { ROLE_TYPE } from '../../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: SiteCheckInListingComponent,
    data: { pageTitle: 'Site Check-in' }
  },
  {
    path: 'detail/:mode/:id',
    component: SiteCheckInAddEditComponent,
    canActivate: [ModeWiseGuard],
    data: {
      edit: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      view: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Update Site Check-in'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SiteCheckInRoutingModule {}
