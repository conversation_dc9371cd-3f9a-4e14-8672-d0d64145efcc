import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { AutomationDeviceList, SiteAutomationDevicesList } from '../../../@shared/models/site.model';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { AddSiteDevice, ImportSiteDevice } from '../../site-device/site-device.model';
import { SiteDeviceService } from '../../site-device/site-device.service';
import { ViewDeviceDetailsComponent } from '../view-device-details/view-device-details.component';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-automation-site-device-list',
  templateUrl: './automation-site-device-list.component.html',
  styleUrls: ['./automation-site-device-list.component.scss']
})
export class AutomationSiteDeviceListComponent implements OnInit {
  @Input() siteId: number;
  @Input() customerId: number;
  @Input() portfolioId: number;
  @Input() automationDataSourceId: number;
  @Input() automationSiteDetailId: string;
  @Output() loading: EventEmitter<boolean> = new EventEmitter<boolean>();
  subscription: Subscription = new Subscription();
  automationDeviceList: AutomationDeviceList[] = [];
  listOfPlottingUnit = [];
  listOfReportingUnit = [];
  modalRef: BsModalRef;
  userRoles: string[];
  missingDCLoadCount: number;
  viewMode: boolean = true;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    public readonly siteDeviceService: SiteDeviceService,
    private readonly alertService: AlertService,
    private readonly router: Router,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.userRoles = this.storageService.get('user').authorities;
    if (this.siteId) {
      this.getSiteAutomationDeviceList();
    }
  }

  getSiteAutomationDeviceList() {
    this.subscription.add(
      this.siteDeviceService.getSiteAutomationDeviceList(this.siteId).subscribe({
        next: (res: SiteAutomationDevicesList) => {
          this.automationDeviceList = res.siteDevices;
          this.missingDCLoadCount = res.invDCNullCount + res.meterDCNullCount;
          setTimeout(() => {
            this.loading.emit(false);
          }, 0);
          this.getDataByDeviceType();
        },
        error: _e => {
          setTimeout(() => {
            this.loading.emit(false);
          }, 0);
        }
      })
    );
  }

  getDataByDeviceType() {
    for (const [index, value] of this.automationDeviceList.entries()) {
      setTimeout(() => {
        this.getDevicePlottingReporting(value.deviceTypeId, value.automationSiteDetailId, this.siteId, index);
      }, 0);
    }
  }

  getDevicePlottingReporting(id, automationSiteDetailId, siteId: number, index: number) {
    this.subscription.add(
      this.siteDeviceService.getDevicePlottingReporting(siteId, id, automationSiteDetailId).subscribe({
        next: (res: any) => {
          if (!this.automationDeviceList[index].reportingUnitId) {
            this.automationDeviceList[index].reportingUnitId = res.defaultReportingUnit;
          }
          if (!this.automationDeviceList[index].plottingUnitId) {
            this.automationDeviceList[index].plottingUnitId = res.defaultPlottingUnit;
          }
          this.listOfPlottingUnit[index] = res.listOfPlottingUnit;
          this.listOfReportingUnit[index] = res.listOfReportingUnit;
        }
      })
    );
  }

  gotoEditableDevice() {
    this.viewMode = false;
  }

  updateAutomationDeviceLIst() {
    this.loading.emit(true);
    this.subscription.add(
      this.siteDeviceService.updateSiteAutomationDeviceList(this.automationDeviceList).subscribe({
        next: (_res: any) => {
          const updatedDcLoad = this.automationDeviceList.filter(
            item =>
              (item.dcLoad === '' || item.dcLoad === '-' || item.dcLoad === null) &&
              (item.deviceType === 'Inverter' || item.deviceType === 'Meter')
          );
          this.missingDCLoadCount = updatedDcLoad.length;
          this.loading.emit(false);
          this.alertService.showSuccessToast('Records update successfully.');
          this.viewMode = true;
        },
        error: _e => {
          this.loading.emit(false);
        }
      })
    );
  }

  Cancel() {
    this.viewMode = true;
  }

  redirectToDeviceAdd() {
    const prePopulateData: AddSiteDevice = new AddSiteDevice();
    prePopulateData.customerId = this.customerId;
    prePopulateData.portfolioId = this.portfolioId;
    prePopulateData.siteId = this.siteId;
    this.siteDeviceService.setAddDevicePrePopulateData(prePopulateData);
    this.router.navigateByUrl('/entities/site-device/add');
  }

  redirectToDeviceImport() {
    const prePopulateData: ImportSiteDevice = new ImportSiteDevice();
    prePopulateData.automationDataSourceId = this.automationDataSourceId;
    prePopulateData.automationSiteDetailId = this.automationSiteDetailId;
    this.siteDeviceService.setImportDevicePrePopulateData(prePopulateData);
    this.router.navigateByUrl('/entities/site-device/import');
  }

  viewSiteDeviceDetails(id: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        deviceId: id
      }
    };
    this.modalRef = this.modalService.show(ViewDeviceDetailsComponent, ngModalOptions);
  }
}
