import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NbDateService } from '@nebular/theme';
import { Subscription } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerService } from '../../customer-management/customer.service';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { SiteService } from '../../site-management/site.service';
import { AvailabilityService } from '../availability.service';
import { AutomationSitePortfolio } from '../reports/report.model';
import {
  DataTableFilter,
  DataTableInformationList,
  DateFetchStatus,
  DeviceFetchStatus,
  EditHistoryTable,
  EditRefetchEmit,
  FetchCount,
  FetchDate,
  FetchDevice,
  FetchStatus,
  RefetchData,
  RefetchEmit
} from './data-table-model';
import { AvailabilityDataTableService } from './data-table.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-data-table',
  templateUrl: './data-table.component.html',
  styleUrls: ['./data-table.component.scss']
})
export class DataTableComponent implements OnInit {
  loading = true;
  loadHistory = false;
  loadDataTable = false;
  loadREFetchHistory = false;
  subscription: Subscription = new Subscription();
  customerList: Dropdown[];
  portfolioList: Dropdown[];
  siteList: Dropdown[];
  siteId: number;
  editHistoryData: EditHistoryTable = new EditHistoryTable();
  filterModel: DataTableFilter = new DataTableFilter();
  editHistory: DataTableFilter = new DataTableFilter();
  reFetchParam: DataTableFilter = new DataTableFilter();
  reFetchData: RefetchData = new RefetchData();
  dataTableInformationList: DataTableInformationList = new DataTableInformationList();
  editHistoryList = [];
  reFetchDetailList = [];
  months = [
    { id: 0, name: 'All' },
    { id: 1, name: 'Jan' },
    { id: 2, name: 'Feb' },
    { id: 3, name: 'Mar' },
    { id: 4, name: 'Apr' },
    { id: 5, name: 'May' },
    { id: 6, name: 'Jun' },
    { id: 7, name: 'Jul' },
    { id: 8, name: 'Aug' },
    { id: 9, name: 'Sep' },
    { id: 10, name: 'Oct' },
    { id: 11, name: 'Nov' },
    { id: 12, name: 'Dec' }
  ];
  activeTab = 'Data Table Information';
  generated = false;
  activeFetchingCount: number;
  allFetched: boolean;
  fetchingDevice: string[] = [];
  fetchingDate: string[] = [];
  @ViewChild('file') fileInput;
  max: Date;
  userRoll: string;

  constructor(
    private readonly customerService: CustomerService,
    private readonly portfolioService: PortfolioService,
    private readonly siteService: SiteService,
    private readonly availabilityDataTableService: AvailabilityDataTableService,
    private readonly commonService: CommonService,
    public datePipe: DatePipe,
    private readonly alertService: AlertService,
    protected dateService: NbDateService<Date>,
    private readonly availabilityService: AvailabilityService,
    private readonly router: Router,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.userRoll = this.storageService.get('user').authorities;
    const filterData = this.availabilityService.getViewFilter();
    if (filterData) {
      this.filterModel.customerId = filterData.customerId;
      this.filterModel.portfolioId = filterData.portfolioId;
      this.filterModel.siteId = filterData.siteId;
      this.filterModel.datePerformed = new Date(filterData.datePerformed);
      this.generateData();
    } else {
      this.filterModel.datePerformed = this.dateService.addDay(this.dateService.today(), -1);
    }
    this.getAllCustomer();
    this.max = this.dateService.addDay(this.dateService.today(), -1);
  }

  generateData() {
    if (this.generated) {
      this.changeTab(null);
    } else if (checkAuthorisations([ROLE_TYPE.CUSTOMER])) {
      this.getAllPerformanceDetails();
    }
    this.generated = true;
  }

  changeTab(event) {
    this.activeTab = event && event.tabTitle ? event.tabTitle : this.activeTab;
    if (this.activeTab === 'Data Table Information') {
      this.getAllPerformanceDetails();
    } else if (this.activeTab === 'Edit History') {
      this.getEditHistory();
    } else if (this.activeTab === 'Re-Fetch Status') {
      this.getReFetchStatus();
    }
  }

  getAllPerformanceDetails() {
    this.loadDataTable = true;
    const model: DataTableFilter = JSON.parse(JSON.stringify(this.filterModel));
    model.datePerformed = this.datePipe.transform(model.datePerformed, AppConstants.fullDateFormat);
    this.siteId = this.filterModel.siteId;
    this.subscription.add(
      this.availabilityDataTableService.getAllDataTableInformation(model).subscribe({
        next: (res: DataTableInformationList) => {
          this.dataTableInformationList = res;
          this.loadDataTable = false;
          this.checkDeviceFetchStatus();
          this.checkDateFetchStatus();
        },
        error: _e => {
          this.loadDataTable = false;
        }
      })
    );
  }

  getEditHistory() {
    this.loadHistory = true;
    this.editHistory.customerId = this.filterModel.customerId;
    this.editHistory.portfolioId = this.filterModel.portfolioId;
    this.editHistory.siteId = this.filterModel.siteId;
    this.editHistory.datePerformed = this.datePipe.transform(this.filterModel.datePerformed, AppConstants.fullDateFormat);
    this.subscription.add(
      this.availabilityDataTableService.getEditHistoryData(this.editHistory).subscribe({
        next: (res: EditHistoryTable) => {
          setTimeout(() => {
            this.editHistoryData = res;
            this.loadHistory = false;
          }, 0);
        },
        error: e => {
          this.loadHistory = false;
        }
      })
    );
  }

  editHistoryPageChange(data: EditRefetchEmit) {
    this.loadHistory = true;
    this.editHistory.page = Number(data.page);
    this.editHistory.itemsCount = Number(data.itemsCount);
    this.getEditHistory();
  }

  refetchStatusEmit(data: RefetchEmit) {
    this.loadREFetchHistory = true;
    this.reFetchParam.page = Number(data.page);
    this.reFetchParam.itemsCount = Number(data.itemsCount);
    this.getReFetchStatus();
  }

  getReFetchStatus() {
    this.loadREFetchHistory = true;
    this.reFetchParam.customerId = this.filterModel.customerId;
    this.reFetchParam.portfolioId = this.filterModel.portfolioId;
    this.reFetchParam.siteId = this.filterModel.siteId;
    this.reFetchParam.datePerformed = this.datePipe.transform(this.filterModel.datePerformed, AppConstants.fullDateFormat);
    this.subscription.add(
      this.availabilityDataTableService.getReFetchData(this.reFetchParam).subscribe({
        next: (res: RefetchData) => {
          setTimeout(() => {
            this.reFetchData = res;
            this.loadREFetchHistory = false;
          }, 0);
        },
        error: _e => {
          this.loadREFetchHistory = false;
        }
      })
    );
  }

  getAllCustomer() {
    this.loading = true;
    this.subscription.add(
      this.customerService.getAvailCustomer().subscribe({
        next: (res: Dropdown[]) => {
          this.customerList = res.filter(item => item.isActive);
          if (this.filterModel.customerId) {
            this.getAllPortfolioByCustomer();
          }
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  onCustomerSelect() {
    this.onCustomerDeSelect();
    if (this.filterModel.customerId) {
      this.getAllPortfolioByCustomer();
    }
  }

  onCustomerDeSelect() {
    this.portfolioList = [];
    this.siteList = [];
    this.onSiteSelectOrDeSelect();
    this.filterModel.portfolioId = null;
    this.filterModel.siteId = null;
  }

  getAllPortfolioByCustomer() {
    this.loading = true;
    const data: AutomationSitePortfolio = new AutomationSitePortfolio();
    if (this.filterModel.customerId) {
      data.customerIds.push(this.filterModel.customerId);
    }
    data.isAvailabilityCheck = true;
    this.siteService.automationSiteList = [];
    this.subscription.add(
      this.portfolioService.getAllAutomationPortfoliosByCustomerId(data).subscribe({
        next: (res: Dropdown[]) => {
          this.portfolioList = res;
          if (this.filterModel.portfolioId) {
            this.getAllSiteByPortfolio();
          }
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  onPortfolioSelect() {
    this.onPortfolioDeSelect();
    if (this.filterModel.portfolioId) {
      this.getAllSiteByPortfolio();
    }
  }

  onPortfolioDeSelect() {
    this.siteList = [];
    this.onSiteSelectOrDeSelect();
    this.filterModel.siteId = null;
  }

  getAllSiteByPortfolio() {
    this.loading = true;
    const data: AutomationSitePortfolio = new AutomationSitePortfolio();
    if (this.filterModel.customerId) {
      data.customerIds.push(this.filterModel.customerId);
    }
    if (this.filterModel.portfolioId) {
      data.portfolioIds.push(this.filterModel.portfolioId);
    }
    data.isAvailabilityCheck = true;
    this.subscription.add(
      this.siteService.getAllAutomationSiteById(data).subscribe({
        next: (res: Dropdown[]) => {
          this.siteList = res;
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  onSiteSelectOrDeSelect() {
    this.editHistory.siteId = this.filterModel.siteId;
    this.editHistory.datePerformed = this.datePipe.transform(this.filterModel.datePerformed, AppConstants.fullDateFormat);
    this.dataTableInformationList = new DataTableInformationList();
    this.editHistoryList = [];
    this.reFetchDetailList = [];
  }

  clearFilter() {
    this.generated = false;
    this.siteList = [];
    this.portfolioList = [];
    this.filterModel = new DataTableFilter();
    this.onSiteSelectOrDeSelect();
    this.availabilityService.setViewFilter(null);
    this.ngOnInit();
  }

  exportData() {
    this.loading = true;
    const obj = {
      customerId: this.filterModel.customerId,
      portfolioId: this.filterModel.portfolioId,
      siteId: this.filterModel.siteId,
      datePerformed: this.datePipe.transform(this.filterModel.datePerformed, AppConstants.fullDateFormat)
    };
    this.subscription.add(
      this.availabilityDataTableService.exportData(obj).subscribe({
        next: (res: any) => {
          if (res) {
            const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
            link.download = 'Data table Report.xlsx';
            link.click();
            this.loading = false;
          }
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  checkFetchCount(fibosCountFirst: number = 0, fibosCountSecond: number = 1) {
    const data: FetchCount = new FetchCount();
    data.siteId = Number(this.filterModel.siteId);
    data.datePerformed = this.datePipe.transform(this.filterModel.datePerformed, AppConstants.fullDateFormat);
    this.subscription.add(
      this.availabilityDataTableService.checkFetchCount(data).subscribe({
        next: (res: FetchStatus) => {
          if (res.count) {
            this.reCheckFetchCount(fibosCountFirst, fibosCountSecond);
          } else {
            this.getAllPerformanceDetails();
          }
        }
      })
    );
  }

  checkFetchStatus() {
    let count: number = this.dataTableInformationList.devicesData.filter(x => x.isFetching).length;
    count = count + this.dataTableInformationList.devices.filter(x => x.isFetching).length;
    this.activeFetchingCount = count;
    if (!count) {
      this.allFetched = true;
    } else {
      this.reCheckFetchCount();
    }
  }

  reCheckFetchCount(first: number = 1, second: number = 2) {
    if (!this.allFetched) {
      let time: number = first + second;
      time = time > 8 ? 8 : time;
      if (time >= 2) {
        setTimeout(() => {
          this.checkFetchCount(second, time);
        }, time * 1000);
      } else {
        this.reCheckFetchCount(second, time);
      }
    }
  }

  reCheckDeviceFetchStatus(data: string[]) {
    for (const i of data) {
      this.fetchingDevice.push(i);
    }
    this.reFetchDeviceInfo();
  }

  reFetchDeviceInfo(first: number = 1, second: number = 2) {
    if (this.fetchingDevice.length) {
      let time: number = first + second;
      time = time > 8 ? 8 : time;
      if (time >= 2) {
        setTimeout(() => {
          this.checkFetchDevice(second, time);
        }, time * 1000);
      } else {
        this.reFetchDeviceInfo(second, time);
      }
    }
  }

  checkFetchDevice(fibosCountFirst: number = 1, fibosCountSecond: number = 2) {
    const data: FetchDevice = new FetchDevice();
    data.customerId = Number(this.filterModel.customerId);
    data.siteId = Number(this.filterModel.siteId);
    data.datePerformed = this.datePipe.transform(this.filterModel.datePerformed, AppConstants.fullDateFormat);
    data.hardwareIds = this.fetchingDevice;
    this.subscription.add(
      this.availabilityDataTableService.refetchDataTablesByDevice(data).subscribe({
        next: (res: DeviceFetchStatus) => {
          if (res.fetched.length) {
            for (const i of res.fetched) {
              for (const j of i.hardwareFetched) {
                const tempData = [...this.dataTableInformationList.devicesData];
                for (const k of tempData) {
                  k.data[k.data.findIndex(x => k.label === j.calenderLabel && x.hardwareId === j.hardwareId)] = j;
                }
                this.dataTableInformationList.devicesData = [...tempData];
              }
              this.dataTableInformationList.devices.find(x => x.hardwareId === i.hardwareId).isFetching = false;
              if (this.activeTab === 'Re-Fetch Status') {
                this.getReFetchStatus();
              }
              if (res.fetching.length === 0) {
                this.alertService.showSuccessToast(`Device's fetching is completed.`);
              }
            }
          }
          if (res.fetching.length) {
            this.fetchingDevice = res.fetching;
            this.reFetchDeviceInfo(fibosCountFirst, fibosCountSecond);
          } else {
            this.fetchingDevice = [];
          }
        }
      })
    );
  }

  checkDeviceFetchStatus() {
    const data = this.dataTableInformationList.devices.filter(x => x.isFetching);
    for (const i of data) {
      this.fetchingDevice.push(i.hardwareId);
    }
    this.reFetchDeviceInfo();
  }

  reCheckFetchStatus(data: string) {
    this.fetchingDate.push(data);
    this.reFetchDateInfo();
  }

  reFetchDateInfo(first: number = 1, second: number = 2) {
    if (this.fetchingDate.length) {
      let time: number = first + second;
      time = time > 8 ? 8 : time;
      if (time >= 2) {
        setTimeout(() => {
          this.checkFetchDate(second, time);
        }, time * 1000);
      } else {
        this.reFetchDateInfo(second, time);
      }
    }
  }

  checkFetchDate(fibosCountFirst: number = 1, fibosCountSecond: number = 2) {
    const data: FetchDate = new FetchDate();
    data.customerId = Number(this.filterModel.customerId);
    data.siteId = Number(this.filterModel.siteId);
    data.datePerformed = this.datePipe.transform(this.filterModel.datePerformed, AppConstants.fullDateFormat);
    data.datesPerformed = this.fetchingDate;
    data.hardwareIds = [];
    for (const i of this.dataTableInformationList.devices) {
      data.hardwareIds.push(i.hardwareId);
    }
    this.subscription.add(
      this.availabilityDataTableService.refetchDataTablesBtDate(data).subscribe({
        next: (res: DateFetchStatus) => {
          if (res.fetched.length) {
            for (const i of res.fetched) {
              for (const j of i.hardwareFetched) {
                const tempData = [...this.dataTableInformationList.devicesData];
                for (const k of this.dataTableInformationList.devicesData) {
                  k.data[k.data.findIndex(x => k.label === j.calenderLabel && x.hardwareId === j.hardwareId)] = j;
                }
                this.dataTableInformationList.devicesData = [...tempData];
              }
              this.dataTableInformationList.devicesData.find(x => x.label === i.date).isFetching = false;
              if (this.activeTab === 'Re-Fetch Status') {
                this.getReFetchStatus();
              }
              if (res.fetching.length === 0) {
                this.alertService.showSuccessToast(`Date's fetching is completed.`);
              }
            }
          }
          if (res.fetching.length) {
            this.fetchingDate = res.fetching;
            this.reFetchDateInfo(fibosCountFirst, fibosCountSecond);
          } else {
            this.fetchingDate = [];
          }
        }
      })
    );
  }

  checkDateFetchStatus() {
    const data = this.dataTableInformationList.devicesData.filter(x => x.isFetching);
    for (const i of data) {
      if (i.isFetching) {
        this.fetchingDate.push(i.label);
      }
    }
    this.reFetchDateInfo();
  }

  selectFile() {
    this.fileInput.nativeElement.click();
  }

  importData(files) {
    if (files.length > 0) {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('uploadedFile', files[0] as File);
      formData.append('customerId', this.filterModel.customerId.toString());
      formData.append('portfolioId', this.filterModel.portfolioId.toString());
      formData.append('siteId', this.filterModel.siteId.toString());
      this.subscription.add(
        this.availabilityDataTableService.importDataTable(formData).subscribe({
          next: (res: any) => {
            this.alertService.showSuccessToast(res.message);
            this.fileInput.nativeElement.value = '';
            this.loading = false;
            this.changeTab(null);
          },
          error: (e: any) => {
            this.loading = false;
            this.alertService.showWarningToast(e.message);
          }
        })
      );
    }
  }

  showExclusion() {
    this.router.navigateByUrl('/entities/availability/exclusions');
    this.filterModel.date = { start: null, end: null };
    this.filterModel.date.start = this.filterModel.date.end = this.filterModel.datePerformed;
    this.availabilityService.setViewFilter(this.filterModel);
  }

  previousDate() {
    this.filterModel.datePerformed = this.dateService.addDay(new Date(this.filterModel.datePerformed), -1);
    this.generateData();
  }

  nextDate() {
    this.filterModel.datePerformed = this.dateService.addDay(new Date(this.filterModel.datePerformed), +1);
    this.generateData();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
