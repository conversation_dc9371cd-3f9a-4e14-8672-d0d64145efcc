import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { WorkorderAddEditComponent } from './workorder-add-edit/workorder-add-edit.component';
import { WorkorderListingComponent } from './workorder-listing/workorder-listing.component';
import { ROLE_TYPE } from '../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: WorkorderListingComponent,
    data: { pageTitle: 'Work Orders' }
  },
  {
    path: 'add',
    component: WorkorderAddEditComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Add Workorder'
    }
  },
  {
    path: 'edit/:id',
    component: WorkorderAddEditComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.ANALYST],
      pageTitle: 'Edit Workorder'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class WorkorderManagementRoutingModule {}
