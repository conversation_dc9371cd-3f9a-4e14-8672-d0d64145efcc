import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AddEditTemplateComponent } from './add-edit-template/add-edit-template.component';
import { CustomFormsLandingPageComponent } from './custom-forms-landing-page/custom-forms-landing-page.component';
import { FormAnalyticsComponent } from './form-analytics/form-analytics.component';
import { MyFormsListingPageComponent } from './my-forms-listing-page/my-forms-listing-page.component';
import { TemplateListingComponent } from './template-listing/template-listing.component';
import { ROLE_TYPE } from '../../../@shared/enums';

const routes: Routes = [
  {
    path: '',
    component: CustomFormsLandingPageComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'Custom Forms'
    }
  },
  {
    path: 'templates',
    component: TemplateListingComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'QEST Templates'
    }
  },
  {
    path: 'add-template',
    component: AddEditTemplateComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'Add QEST Template'
    }
  },
  {
    path: 'edit-template/:id',
    component: AddEditTemplateComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'Edit QEST Templates'
    }
  },
  {
    path: 'forms',
    component: MyFormsListingPageComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'QEST Forms'
    }
  },
  {
    path: 'add-form',
    component: AddEditTemplateComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'Add QEST Form'
    }
  },
  {
    path: 'edit-form/:id',
    component: AddEditTemplateComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'Edit QEST Form'
    }
  },
  {
    path: 'fill-form/:id',
    component: AddEditTemplateComponent
    // data: { permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH, ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.ANALYST], pageTitle: 'QEST Form' }
  },
  {
    path: 'fill-mobile-form',
    component: AddEditTemplateComponent
  },
  {
    path: 'form-analytics',
    component: FormAnalyticsComponent,
    data: {
      permittedRoles: [ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.FIELDTECH],
      pageTitle: 'QEST Form Analytics'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomFormsRoutingModule {}
