import { DatePipe, Location } from '@angular/common';
import { Component, HostListener, Input, OnDestroy, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NbDialogService } from '@nebular/theme';
import * as _moment from 'moment/moment';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { MessageService } from 'primeng/api';
import { BehaviorSubject, catchError, concatMap, finalize, forkJoin, Observable, of, Subject, Subscription, takeUntil, tap } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { ImageDropboxGalleryComponent } from '../../../@shared/components/image-dropbox-gallery/image-dropbox-gallery.component';
import { AppConstants } from '../../../@shared/constants';
import { MultiSelectDropdown } from '../../../@shared/models/dropdown.model';
import { GenerateReport, NewReport, ReportTypes } from '../../../@shared/models/report.model';
import { ChunkUploadProgressDetails } from '../../../@shared/models/share';
import {
  MultipleFileUpload,
  listOfOEMDocument,
  ReportDocument,
  WorkOrder,
  WorkOrderAssFre,
  WorkOrderData
} from '../../../@shared/models/workOrder.model';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { DropboxImageGalleryService } from '../../../@shared/services/dropbox-image-gallery.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { DialogComponent } from '../../modal-overlays/dialog/dialog.component';
import { CustomFormService } from '../../operations/custom-forms/custom-form.service';
import { PageOpenFromConstant, QESTFormTemplateTypes } from '../../operations/custom-forms/custom-forms.model';
import { ReportService } from '../../report/report.service';
import { ShareModelComponent } from '../../report/share-model/share-model.component';
import { jhaDetail } from '../../ticket-management/ticket.model';
import { TicketService } from '../../ticket-management/ticket.service';
import { ReschedulerModalComponent } from '../rescheduler-modal/rescheduler-modal.component';
import {
  AvailableFormsList,
  AvailableFormsResponse,
  AvailableModuleTorqueFormsResponse,
  DownloadAllViewFormResponse,
  FormStatus,
  GetCompletedFormCount,
  ModuleTorqueFormsByWorkOrder,
  QESTFillFormRedirection,
  SelectedInverters,
  SelectInverterList,
  UploadedFormsData,
  ViewFormsList,
  ViewFormsResponse,
  WO_STATUSES,
  WorkOrderSchedule,
  WOTemplateOpenModelForFormConstant,
  WOTemplateOpenModelForFormConstantValues
} from '../workorder.model';
import { WorkorderService } from '../workorder.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-workorder-add-edit',
  templateUrl: './workorder-add-edit.component.html',
  styleUrls: ['./workorder-add-edit.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class WorkorderAddEditComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  moment = (_moment as any).default ? (_moment as any).default : _moment;
  workOrders: WorkOrder = new WorkOrder();
  loading = false;
  todayDate = new Date();
  workOrderDatas: WorkOrderData[];
  workOrderDatasClone: WorkOrderData[];
  workOrderDatasDetailModel: WorkOrderData = new WorkOrderData();
  isEdit = false;
  isPerformance = false;
  isWOGenerated = false;
  fieldTechs: any[] = [];
  workOrderStatus: any[] = [];
  totalWO: number;
  modalRef: BsModalRef;
  deleteAllWorkOrder: boolean;
  deletesingleWO: boolean;
  assessmentType: string;
  frequncyType: string;
  portfolioNames: string;
  customerNames: string;
  siteNames: string;
  IsMLT = false;
  workOrderNumber: string;
  isDashboard = false;
  workOrder: WorkOrderData[] = [];
  recreateWorkOrder: WorkOrderData[];
  workOrderId: WorkOrderData[];
  fieldTechs1 = [{ id: 0, name: 'Select' }];
  reportAutherdata: any = [];
  primaryFieldtTechdata: MultiSelectDropdown[] = [];
  zoneListData: MultiSelectDropdown[] = [];
  fieldTechsdata: MultiSelectDropdown[] = [];
  disabled = false;
  customerId: number;
  portfolioId: number;
  siteId: number;
  reportType: number;
  // reportId: string;
  user = this.storageService.get('user');
  fullDateFormat = AppConstants.fullDateFormat;
  pdfFileList: MultipleFileUpload[] = [];
  jhaDetail: jhaDetail[] = [];
  workorderSchedules: WorkOrderSchedule[] = [];
  selectedWoStatus = [];
  oilElectricalReportDocuments: ReportDocument[] = [];
  public rtStatus = [
    { text: 'Draft', value: 1 },
    { text: 'Complete', value: 2 }
  ];
  activeTab = 'Photos';
  public scanTypeStatus = [
    { text: 'Select', value: 0 },
    { text: 'Fixed Wing', value: 1 },
    { text: 'Drone', value: 2 }
  ];
  public tentativeMonthList = [
    { text: `-`, value: '' },
    { text: 'January', value: 1 },
    { text: 'February', value: 2 },
    { text: 'March', value: 3 },
    { text: 'April', value: 4 },
    { text: 'May', value: 5 },
    { text: 'June', value: 6 },
    { text: 'July', value: 7 },
    { text: 'August', value: 8 },
    { text: 'September', value: 9 },
    { text: 'October', value: 10 },
    { text: 'November', value: 11 },
    { text: 'December', value: 12 }
  ];
  isportfolioUser = false;
  jhaMap: any[] = [];
  filteredJHAIds: number[] = [];
  rescheduleActiveIds = [];
  alsoUpdateTheWO: boolean;
  @Input() idleTime = 1000 * 60 * 28;
  showModal = false;
  @ViewChild('openModalButton', { static: true }) openModalButton;
  @ViewChild('imageInput') imageInput;
  @ViewChild('videoInput') videoInput;
  responsiveOptions;
  userActivity;
  logoutTimer;
  countDown;
  counter = 120;
  logoutCounter = 1000 * 60 * 2;
  tick = 1000;
  isAutoLogout = true;
  isWOLocked = false;
  isWOBeingEdited = false;
  userRole: string;
  videoPaginationParams = {
    currentPage: 1,
    itemsCount: 10,
    pageSize: 10
  };
  @ViewChild('startFormUsingInverter', { static: false }) startFormUsingInverter: TemplateRef<any>;
  @ViewChild('availableFormList', { static: false }) availableFormList: TemplateRef<any>;
  @ViewChild('availableModuleTorqueForms', { static: false }) availableModuleTorqueForms: TemplateRef<any>;
  @ViewChild('selectZoneModal', { static: false }) selectZoneModal: TemplateRef<any>;
  @ViewChild('viewModuleTorqueFormsTemplate', { static: false }) viewModuleTorqueFormsTemplate: TemplateRef<any>;
  @ViewChild('startFormUsingZone', { static: false }) startFormUsingZone: TemplateRef<any>;
  @ViewChild('viewFormsTemplate', { static: false }) viewFormsTemplate: TemplateRef<any>;
  @ViewChild('viewSummaryFormsTemplate', { static: false }) viewSummaryFormsTemplate: TemplateRef<any>;
  @ViewChild('viewTPMFormsTemplate', { static: false }) viewTPMFormsTemplate: TemplateRef<any>;
  @ViewChild('historyModalTemplate', { static: false }) historyModalTemplate: TemplateRef<any>;
  pagination = {
    [WOTemplateOpenModelForFormConstant.AVAILABLE_FORM]: {
      pageSize: 100,
      itemsCount: 100,
      totalCount: 0,
      currentPage: 1
    },
    [WOTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]: {
      pageSize: 100,
      itemsCount: 100,
      totalCount: 0,
      currentPage: 1
    },
    [WOTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]: {
      pageSize: 100,
      itemsCount: 100,
      totalCount: 0,
      currentPage: 1
    },
    [WOTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS]: {
      pageSize: 100,
      itemsCount: 100,
      totalCount: 0,
      currentPage: 1
    },
    [WOTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]: {
      pageSize: 100,
      itemsCount: 100,
      totalCount: 0,
      currentPage: 1
    },
    [WOTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]: {
      pageSize: 100,
      itemsCount: 100,
      totalCount: 0,
      currentPage: 1
    }
  };
  searchText: string = '';
  searchInverter: string = '';
  qestFormId: number = null;
  isPmReviewForm: boolean = false;
  progressBarCount: number = 0;
  qestWorkOrderId: number = null;
  isStartAll: boolean = false;
  formsListingData: AvailableFormsList[] = [];
  moduleTorqueFormsListingData: ModuleTorqueFormsByWorkOrder;
  inverterListData: SelectInverterList[] = [];
  zoneListDataForSelection: any[] = [];
  mapedFormsForZone: any[] = [];
  uploadedFormsData: UploadedFormsData = {
    [WOTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]: [],
    [WOTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]: [],
    [WOTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]: [],
    [WOTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]: []
  };
  formEditNoteHistory: any;
  viewFromInfo: ViewFormsList;
  availableFormModalRef: BsModalRef;
  availableModuleTorqueFormModalRef: BsModalRef;
  selectInverterModalRef: BsModalRef;

  selectZoneModalRef: BsModalRef;
  startFormUsingZoneRef: BsModalRef;
  viewModuleTorqueFormsRef: BsModalRef;

  viewFromModalRef: BsModalRef;
  historyModalRef: BsModalRef;
  startFormByInverterModalRef: BsModalRef;
  selectAllBtnText = 'Select All';
  selectedInverters: SelectedInverters[] = [];
  selectedZones: any[] = [];
  isAnyFormStartOrComplete = false;
  formStatuses = FormStatus;
  backToWhichModal: string = '';
  isFromQESTForm: boolean = false;
  isModalOpen = false;
  WO_STATUSES = WO_STATUSES;
  // completedFormMapId: CompletedFormMapId = {
  //   [WOTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]: [],
  //   [WOTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]: [],
  //   [WOTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]: []
  // };
  completedFormMapId: number[] = [];
  private apiCount$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  pageOpenFromConstant = PageOpenFromConstant;
  // completedFormMapId: CompletedFormMapId = {
  //   [WOTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]: [],
  //   [WOTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]: [],
  //   [WOTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]: []
  // };
  woTemplateOpenModelForFormConstant = WOTemplateOpenModelForFormConstant;
  MTRQprogressBarCount: number;
  backToDashboard: boolean;
  chunkUploadInProgress: boolean;
  remainingChunks$ = new BehaviorSubject<number>(0);
  private destroy$ = new Subject<void>();
  newReportBtnText = 'Start New Report';
  allowStartNewReport = false;
  newReportData: NewReport = new NewReport();
  isReportExistForWO = false;
  generateNewReportData: GenerateReport;
  expandedWoId: number;
  currentWOStatus = WO_STATUSES.NOT_FOUND.id;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly workorderService: WorkorderService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly dialogService: NbDialogService,
    private readonly reportService: ReportService,
    private readonly commonService: CommonService,
    private readonly storageService: StorageService,
    private readonly _location: Location,
    private readonly ticketService: TicketService,
    public readonly datePipe: DatePipe,
    private readonly messageService: MessageService,
    public readonly customFormService: CustomFormService,
    public readonly dropBoxService: DropboxImageGalleryService
  ) {}

  ngOnInit() {
    this.apiCount$.subscribe({
      next: count => {
        this.loading = count > 0 ? true : false;
      }
    });
    this.setTimeout();
    this.userRole = this.storageService.get('user').authorities[0];
    this.responsiveOptions = [
      {
        breakpoint: '1440px',
        numVisible: 8,
        numScroll: 1
      },
      {
        breakpoint: '1024px',
        numVisible: 6,
        numScroll: 1
      },
      {
        breakpoint: '768px',
        numVisible: 4,
        numScroll: 1
      },
      {
        breakpoint: '560px',
        numVisible: 2,
        numScroll: 1
      }
    ];

    this.route.queryParams.subscribe(params => {
      this.workOrders.id = Number(params['id']);
      this.workOrders.assementType = params['assementType'];
      this.workOrders.frequncyType = params['frequencyType'];
      this.workOrders.selectedWorkOrderId = params['workOrderId'];
      this.workOrderNumber = params['workOrderNumber'];
      this.backToWhichModal = params['backToWhere'];
      this.qestWorkOrderId = Number(params['workOrderId']);
      this.qestFormId = Number(params['formId']);
      this.backToDashboard = params['backToDashboard'];
      this.isPmReviewForm = params['isPmReviewForm'] === 'true' ? true : params['isPmReviewForm'] === 'false' ? false : false;
    });
    if (this.workOrders.assementType == 'PR') {
      this.isPerformance = true;
    }

    if (this.backToWhichModal === PageOpenFromConstant.FILL_INITIAL_SR) {
      this.removeBackToWhereParam();
    }

    if (this.backToWhichModal === PageOpenFromConstant.FILL_INITIAL_TPM) {
      this.removeBackToWhereParam();
    }

    this.checkWOLockingStatus();

    this.getWOStatus();
    this.isportfolioUser = checkAuthorisations([ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.MANAGER]);

    this.subscription.add(
      this.customFormService.onPreviewModalClose.subscribe(result => {
        if (result && this.customerId && (this.router.url.includes('workorders/add') || this.router.url.includes('workorders/edit'))) {
          this.openTemplateModal(
            this.availableFormList,
            this.woTemplateOpenModelForFormConstant.AVAILABLE_FORM,
            this.qestWorkOrderId,
            this.qestFormId
          );
          this.getCompletedFormCount();
          this.customFormService.onPreviewModalClose.next(false);
        }
      })
    );
  }

  checkWOLockingStatus() {
    const data = {
      id: this.workOrders.id,
      assementType: this.workOrders.assementType,
      isReLockWo: false
    };

    this.workorderService.woLockingStatus(data).subscribe({
      next: res => {
        this.isWOBeingEdited = res.islocked;
        if (res.islocked) {
          this.messageService.add({ key: 'woToast', severity: 'info', detail: res.message, sticky: true, closable: false });
        } else {
          this.lockUnlockWO(data);
        }
      }
    });
  }

  reLockWO() {
    const data = {
      id: this.workOrders.id,
      assementType: this.workOrders.assementType,
      isReLockWo: true
    };

    this.workorderService.lockWO(data).subscribe({
      next: () => this.removeApiCount(),
      error: () => this.removeApiCount()
    });
  }

  lockUnlockWO(data, isLocked = false) {
    this.addApiCount();

    if (!isLocked) {
      this.workorderService.lockWO(data).subscribe({
        next: res => {
          this.isWOLocked = true;
          this.removeApiCount();
        },
        error: () => this.removeApiCount()
      });
    } else {
      this.workorderService.unlockWO(data).subscribe({
        next: res => {
          this.removeApiCount();
        },
        error: () => this.removeApiCount()
      });
    }
  }

  // get All Portfolio Access
  getreportauthor() {
    const data = [];
    this.fieldTechs.forEach(element => {
      if (element.id !== 0) {
        data.push(element);
      }
    });
    this.reportAutherdata = data;
    this.primaryFieldtTechdata = data;
    this.fieldTechsdata = data;
  }

  selectAndDeselectAllJHA(workOrderData, isSelect) {
    if (isSelect) {
      if (this.filteredJHAIds.length) {
        workOrderData.jhaMap = [...new Set([...workOrderData.jhaMap, ...JSON.parse(JSON.stringify(this.filteredJHAIds))])];
      } else {
        workOrderData.jhaMap = this.jhaDetail.map(jha => jha.id);
      }
    } else {
      if (this.filteredJHAIds.length) {
        workOrderData.jhaMap = workOrderData.jhaMap.filter(x => !this.filteredJHAIds.includes(x));
      } else {
        workOrderData.jhaMap = [];
      }
    }

    let objIndex = this.workOrderDatas.findIndex(obj => obj.id == workOrderData.id);

    this.workOrderDatas[objIndex].jhaMap = JSON.parse(JSON.stringify(workOrderData.jhaMap));
  }

  getWorkOrder(): Promise<void> {
    this.addApiCount();
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.workorderService.getWorkOrder(this.workOrders).subscribe({
          next: (res: WorkOrderAssFre) => {
            const workOrderDatas: WorkOrderData[] = res.workOrders;
            if ((res.workOrders[0]?.id && !this.backToWhichModal) || (this.workOrders.selectedWorkOrderId && !this.backToWhichModal))
              this.updateWorkOrderId(this.workOrders.selectedWorkOrderId ?? res.workOrders[0]?.id);
            this.oilElectricalReportDocuments = res.oilElectricalReportDocuments;
            this.deleteAllWorkOrder = res.isWorkOrderDeleted;
            this.assessmentType = this.workOrders.assementType === 'TPM' ? 'Tracker Preventative Maintenance' : res.assesmentType;
            this.frequncyType = this.workOrders.frequncyType === 'Annual' ? 'Annual' : res.frequncyType;
            this.customerNames = res.customerNane;
            this.customerId = res.customerId;
            this.portfolioId = res.portfolioId;
            this.siteId = res.siteId;
            this.portfolioNames = res.portfolioName;
            this.siteNames = res.siteName;
            this.reportType = res.assessmentId;
            this.fieldTechs = res.dropdownDtos && res.dropdownDtos.length ? res.dropdownDtos : this.fieldTechs1;
            this.zoneListData = res?.zoneList && res?.zoneList.length ? res.zoneList : [];
            this.totalWO = workOrderDatas.length;
            if (!workOrderDatas.length) {
              this.isWOGenerated = true;
            } else {
              this.isWOGenerated = false;
            }

            if (res.frequncyType === 'Dated/Multiple') {
              this.IsMLT = true;
            } else {
              this.IsMLT = false;
            }
            this.getreportauthor();
            this.getJhaList();
            this.selectedWoStatus = [];
            workOrderDatas.forEach(element => {
              element.workOrderSchedules = [];
              if (element.dateScheduled) {
                element.dateScheduled = new Date(element.dateScheduled);
              }
              if (element.dueDate) {
                element.dueDate = new Date(element.dueDate);
              }
              if (element.datePerformed) {
                element.datePerformed = new Date(element.datePerformed);
              }
              if (element.rescheduleDate) {
                element.rescheduleDate = new Date(element.rescheduleDate);
              }
              if (element.reportCompleteDate) {
                element.reportCompleteDateDisplay = new Date(element.reportCompleteDateDisplay);
              }
              if (element.workCompleteDate) {
                element.workCompleteDateDisplay = new Date(element.workCompleteDateDisplay);
              }
              element.expanded = false;
              if (!element.fieldTechs) {
                element.fieldTechs = element.fieldTechList.map(x => x.userId);
              } else {
                element.fieldTechs = [];
              }
              if (!element.reportAuthors) {
                element.reportAuthors = element.reportAuthorList.map(x => x.userId);
              } else {
                element.reportAuthors = [];
              }
              if (!element.scanType) {
                element.scanType = this.scanTypeStatus[0].value;
              }
              if (!element.reportStatus) {
                if (this.rtStatus.length > 0) {
                  element.reportStatus = this.rtStatus[0].text;
                }
              }
              if (!element.woStatus) {
                if (this.workOrderStatus.length > 0) {
                  element.woStatus = this.workOrderStatus[0].id;
                }
              }
              this.selectedWoStatus.push(element.woStatus);
            });
            if (this.expandedWoId) {
              workOrderDatas.forEach(element => {
                if (element.id === this.expandedWoId) {
                  this.currentWOStatus = element.woStatus;
                  element.expanded = true;
                }
              });
              this.isDashboard = true;
            } else if (workOrderDatas.length > 0 && this.workOrderNumber) {
              workOrderDatas.forEach(element => {
                if (element.workOrderNumber === this.workOrderNumber) {
                  this.currentWOStatus = element.woStatus;
                  element.expanded = true;
                }
              });
            } else if (workOrderDatas.length > 0) {
              this.currentWOStatus = workOrderDatas[0].woStatus;
              workOrderDatas[0].expanded = true;
            }
            this.workOrderDatasClone = JSON.parse(JSON.stringify(workOrderDatas));
            this.workOrderDatas = workOrderDatas.map(WO => {
              if (WO.isDeleted) {
                const newWOValues = new WorkOrderData();
                return { ...WO, isDeleted: true, workOrderNumber: WO.workOrderNumber, expanded: WO.expanded, id: WO.id };
              }
              return WO;
            });
            setTimeout(() => {
              this.gotoScroll(this.workOrderNumber);
            }, 0);
            if (this.backToWhichModal === this.pageOpenFromConstant.INVERTER_MODEL_SCREEN) {
              this.isFromQESTForm = true;
              this.getAvailableQESTFormByTemplateType(this.woTemplateOpenModelForFormConstant.AVAILABLE_FORM);
              this.openModalForStartFormByInverter(this.startFormUsingInverter);
            } else if (this.backToWhichModal === this.pageOpenFromConstant.VIEW_MODEL_SCREEN) {
              this.isFromQESTForm = true;
              this.openTemplateModal(
                this.viewFormsTemplate,
                this.woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL,
                this.qestWorkOrderId,
                this.qestFormId
              );
            } else if (this.backToWhichModal === this.pageOpenFromConstant.VIEW_SUMMARY_MODEL_SCREEN) {
              this.isFromQESTForm = true;
              this.openTemplateModal(
                this.viewSummaryFormsTemplate,
                this.woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL,
                this.qestWorkOrderId,
                this.qestFormId
              );
            } else if (this.backToWhichModal === this.pageOpenFromConstant.VIEW_TPM_MODEL_SCREEN) {
              this.isFromQESTForm = true;
              this.openTemplateModal(
                this.viewTPMFormsTemplate,
                this.woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL,
                this.qestWorkOrderId,
                this.qestFormId
              );
            } else if (this.backToWhichModal === this.pageOpenFromConstant.FILL_INITIAL_SR) {
              this.isFromQESTForm = true;
            } else if (this.backToWhichModal === this.pageOpenFromConstant.FILL_INITIAL_TPM) {
              this.isFromQESTForm = true;
            } else if (this.backToWhichModal === this.woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL) {
              this.isFromQESTForm = true;
              this.openTemplateModal(
                this.viewModuleTorqueFormsTemplate,
                this.woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL,
                this.qestWorkOrderId,
                this.qestFormId
              );
            } else if (this.backToWhichModal === this.pageOpenFromConstant.ZONE_MODEL_SCREEN) {
              this.isFromQESTForm = true;
              this.openModalForStartFormByZone(this.startFormUsingZone, null, this.qestWorkOrderId);
            }
            this.removeApiCount();
            resolve();
          },
          error: e => {
            this.removeApiCount();
            reject();
          }
        })
      );
    });
  }

  getJhaList() {
    this.subscription.add(
      this.ticketService.getJhaList(this.siteId).subscribe({
        next: res => {
          this.jhaDetail = res;
          this.removeApiCount();
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  getWOStatus() {
    this.addApiCount();
    this.subscription.add(
      this.workorderService.getWorkOrderStatus(this.isPerformance).subscribe({
        next: res => {
          if (res) {
            res.forEach(element => {
              this.workOrderStatus.push(element);
            });
            this.removeApiCount();
            this.getWorkOrder();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  addLink(index: number) {
    if (!this.workOrderDatas[index].listOfOEMDocument) {
      this.workOrderDatas[index].listOfOEMDocument = [];
    }
    const newUrl = new listOfOEMDocument();
    this.workOrderDatas[index].listOfOEMDocument.push(newUrl);
  }

  deletelink(i: number, linkIndex: number, woId: number, id: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        const url = this.workOrderDatas[i].listOfOEMDocument.find(cert => cert.id == id && id !== 0);
        if (url) {
          this.subscription.add(
            this.workorderService.deleteOemDocument(url.id, woId).subscribe({
              next: resData => {
                this.workOrderDatas[i].listOfOEMDocument.splice(linkIndex, 1);
                this.alertService.showSuccessToast(resData.message);
              },
              error: err => {
                this.loading = false;
              }
            })
          );
        } else {
          this.workOrderDatas[i].listOfOEMDocument.splice(linkIndex, 1);
        }
      }
    });
  }

  // Create  Work Order
  createWO(isAddNewWO = false) {
    this.addApiCount();
    const param = {
      ...this.workOrders,
      isAddNewWO
    };
    if (this.workOrders.id !== 0) {
      this.subscription.add(
        this.workorderService.createWorkOrder(param).subscribe({
          next: res => {
            this.workOrders.selectedWorkOrderId = null;
            this.getWorkOrder();

            if (res.message === 'Work order is already exist for this site.') {
              this.alertService.showErrorToast(res.message);
            } else {
              this.alertService.showSuccessToast(res.message);
            }

            this.removeApiCount();
          },
          error: e => {
            this.removeApiCount();
          }
        })
      );
    }
  }

  restoreAllWO() {
    this.addApiCount();
    if (this.workOrders.id !== 0) {
      const queryFilter = {
        assesmentType: this.workOrders.assementType,
        assesmentId: this.workOrders.id
      };
      this.subscription.add(
        this.workorderService.restoreAllWorkOrder(queryFilter).subscribe({
          next: res => {
            this.getWorkOrder();
            this.alertService.showSuccessToast(res.message);
            this.removeApiCount();
          },
          error: e => {
            this.removeApiCount();
          }
        })
      );
    }
  }

  onSelectContractor(event, index) {
    if (event) {
      this.workOrderDatas[index].fieldTechs.push(event.id);
    }
  }

  // Update Work Order
  UpdateWO(id, reportId) {
    this.addApiCount();
    this.workOrderDatas.forEach(
      (element, idx) => {
        if (element.id === id) {
          if (Array.isArray(element.listOfOEMDocument)) {
            element.listOfOEMDocument = element.listOfOEMDocument.filter(
              doc => doc.documentLink != null && doc.documentLink !== '' && !this.isLinkInvalid(doc)
            );
            element.listOfOEMDocument.forEach(doc => {
              if (doc.isEditing) {
                delete doc.isEditing;
                delete doc.date;
              }
            });
          }
          this.workOrders.selectedWorkOrderId = id;
          const data = { ...element };
          if (
            data.fieldTechs.length === 0 &&
            data.assesmentType !== 'PR' &&
            data.assesmentType !== 'AS' &&
            (data.woStatus === WO_STATUSES.REPORT_COMPLETE.id ||
              data.woStatus === WO_STATUSES.FIELD_WORK_COMPLETE.id ||
              (data.woStatus === WO_STATUSES.FIELD_WORK_PARTIALLY_COMPLETE.id &&
                this.pdfFileList.some(element => !element.isOilElectricalReport)))
          ) {
            this.removeApiCount();
            if (data.assesmentType === 'VGT') {
              this.alertService.showErrorToast('Please select contractor.');
            } else {
              this.alertService.showErrorToast('Please select a Field Tech to continue.');
            }
          } else {
            if (data.assesmentType === 'PR' || data.assesmentType === 'VGT' || data.assesmentType === 'AS') {
              data.reportAuthors = [];
            }
            data.woStatus = Number(data.woStatus);
            if (data.datePerformed) {
              data.datePerformed = this.datePipe.transform(data.datePerformed, AppConstants.fullDateFormat); //this.moment(data.datePerformed).format();
            }
            if (data.dateScheduled) {
              data.dateScheduled = this.datePipe.transform(data.dateScheduled, AppConstants.fullDateFormat);
            }
            if (data.dueDate) {
              data.dueDate = this.datePipe.transform(data.dueDate, AppConstants.fullDateFormat);
            }
            if (data.rescheduleDate) {
              data.rescheduleDate = this.datePipe.transform(data.rescheduleDate, AppConstants.fullDateFormat);
            }
            // if (data.reportCompleteDate) {
            //   data.reportCompleteDate = this.datePipe.transform(data.reportCompleteDate, AppConstants.fullDateFormat);
            // }
            // if (data.workCompleteDate) {
            //   data.workCompleteDate = this.datePipe.transform(data.workCompleteDate, AppConstants.fullDateFormat);
            // }

            // check the photos link field contains a valid url using a regex
            if (data.photosLink && data.photosLink !== '') {
              this.checkPhotosLinkURL(data, idx);
              // // Regular expression to validate URL
              if (this.workOrderDatas[idx].inValidUrl) {
                this.alertService.showErrorToast('Invalid photos link URL');
                this.removeApiCount();
                return;
              }
            }
            // if (data.assesmentType === 'SV' || data.assesmentType === 'MVPM' || data.assesmentType === 'VGT') {
            //   data.jhaMap = this.jhaMap;
            // }
            this.updateWOAPICall(data, id);
          }
        }
      },
      error => {
        this.removeApiCount();
      }
    );
  }

  showWOStatusChangesPrompt(): Promise<boolean> {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        hideCloseIcon: true,
        confirmBtnText: 'Yes, Update',
        cancelBtnText: 'No, Keep Current',
        message: 'Are you sure you want to change the status of this Work Order?'
      }
    };
    return new Promise(res => {
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => res(result));
    });
  }

  updateWOAPICall(data: WorkOrderData, id: number): void {
    this.subscription.add(
      this.workorderService.updateWorkOrder(data).subscribe({
        next: async res => {
          if (res.status === 417) {
            this.removeApiCount();
            data.isUpdateCost = await this.showWOCostDataLossPrompt();
            this.addApiCount();
            this.updateWOAPICall(data, id);
          } else if (res.status === 428) {
            this.loading = false;
            const shouldUpdateStatus = await this.showWOStatusChangesPrompt();
            data.isSkipValidation = shouldUpdateStatus;

            if (shouldUpdateStatus) {
              this.removeApiCount();
              this.addApiCount();
              this.updateWOAPICall(data, id);
            } else {
              this.modalRef.hide();
              this.removeApiCount();
              this.getWorkOrder();
            }
          } else {
            if (data.dateScheduled || this.alsoUpdateTheWO) this.alsoUpdateTheWO = false;
            if (this.pdfFileList.length) {
              this.getUploadedFiles(this.pdfFileList, data.reportId);
            } else {
              this.alertService.showSuccessToast(res.message);
              this.removeApiCount();
            }
            this.getWorkOrder();
            this.getAllReschedulesByWorkOrder(id);
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  showWOCostDataLossPrompt(): Promise<boolean> {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        hideCloseIcon: true,
        confirmBtnText: 'Yes, Update',
        cancelBtnText: 'No, Keep Current',
        message: 'The cost in this Work Order doesn’t match the Site Info section. Would you like to update it to match?'
      }
    };
    return new Promise(res => {
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => res(result));
    });
  }

  checkPhotosLinkURL(workorderData, idx) {
    workorderData.inValidUrl = false;
    if (workorderData.photosLink && workorderData.photosLink !== '') {
      // check if the photos link starts from https if not then append https://
      if (!workorderData.photosLink.startsWith('http')) {
        workorderData.photosLink = 'https://' + workorderData.photosLink;
      }
      // Regular expression to validate URL
      const urlRegex = /^(https?:\/\/|www\.)[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&\/\/=]*)$/;
      this.workOrderDatas[idx].inValidUrl = !urlRegex.test(workorderData.photosLink);
      workorderData.inValidUrl = this.workOrderDatas[idx].inValidUrl;
    }
  }

  // Delete Assessment
  onDelete() {
    const isReportGenerated = this.checkReportGeneratedStatus(this.workOrderDatas);
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete all work order?',
        isFromWorkOrder: isReportGenerated,
        warningMassage: 'Work orders are associated with the reports. Once deleted, system will not be able to recover the deleted reports.'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        // 3961: the delete wo will now take only the wo ids
        const woIdsToBeRemoved = this.workOrderDatas.filter(wo => !wo.isDeleted).map(item => item.id);
        this.subscription.add(
          this.workorderService.deleteWorkOrder(woIdsToBeRemoved).subscribe({
            next: res => {
              if (res) {
                this.getWorkOrder();
                this.alertService.showSuccessToast(res.message);
              }
            },
            error: e => {
              this.removeApiCount();
            }
          })
        );
      }
    });
  }

  checkReportGeneratedStatus(workOrders: WorkOrderData[]) {
    for (const item of workOrders) {
      if (item.isReportGenreated === true) {
        return true;
      }
    }
    return false;
  }

  open() {
    this.dialogService
      .open(DialogComponent, {
        hasBackdrop: true,
        closeOnBackdropClick: false
      })
      .onClose.subscribe(() => {
        this.getWorkOrder();
      });
  }

  gotoScroll(id) {
    const element = document.getElementById(id);

    if (element) {
      element.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }

  onBack() {
    if (this.backToDashboard) {
      this.router.navigate(['/entities/dashboard']);
    } else if (this.isFromQESTForm && !this.isPmReviewForm) {
      this.router.navigate(['/entities/workorders']);
    } else if (this.isFromQESTForm && this.isPmReviewForm) {
      this.router.navigate(['/entities/dashboard']);
    } else {
      this.router.navigate(['/entities/workorders']);
    }
  }

  DeleteWO(workOrderData) {
    this.workOrder = [];
    this.workOrder.push(workOrderData);
    const isReportGenerated = this.checkReportGeneratedStatus(this.workOrder);
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete this work order?',
        isFromWorkOrder: isReportGenerated,
        warningMassage: 'Work order is associated with the reports. Once deleted, system will not be able to recover the deleted reports.'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.subscription.add(
          this.workorderService.deleteWorkOrder([workOrderData.id]).subscribe({
            next: res => {
              if (res) {
                this.expandedWoId = workOrderData.id;
                this.getWorkOrder();
                this.alertService.showSuccessToast(res.message);
              }
            }
          })
        );
      }
    });
  }

  RestoreWO(id) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to restore work order?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.subscription.add(
          this.workorderService.restoreWorkOrder(id).subscribe({
            next: res => {
              if (res) {
                this.getWorkOrder();
                this.alertService.showSuccessToast(res.message);
              }
            }
          })
        );
      }
    });
  }

  RecreateWO(workOrderDataId: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to recreate work order?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        const WOData = this.workOrderDatasClone.find(x => x.id === workOrderDataId);
        this.subscription.add(
          this.workorderService.recreateWorkOrder(WOData).subscribe({
            next: res => {
              if (res) {
                this.getWorkOrder();
                this.alertService.showSuccessToast(res.message);
              }
            }
          })
        );
      }
    });
  }

  createReport(id) {
    this.addApiCount();
    const newReportData: NewReport = new NewReport();
    if (this.reportType !== 11) {
      newReportData.customerId = this.customerId;
      newReportData.portfolioId = this.portfolioId;
      newReportData.siteId = this.siteId;
      newReportData.reportType = this.reportType;
      newReportData.workorderId = id;
      newReportData.reportVersion = 3;
    }
    this.subscription.add(
      this.reportService.createReport(newReportData).subscribe({
        next: res => {
          this.workOrderDatas.find(wo => wo.id === id).reportId = res.id;
          this.removeApiCount();
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  // Upload General Images
  getUpload(files, id, reportId, isOilElectricalReport = false) {
    this.addApiCount();
    // this.reportId = reportId;
    const fileList: MultipleFileUpload[] = [];
    for (const i of files) {
      const fileData: MultipleFileUpload = new MultipleFileUpload();
      fileData.file = i as File;
      fileData.reportLabel = i.name.split('.')[0];
      fileData.isOilElectricalReport = isOilElectricalReport;
      fileList.push(fileData);
    }
    setTimeout(() => {
      this.pdfFileList = this.pdfFileList.concat(fileList);
      if (reportId) {
        this.removeApiCount();
      } else {
        this.removeApiCount();
        this.createReport(id);
      }
    }, 0);
  }

  getUploadedFiles(files: MultipleFileUpload[], reportId) {
    this.addApiCount();
    const fileList: any = [];
    for (const i of files) {
      const formData: FormData = new FormData();
      const allowedExtensions = AppConstants.allowedDocumentsType;
      const fileExtension = i.file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(fileExtension)) {
        this.alertService.showErrorToast('The file is invalid, or not supported.');
        this.removeApiCount();
        return;
      }
      formData.append('file', i.file as File);
      formData.append('reportId', reportId);
      formData.append('reportLabel', i.reportLabel);
      formData.append('isOilElectricalReport', `${i.isOilElectricalReport}`);
      fileList.push(this.reportService.getUplodedReport(formData));
    }
    this.subscription.add(
      forkJoin(fileList).subscribe({
        next: res => {
          if (res) {
            this.alertService.showSuccessToast('The Work Order was updated successfully');
            this.pdfFileList = [];
            this.removeApiCount();
            this.getWorkOrder();
          }
        },
        error: e => {
          this.alertService.showWarningToast('Fail to upload file.');
          this.removeApiCount();
        }
      })
    );
  }

  removeFile(index: number) {
    this.pdfFileList.splice(index, 1);
  }

  sendReportForReview(id: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to submit to customer?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.addApiCount();
        this.subscription.add(
          this.workorderService.sendReportForReview(id).subscribe({
            next: res => {
              if (res) {
                this.alertService.showSuccessToast(res.message);
                this.removeApiCount();
              }
            },
            error: e => {
              this.removeApiCount();
            }
          })
        );
      }
    });
  }

  // PDF/PPt Report URL
  onReportLink(reportLink, ispdf: boolean) {
    if (reportLink) {
      if (ispdf) {
        window.open(reportLink);
      } else {
        window.open(reportLink, '_self');
      }
    }
  }

  copylink = false;

  /* To copy any Text */
  copyText(reportUrl) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-content',
      initialState: {
        message: reportUrl
      }
    };
    this.modalRef = this.modalService.show(ShareModelComponent, ngModalOptions);
  }

  // Track by
  trackByFunction(index: number, element) {
    return element ? index : null;
  }

  // Download Report - PDF
  downloadReport(documentId, originalfileName) {
    this.addApiCount();
    this.reportService.downloadAllPdfReport(documentId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, 'application/pdf');
          link.download = `${originalfileName}`;
          link.click();
          this.removeApiCount();
        } else {
          this.removeApiCount();
        }
      },
      error: e => {
        this.removeApiCount();
      }
    });
  }
  // Download Report - PDF
  downloadReportOilElectrical(documentId, originalfileName) {
    this.addApiCount();
    this.reportService.downloadAllOilElectricalPdfReport(documentId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, 'application/pdf');
          const reportName = originalfileName;
          link.download = `${reportName}.pdf`;
          link.click();
          this.removeApiCount();
        } else {
          this.removeApiCount();
        }
      },
      error: e => {
        this.removeApiCount();
      }
    });
  }

  // Delete Report
  deleteReport(event: any, items: any, index: number, reportType) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this report?' }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        this.addApiCount();
        if (result) {
          if (reportType === 'oilElectricalReport') {
            this.subscription.add(
              this.reportService.deleteUploadedOilElectricalReport(event).subscribe({
                next: res => {
                  this.removeApiCount();
                  if (res) {
                    items.splice(index, 1);
                    this.alertService.showSuccessToast(res.message);
                    this.getWorkOrder();
                  }
                },
                error: e => {
                  this.removeApiCount();
                }
              })
            );
          } else {
            this.subscription.add(
              this.reportService.deleteUploadedReport(event).subscribe({
                next: res => {
                  this.removeApiCount();
                  if (res) {
                    items.splice(index, 1);
                    this.alertService.showSuccessToast(res.message);
                  }
                },
                error: e => {
                  this.removeApiCount();
                }
              })
            );
          }
        } else {
          this.removeApiCount();
        }
      });
    }
  }

  onViewReport(id) {
    if (this.workOrders.assementType === 'SV') {
      this.router.navigate(['/entities/reports/sitevisits/edit/' + id + '/false'], { queryParams: { returnTo: 'Workorder' } });
    } else if (this.workOrders.assementType === 'VGT') {
      this.router.navigate(['/entities/reports/vegetation/edit/' + id + '/false']);
    } else {
      this.router.navigate(['/entities/reports/mvpm/edit/' + id + '/false'], { queryParams: { returnTo: 'Workorder' } });
    }
  }

  onJHASearchFilter(event: any) {
    if (event.term) {
      this.filteredJHAIds = event.items?.map(element => element.id);
    } else {
      this.filteredJHAIds = [];
    }
  }

  rescheduleAccordionChange(event, woDetails) {
    if (!event && !this.workorderSchedules.length) {
      this.getAllReschedulesByWorkOrder(woDetails.id);
    }
  }

  getAllReschedulesByWorkOrder(id) {
    this.addApiCount();
    this.subscription.add(
      this.workorderService.getWorkOrderReschedules(id).subscribe({
        next: reschedules => {
          const workOrder = this.workOrderDatas.find(wod => wod.id === id);
          if (workOrder && reschedules?.length) {
            workOrder.workOrderSchedules = [...reschedules];
            workOrder.isScheduleExpanded = true;
          }
          this.removeApiCount();
        },
        error: () => {
          this.removeApiCount();
        }
      })
    );
  }

  // TODO Get work order audit log
  getWorkOrderAuditLogs(workOrderId, index) {
    this.addApiCount();
    this.subscription.add(
      this.workorderService.getWorkOrderAuditLogs(workOrderId).subscribe({
        next: (res: any) => {
          if (res) {
            this.workOrderDatas[index].workLogAuditHistory = res;
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  deleteReschedulesByWorkOrder(woRescheduleId, workOrderData) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete this Reschedule?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.removeApiCount();
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.addApiCount();
        this.subscription.add(
          this.workorderService.deleteWorkOrderReschedules(woRescheduleId).subscribe({
            next: async () => {
              this.alertService.showSuccessToast('The Reschedule was removed successfully.');
              await this.getWorkOrder();
              this.getAllReschedulesByWorkOrder(workOrderData.id);
              setTimeout(() => {
                this.gotoScroll(this.workOrderNumber + 'reschedule');
              }, 200);
              this.removeApiCount();
            },
            error: () => {
              this.removeApiCount();
            }
          })
        );
      }
    });
  }

  // used to show the rescheduler form in the modal popup
  showRescheduler(workOrderData, schedule) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      ignoreBackdropClick: true,
      initialState: {
        primaryFieldtTechdata: this.primaryFieldtTechdata,
        workOrderData: workOrderData ? JSON.parse(JSON.stringify(workOrderData)) : null,
        rescheduleDetails: schedule ? JSON.parse(JSON.stringify(schedule)) : null
      },
      class: 'rescheduler-modal'
    };
    // check here if need to update the WO or just the reschedule, if there are any changes in WO then only call the updateWO else smoothly update the reschedules here only
    if (this.alsoUpdateTheWO) {
      this.UpdateWO(workOrderData.id, '');
    }
    this.modalRef = this.modalService.show(ReschedulerModalComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(async res => {
      if (res) {
        await this.getWorkOrder();
        this.getAllReschedulesByWorkOrder(workOrderData.id);
        setTimeout(() => {
          this.gotoScroll(this.workOrderNumber + 'reschedule');
        }, 2000);
      }
    });
  }

  getFieldTechNameById(id: string): string {
    const ids = id.split(',');
    const fieldTechsArray = ids.map(fId => this.fieldTechs.find(x => x.id === Number(fId)).name);
    return fieldTechsArray.join(', ');
  }

  getDisableField(workOrderS, workOrderData, index) {
    if (this.assessmentType === 'Medium Voltage PM') {
      if (workOrderS?.name === WO_STATUSES.REPORT_COMPLETE.name && !this.oilElectricalReportDocuments?.length) {
        return true;
      }
    }

    // CANNOT_COMPLETE
    if (this.selectedWoStatus[index] === WO_STATUSES.CANNOT_COMPLETE.id) {
      if (
        [
          WO_STATUSES.FIELD_WORK_PARTIALLY_COMPLETE.name,
          WO_STATUSES.FIELD_WORK_COMPLETE.name,
          WO_STATUSES.REPORT_STARTED.name,
          WO_STATUSES.REPORT_DRAFTED.name,
          WO_STATUSES.REPORT_COMPLETE.name
        ].includes(workOrderS?.name)
      ) {
        return true;
      } else {
        return false;
      }
    }

    // PENDING
    if (this.selectedWoStatus[index] === WO_STATUSES.PENDING.id) {
      if (
        [WO_STATUSES.REPORT_COMPLETE.name, WO_STATUSES.REPORT_DRAFTED.name, WO_STATUSES.REPORT_STARTED.name].includes(workOrderS?.name) ||
        ([WO_STATUSES.CANNOT_COMPLETE.name].includes(workOrderS?.name) && checkAuthorisations([ROLE_TYPE.MANAGER, ROLE_TYPE.ADMIN]))
      ) {
        return true;
      } else {
        return false;
      }
    }

    // FIELD_WORK_PARTIALLY_COMPLETE
    if (this.selectedWoStatus[index] === WO_STATUSES.FIELD_WORK_PARTIALLY_COMPLETE.id) {
      if (
        [
          WO_STATUSES.REPORT_COMPLETE.name,
          WO_STATUSES.REPORT_DRAFTED.name,
          WO_STATUSES.REPORT_STARTED.name,
          WO_STATUSES.CANNOT_COMPLETE.name
        ].includes(workOrderS?.name)
      ) {
        return true;
      } else {
        return false;
      }
    }

    // FIELD_WORK_COMPLETE
    if (this.selectedWoStatus[index] === WO_STATUSES.FIELD_WORK_COMPLETE.id) {
      if (
        [WO_STATUSES.CANNOT_COMPLETE.name, WO_STATUSES.PENDING.name].includes(workOrderS?.name) ||
        ([WO_STATUSES.REPORT_COMPLETE.name].includes(workOrderS?.name) &&
          (!workOrderData?.reportDocuments || workOrderData?.reportDocuments.length === 0))
      ) {
        return true;
      } else {
        return false;
      }
    }

    // REPORT_STARTED && REPORT_DRAFTED
    if ([WO_STATUSES.REPORT_DRAFTED.id, WO_STATUSES.REPORT_STARTED.id].includes(this.selectedWoStatus[index])) {
      if (
        [WO_STATUSES.CANNOT_COMPLETE.name, WO_STATUSES.PENDING.name, WO_STATUSES.FIELD_WORK_PARTIALLY_COMPLETE.name].includes(
          workOrderS?.name
        ) ||
        ([WO_STATUSES.REPORT_COMPLETE.name].includes(workOrderS?.name) &&
          (!workOrderData?.reportDocuments || workOrderData?.reportDocuments.length === 0))
      ) {
        return true;
      } else {
        return false;
      }
    }

    // REPORT_COMPLETE
    if ([WO_STATUSES.REPORT_COMPLETE.id].includes(this.selectedWoStatus[index])) {
      if ([WO_STATUSES.CANNOT_COMPLETE.name, WO_STATUSES.PENDING.name].includes(workOrderS?.name)) {
        return true;
      } else {
        return false;
      }
    }
    if (this.assessmentType === 'Performance Report') {
      if (
        workOrderS?.name === WO_STATUSES.REPORT_COMPLETE.name &&
        (!workOrderData?.reportDocuments || workOrderData?.reportDocuments.length === 0)
      ) {
        return true;
      } else {
        return false;
      }
    }
  }

  onChangeHistoryAccordian(event, workOrderId, index) {
    if ('workLogAuditHistory' in this.workOrderDatas[index]) {
      if (this.workOrderDatas[index].workLogAuditHistory.length === 0) {
        this.getWorkOrderAuditLogs(workOrderId, index);
      }
    } else {
      this.getWorkOrderAuditLogs(workOrderId, index);
    }
  }

  scheduledDateChanges(workOrderData) {
    this.alsoUpdateTheWO = true;
  }
  woStatusChanges() {
    this.alsoUpdateTheWO = true;
  }

  setTimeout() {
    this.showModal = false;
    this.userActivity = setTimeout(() => {
      this.openModalButton.nativeElement.click();
    }, this.idleTime);
  }

  @HostListener('window:mousemove') refreshUserState() {
    if (!this.showModal) {
      clearTimeout(this.userActivity);
      this.setTimeout();
    }
  }

  openModal(template: TemplateRef<any>) {
    this.showModal = true;
    if (this.isAutoLogout) {
      this.modalRef = this.modalService.show(template, { keyboard: false, ignoreBackdropClick: true });
    }
    this.startLogoutTimer();
  }

  continueWorking() {
    setTimeout(() => {
      this.reLockWO();
      this.clearTimers();
      this.setTimeout();
    }, 0);
  }

  startLogoutTimer() {
    this.startInterval();
    this.logoutTimer = setTimeout(() => {
      this.clearTimers();
    }, this.logoutCounter);
  }

  startInterval() {
    this.countDown = setInterval(() => {
      --this.counter;
    }, 1000);
  }

  clearTimers() {
    clearTimeout(this.userActivity);
    clearTimeout(this.logoutTimer);
    clearInterval(this.countDown);
    this.counter = 120;
  }

  hideToast() {
    this.messageService.clear();
  }

  openDropBoxImageGallery(workOrderData, imageId = null) {
    const requestParamsConfig = {
      id: 0,
      customerId: workOrderData.customerId,
      portfolioId: workOrderData.portfolioId,
      siteId: workOrderData.siteId,
      entityId: workOrderData.id,
      entityNumber: workOrderData.workOrderNumber,
      imagePreviewId: imageId ? this.getActiveIndexById(workOrderData, imageId) : 0,
      isCustomerFacing: false,
      moduleType: 1,
      sortBy: '',
      direction: '',
      page: 0,
      itemsCount: 15
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-dialog image-gallery-modal',
      initialState: {
        requestParamsConfig: requestParamsConfig
      }
    };
    this.modalRef = this.modalService.show(ImageDropboxGalleryComponent, ngModalOptions);
    this.modalRef.content.isParentRefresh.subscribe(res => {
      if (res) {
        this.refreshGalleryAfterUpload(workOrderData.id, 'image');
      }
    });
  }

  getActiveIndexById(workOrderData, id) {
    let index = 0;
    for (const element of workOrderData.imageGalleryList?.imageGallery) {
      if (element.id === id) {
        return index;
      }
      index++;
    }
    return -1;
  }

  isPlPlusUser(userRole: string): boolean {
    return checkAuthorisations([ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER]);
  }

  uploadFilesToGallery(event: any, workOrderData, fileType: string) {
    const files: FileList = event.target.files;
    if (files.length === 0) {
      if (this.imageInput) {
        this.imageInput.nativeElement.value = '';
      }
      if (this.videoInput) {
        this.videoInput.nativeElement.value = '';
      }
      return;
    }
    const fileTypeText = fileType === 'image' ? 'Image' : 'Video';
    const allowedExtensions = {
      image: AppConstants.allowedDropboxImages,
      video: AppConstants.allowedDropboxVideos
    };
    const formData: FormData = new FormData();
    for (let i = 0; i < files.length; i++) {
      const file: File = files[i];
      // Check if the file type is allowed
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions[fileType].includes(fileExtension)) {
        this.alertService.showErrorToast(`Unsupported file type`);
        continue;
      }
      // Add file to FormData object
      formData.append('files', file);
    }
    if (formData.getAll('files').length === 0) {
      return;
    }

    formData.append('customerId', `${workOrderData.customerId}`);
    formData.append('id', '0');
    formData.append('portfolioId', `${workOrderData.portfolioId}`);
    formData.append('siteId', `${workOrderData.siteId}`);
    formData.append('entityId', `${workOrderData.id}`);
    formData.append('entityNumber', `${workOrderData.workOrderNumber}`);
    formData.append('fileType', `${fileType}`);
    formData.append('moduleType', '1');
    formData.append('fileTagIds', null);
    formData.append('notes', '');

    // check the number of files which are > 300 mbs and which are < 200 mbs and prepare two arrays for the same
    // now for the files which are less than 200 mb will be proccessed as belows and
    // for the file greater than 200 will require to be uploaded as chunk
    const bufferChunkSize = 200 * (1024 * 1024);

    if (event.target.files.length) {
      this.chunkUploadInProgress = true;
      this.commonService.isChunkUploadInProgress$.next(true);
      let totalChunksCount = 0; // Track total chunks across all files

      for (const file of event.target.files) {
        const fileUploadTimeStamp = new Date().getTime();
        const totalChunks = Math.ceil(file.size / bufferChunkSize);
        totalChunksCount += totalChunks; // Add to the global count
        this.startChunkUpload(file, bufferChunkSize, fileUploadTimeStamp, totalChunks, workOrderData, fileType);
      }

      this.remainingChunks$.next(totalChunksCount); // Initialize the counter
    }
  }

  startChunkUpload(file: File, chunkSize: number, fileUploadTimeStamp: number, totalChunks: number, workOrderData, fileType) {
    let chunkUpload$: Observable<void> = of(undefined);

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      chunkUpload$ = chunkUpload$.pipe(
        concatMap(() =>
          this.uploadChunk(chunk, chunkIndex, totalChunks, file, fileUploadTimeStamp, workOrderData, fileType).pipe(
            finalize(() => {
              // Decrease the global counter after each chunk
              this.remainingChunks$.next(this.remainingChunks$.value - 1);
              // Check if it's the last chunk of the file and remove from chunkDetails
              if (chunkIndex + 1 === totalChunks) {
                this.removeFileFromChunkDetails(fileUploadTimeStamp);
              }
            })
          )
        )
      );
    }

    chunkUpload$.subscribe({
      next: () => {
        console.log(`All chunks uploaded successfully for file: ${file.name}`);
      },
      error: err => console.error(`Error during chunk upload for file: ${file.name}`, err)
    });

    // Monitor when uploads are fully complete
    this.remainingChunks$.subscribe(remaining => {
      if (remaining === 0) {
        console.log('All files and chunks uploaded.');
        this.chunkUploadInProgress = false;
        this.commonService.isChunkUploadInProgress$.next(false);
        this.commonService.setAutoLogoutValue(false);

        setTimeout(() => {
          if (!this.chunkUploadInProgress) {
            this.alertService.showSuccessToast('The Image was uploaded successfully.');
            this.refreshGalleryAfterUpload(workOrderData.id, 'image');
            this.commonService.commonChunkUploadDetails = [];
            this.commonService.chunkUploadDetails$.next(null);
          }
        }, 1000);
      } else {
        this.chunkUploadInProgress = true;
        this.commonService.isChunkUploadInProgress$.next(true);
        this.commonService.setAutoLogoutValue(true);
      }
    });
  }

  uploadChunk(
    chunk: Blob,
    chunkIndex: number,
    totalChunks: number,
    file: File,
    fileUploadTimeStamp: number,
    workOrderData,
    fileType
  ): Observable<void> {
    const bufferChunkSize = 200 * (1024 * 1024);
    const chunkDetails: ChunkUploadProgressDetails = {
      fileName: file.name,
      currentChunk: chunkIndex + 1,
      totalChunks: totalChunks,
      fileUploadTimeStamp: fileUploadTimeStamp
    };
    // let filePartName = `${file.name}.part_${chunkIndex + 1}.${totalChunks}`;
    let filePartName = '';
    if (file.size > bufferChunkSize) {
      filePartName = `${file.name}.part_${chunkIndex + 1}.${totalChunks}`;
    } else {
      filePartName = `${file.name}`;
    }
    let isChunkUpload = true;
    const formData = new FormData();

    // original form data
    formData.append('customerId', `${workOrderData.customerId}`);
    formData.append('id', '0');
    formData.append('portfolioId', `${workOrderData.portfolioId}`);
    formData.append('siteId', `${workOrderData.siteId}`);
    formData.append('entityId', `${workOrderData.id}`);
    formData.append('entityNumber', `${workOrderData.workOrderNumber}`);
    formData.append('fileType', `${fileType}`);
    formData.append('moduleType', '1');
    formData.append('fileTagIds', null);
    formData.append('notes', '');

    formData.append('files', chunk, filePartName);
    formData.append('fileName', filePartName);
    // form data for chunk upload
    if (file.size > bufferChunkSize) {
      formData.append('totalPart', `${totalChunks}`);
      formData.append('currentPart', `${chunkIndex + 1}`);
      formData.append('fileUploadTimeStamp', `${fileUploadTimeStamp}`);
      formData.append('IsLargeFile', `${isChunkUpload}`);
    } else {
      formData.append('IsLargeFile', `false`);
    }
    return this.commonService.uploadChunk(formData, chunkDetails).pipe(
      takeUntil(this.destroy$),
      tap(() => {
        console.log(`Chunk ${chunkIndex + 1}/${totalChunks} uploaded successfully.`);
      }),
      catchError(err => {
        console.error(`Error uploading chunk ${chunkIndex + 1}/${totalChunks}:`, err);
        throw err; // Propagate error for retry or higher-level handling
      })
    );
  }

  removeFileFromChunkDetails(fileUploadTimeStamp: number) {
    const updatedDetails = this.commonService.chunkUploadDetails$.value.filter(
      detail => detail.fileUploadTimeStamp !== fileUploadTimeStamp
    );
    // Emit the updated details to the BehaviorSubject
    this.commonService.chunkUploadDetails$.next(updatedDetails);
  }

  refreshGalleryAfterUpload(entityId, type) {
    const params = {
      entityId: entityId,
      isVideoRefresh: type === 'video'
    };
    this.subscription.add(
      this.workorderService.getWorkOrderGalleryAfterUpload(params).subscribe({
        next: res => {
          for (const obj of this.workOrderDatas) {
            if (obj.id === entityId) {
              type === 'image' ? (obj.imageGalleryList = res) : (obj.videoGalleryList = res);
              break;
            }
          }
          this.removeApiCount();
        },
        error: err => {
          this.removeApiCount();
        }
      })
    );
  }

  downloadVideoFile(videoId, fileName) {
    this.addApiCount();
    this.dropBoxService.downloadPreviewedImage(videoId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, data.type);
          link.download = fileName;
          link.click();
          this.removeApiCount();
        } else {
          this.removeApiCount();
        }
      },
      error: e => {
        this.removeApiCount();
      }
    });
  }

  deleteGalleryItem(workOrderDataId, videoId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.subscription.add(
          this.dropBoxService.deleteImageGalleryFiles(videoId).subscribe({
            next: data => {
              this.alertService.showSuccessToast('File deleted Successfully.');
              this.refreshGalleryAfterUpload(workOrderDataId, 'video');
              this.removeApiCount();
            },
            error: e => {
              this.removeApiCount();
            }
          })
        );
      }
    });
  }

  onChangeSize(entityId) {
    this.videoPaginationParams.currentPage = 0;
    this.videoPaginationParams.itemsCount = Number(this.videoPaginationParams.pageSize);
    this.onPaginationChanges(entityId);
  }

  onPageChange(obj, entityId) {
    this.videoPaginationParams.currentPage = obj;
    this.onPaginationChanges(entityId);
  }

  onPaginationChanges(entityId) {
    const params = {
      entityId: entityId,
      sortBy: '',
      direction: '',
      page: this.videoPaginationParams.currentPage - 1,
      itemsCount: this.videoPaginationParams.itemsCount
    };
    this.subscription.add(
      this.workorderService.getWoVideoGallery(params).subscribe({
        next: res => {
          for (const obj of this.workOrderDatas) {
            if (obj.id === entityId) {
              obj.videoGalleryList = res;
              break;
            }
          }
          this.removeApiCount();
        },
        error: err => {
          this.removeApiCount();
        }
      })
    );
  }

  //below code is for QEST FORM for workOrders

  openTemplateModal(template: TemplateRef<any>, templateFor, workorderId, qestFormId = null) {
    this.qestFormId = qestFormId ? qestFormId : this.qestFormId;
    this.qestWorkOrderId = workorderId ? workorderId : this.qestWorkOrderId;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-xl'
    };

    setTimeout(() => {
      if (templateFor === this.woTemplateOpenModelForFormConstant.AVAILABLE_FORM) {
        this.availableFormModalRef = this.modalService.show(template, ngModalOptions);
        this.isStartAll = false;
        this.getAvailableQESTFormByTemplateType(templateFor, null, workorderId);
      } else if (templateFor === this.woTemplateOpenModelForFormConstant.SELECT_INVERTER) {
        this.selectInverterModalRef = this.modalService.show(template, ngModalOptions);
        this.getInverterListForQESTForms(workorderId);
      } else if (templateFor === this.woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS) {
        this.availableModuleTorqueFormModalRef = this.modalService.show(template, ngModalOptions);
        this.getAvailableQESTFormByTemplateType(templateFor, null, workorderId);
      } else if (templateFor === this.woTemplateOpenModelForFormConstant.SELECT_ZONE) {
        this.selectZoneModalRef = this.modalService.show(template, ngModalOptions);
        this.getAvailableZoneList(templateFor, workorderId);
      } else if (templateFor === this.woTemplateOpenModelForFormConstant.ZONE_MODEL_SCREEN) {
        this.startFormUsingZoneRef = this.modalService.show(template, ngModalOptions);
        this.getAllSavedMTRQQestFormForWO(workorderId);
      } else if (templateFor === this.woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL) {
        this.viewModuleTorqueFormsRef = this.modalService.show(template, ngModalOptions);
        this.getMTRQQESTFormForViewByTemplateType(templateFor, null, workorderId);
      } else {
        this.viewFromModalRef = this.modalService.show(template, ngModalOptions);
        this.getQESTFormForViewByTemplateType(templateFor, null, workorderId);
      }
    }, 0);
  }

  getAvailableFormsForQESTForms(reqParam = null, formTemplateType: WOTemplateOpenModelForFormConstantValues) {
    this.addApiCount();
    const params = {
      customerIds: [this.customerId],
      siteId: this.siteId,
      workorderIds: [this.qestWorkOrderId],
      templateTypeIds: [1],
      search: '',
      sortBy: 'UpdatedDate ',
      direction: 'desc',
      page: reqParam ? reqParam.page : 0,
      itemsCount: reqParam ? reqParam.itemsCount : this.pagination.availableForm.itemsCount
    };
    this.subscription.add(
      this.workorderService.getWorkOrderAvailableFormsList(params).subscribe({
        next: (res: AvailableFormsResponse) => {
          if (res) {
            this.formsListingData = res.listOfQESTForm;
            this.pagination[formTemplateType].totalCount = res.totalQESTForm;
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  getAvailableModuleTorqueFormsForQESTForms(reqParam = null, formTemplateType: WOTemplateOpenModelForFormConstantValues) {
    this.addApiCount();
    const params = {
      search: '',
      templateTypeIds: [4],
      customerIds: [this.customerId],
      siteId: this.siteId,
      isFromWorkOrder: true,
      workorderIds: [this.qestWorkOrderId],
      isShowInProgressForm: null,
      qESTFormId: null,
      isApplyPagination: true,
      sortBy: 'UpdatedDate',
      direction: 'desc',
      page: reqParam ? reqParam.page : 0,
      itemsCount: reqParam ? reqParam.itemsCount : this.pagination.availableForm.itemsCount
    };
    this.subscription.add(
      this.workorderService.getAvailableModuleTorqueFormsForQESTForms(params).subscribe({
        next: (res: ModuleTorqueFormsByWorkOrder) => {
          if (res) {
            this.moduleTorqueFormsListingData = res;
            this.pagination[formTemplateType].totalCount = res.totalQESTForm;
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  getAvailableQESTFormByTemplateType(formTemplateType: WOTemplateOpenModelForFormConstantValues, reqParam = null, workorderId?): void {
    switch (this.setQESTFormSelectionType(formTemplateType)) {
      case 1:
        const params = {
          customerIds: [this.customerId],
          siteId: this.siteId,
          workorderIds: [workorderId ?? this.qestWorkOrderId],
          templateTypeIds: [1],
          search: '',
          sortBy: 'UpdatedDate ',
          direction: 'desc',
          page: reqParam ? reqParam.page : 0,
          itemsCount: reqParam ? reqParam.itemsCount : this.pagination[formTemplateType].itemsCount,
          templateType: this.setQESTFormSelectionType(formTemplateType)
        };
        this.getAvailableFormsForQESTForms(params, formTemplateType);
        break;
      case 2:
        break;
      case 3:
        break;
      case 4:
        const MTparams = {
          customerIds: [this.customerId],
          siteId: this.siteId,
          workorderIds: [workorderId ?? this.qestWorkOrderId],
          templateTypeIds: [this.setQESTFormSelectionType(formTemplateType)],
          search: '',
          sortBy: 'UpdatedDate ',
          direction: 'desc',
          page: reqParam ? reqParam.page : 0,
          itemsCount: reqParam ? reqParam.itemsCount : this.pagination[formTemplateType].itemsCount,
          templateType: this.setQESTFormSelectionType(formTemplateType)
        };
        this.getAvailableModuleTorqueFormsForQESTForms(MTparams, formTemplateType);
        break;
    }
  }

  getInverterListForQESTForms(workorderId?) {
    const listOfQESTFormId = this.formsListingData.map(forms => forms.qestFormId);
    this.addApiCount();
    const reqParam = {
      siteId: this.siteId,
      companyId: 0,
      deviceType: 1,
      workOrderId: workorderId ?? this.qestWorkOrderId,
      qestFormId: this.qestFormId ? this.qestFormId : 0,
      listOfQESTFormId: this.isStartAll ? listOfQESTFormId : [this.qestFormId]
    };
    this.subscription.add(
      this.workorderService.getWorkOrderInverterList(reqParam).subscribe({
        next: (res: SelectInverterList[]) => {
          if (res) {
            this.inverterListData = res;
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  getAvailableZoneList(templateFor: any, workorderId) {
    const listOfQESTFormId = this.formsListingData.map(forms => forms.qestFormId);
    this.addApiCount();
    const reqParam = {
      siteId: this.siteId,
      companyId: 0,
      workOrderId: workorderId ?? this.qestWorkOrderId,
      qestFormId: this.qestFormId ? this.qestFormId : 0,
      listOfQESTFormId: this.isStartAll ? listOfQESTFormId : [this.qestFormId]
    };
    this.subscription.add(
      this.workorderService.getSiteZonesDetails(reqParam).subscribe({
        next: (res: SelectInverterList[]) => {
          if (res) {
            this.zoneListDataForSelection = res;
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  setQESTFormSelectionType(formTemplateType: WOTemplateOpenModelForFormConstantValues): number {
    switch (formTemplateType) {
      case this.woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL:
      case this.woTemplateOpenModelForFormConstant.AVAILABLE_FORM:
        return QESTFormTemplateTypes.QEST_INVERTER_PM;
      case this.woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL:
        return QESTFormTemplateTypes.QEST_SUMMARY_REPORT;
      case this.woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL:
        return QESTFormTemplateTypes.QEST_TPM_FORM;
      case this.woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL:
      case this.woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS:
        return QESTFormTemplateTypes.QEST_MODULE_TORQUE;
      default:
        return 0;
    }
  }

  getQESTFormForViewByTemplateType(formTemplateType: WOTemplateOpenModelForFormConstantValues, reqParam = null, workorderId?): void {
    const params = {
      workorderId: workorderId ?? this.qestWorkOrderId,
      qESTFormId: null,
      isApplyPagination: true,
      sortBy: 'UpdatedDate',
      direction: 'desc',
      page: reqParam ? reqParam.page : 0,
      itemsCount: reqParam ? reqParam.itemsCount : this.pagination[formTemplateType].itemsCount,
      templateType: this.setQESTFormSelectionType(formTemplateType)
    };
    this.getViewFormsForQESTForms(params, formTemplateType);
  }

  getMTRQQESTFormForViewByTemplateType(formTemplateType: WOTemplateOpenModelForFormConstantValues, reqParam = null, workorderId?): void {
    const params = {
      workorderId: workorderId ?? this.qestWorkOrderId,
      qESTFormId: null,
      isApplyPagination: true,
      sortBy: 'UpdatedDate',
      direction: 'desc',
      page: reqParam ? reqParam.page : 0,
      itemsCount: reqParam ? reqParam.itemsCount : this.pagination[formTemplateType].itemsCount,
      templateType: this.setQESTFormSelectionType(formTemplateType)
    };
    this.getViewFormsForMTRQQESTForms(params, formTemplateType);
  }

  getViewFormsForQESTForms(params, formTemplateType: WOTemplateOpenModelForFormConstantValues) {
    this.addApiCount();
    this.subscription.add(
      this.workorderService.getWorkOrderViewFormsList(params).subscribe({
        next: (res: ViewFormsResponse) => {
          if (res) {
            this.pagination[formTemplateType].totalCount = res.totalMappedQESTForm;
            this.uploadedFormsData[formTemplateType] = res.listOfMappedQESTForm;
            this.completedFormMapId[formTemplateType] = this.uploadedFormsData[formTemplateType]
              .filter(form => form.formStatus === 3)
              .map(form => form.qestwoMapId);
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  getViewFormsForMTRQQESTForms(params, formTemplateType: WOTemplateOpenModelForFormConstantValues) {
    this.addApiCount();
    this.subscription.add(
      this.workorderService.viewModuleTorqueForms(params).subscribe({
        next: (res: AvailableModuleTorqueFormsResponse) => {
          if (res) {
            this.pagination[formTemplateType].totalCount = res.totalMappedQESTForm;
            this.uploadedFormsData[formTemplateType] = res.listOfMappedQESTForm;
            this.completedFormMapId[formTemplateType] = this.uploadedFormsData[formTemplateType]
              .filter(form => form.formStatus === 3)
              .map(form => form.qestwoMapId);
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  getCompletedFormCount() {
    this.addApiCount();
    this.subscription.add(
      this.workorderService.getCompletedFormCount(this.qestWorkOrderId).subscribe({
        next: (res: GetCompletedFormCount) => {
          if (res) {
            this.workOrderDatas.forEach(element => {
              if (element.id === this.qestWorkOrderId) {
                element.qestFormCount = res.ipmCount;
                element.summaryReportCount = res.summaryReportCount;
                element.tpmFormCount = res.tpmFormCount;
                element.moduleTorqueCount = res.moduleTorqueCount;
                element.zoneIds = res.zoneIds ?? [];
              }
            });
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  getStatusLabel(status: number) {
    switch (status) {
      case FormStatus.NotStarted:
        return 'Not Started';
      case FormStatus.InProgress:
        return 'In Progress';
      case FormStatus.Draft:
        return 'Draft';
      case FormStatus.Completed:
        return 'Completed';
      default:
        return 'Not Started';
    }
  }

  onChangeSizeQEST(changeFor) {
    this.pagination[changeFor].currentPage = 0;
    this.pagination[changeFor].itemsCount = Number(this.pagination[changeFor].pageSize);
    const params = {
      page: 0,
      itemsCount: this.pagination[changeFor].itemsCount
    };
    if (changeFor === this.woTemplateOpenModelForFormConstant.AVAILABLE_FORM) {
      this.getAvailableQESTFormByTemplateType(changeFor, params);
    } else {
      this.getQESTFormForViewByTemplateType(changeFor, params);
    }
  }

  onPageChangeQEST(obj, changeFor) {
    this.pagination[changeFor].currentPage = obj;
    const params = {
      page: obj - 1,
      itemsCount: this.pagination[changeFor].itemsCount
    };
    if (changeFor === this.woTemplateOpenModelForFormConstant.AVAILABLE_FORM) {
      this.getAvailableQESTFormByTemplateType(changeFor, params);
    } else {
      this.getQESTFormForViewByTemplateType(changeFor, params);
    }
  }

  SelectDeSelectAll(changeEvent: boolean) {
    this.inverterListData.forEach(inverter => (inverter.isSelected = changeEvent));
    if (changeEvent) {
      this.selectAllBtnText = 'UnSelect All';
    } else {
      this.selectAllBtnText = 'Select All';
    }
  }

  SelectDeSelectAllZones(changeEvent: boolean) {
    this.zoneListDataForSelection.forEach(zone => (zone.isSelected = changeEvent));
    if (changeEvent) {
      this.selectAllBtnText = 'UnSelect All';
    } else {
      this.selectAllBtnText = 'Select All';
    }
  }

  AddSelectedInverter() {
    this.addApiCount();
    const selectedDevices = this.inverterListData
      .filter(inverter => inverter.isSelected)
      .map(({ qestFormId, id }) => ({ qestFormId, siteDeviceId: id }));

    const params = {
      listOfQESTFormSiteDevice: selectedDevices,
      workOrderId: this.qestWorkOrderId,
      isStartAll: this.isStartAll
    };
    if (selectedDevices.length) {
      this.subscription.add(
        this.workorderService.saveSelectedInverters(params).subscribe({
          next: (res: any) => {
            if (res) {
              this.selectInverterModalRef.hide();
              this.searchText = '';
              this.alertService.showSuccessToast('Selected inverters added successfully.');
              this.openModalForStartFormByInverter(this.startFormUsingInverter);
              this.removeApiCount();
            }
          },
          error: e => {
            this.removeApiCount();
          }
        })
      );
    } else {
      this.alertService.showWarningToast('Please select at least one device.');
      this.removeApiCount();
    }
  }

  addSelectedZones() {
    this.addApiCount();
    const selectedZones = this.zoneListDataForSelection
      .filter(zone => zone.isSelected)
      .map(({ qestFormId, zoneId }) => ({ qestFormId, zoneId }));

    const params = {
      listOfQESTFormSiteZone: selectedZones,
      workOrderId: this.qestWorkOrderId,
      isStartAll: this.isStartAll
    };
    if (selectedZones.length) {
      this.subscription.add(
        this.workorderService.saveSelectedZoneForWorkorder(params).subscribe({
          next: (res: any) => {
            if (res) {
              this.selectZoneModalRef.hide();
              this.searchText = '';
              this.alertService.showSuccessToast('Selected zones added successfully.');
              this.openModalForStartFormByZone(this.startFormUsingZone);
              this.removeApiCount();
            }
          },
          error: e => {
            this.removeApiCount();
          }
        })
      );
    } else {
      this.alertService.showWarningToast('Please select at least one zone.');
      this.removeApiCount();
    }
  }

  openModalForStartFormByInverter(template: TemplateRef<any>, qestFormId = null) {
    this.qestFormId = qestFormId ? qestFormId : this.qestFormId;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog'
    };
    setTimeout(() => {
      this.startFormByInverterModalRef = this.modalService.show(template, ngModalOptions);
      this.getAllSavedQestFormForWO();
    }, 0);
  }

  openModalForStartFormByZone(template: TemplateRef<any>, qestFormId = null, workorderId?) {
    this.qestFormId = qestFormId ? qestFormId : this.qestFormId;
    const woId = workorderId;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog'
    };
    setTimeout(() => {
      this.startFormUsingZoneRef = this.modalService.show(template, ngModalOptions);
      this.getAllSavedMTRQQestFormForWO(woId);
    }, 0);
  }

  onDeleteUploadedForm(qestWoMapId: number, formTemplateType: WOTemplateOpenModelForFormConstantValues) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.deleteViewForm(qestWoMapId, formTemplateType);
      }
    });
  }

  deleteViewForm(qestWoMapId, formTemplateType: WOTemplateOpenModelForFormConstantValues) {
    this.subscription.add(
      this.workorderService.deleteUploadedForm(qestWoMapId).subscribe({
        next: (res: any) => {
          if (res) {
            if (this.assessmentType === 'Module Torque') {
              this.getMTRQQESTFormForViewByTemplateType(formTemplateType);
            } else {
              this.getQESTFormForViewByTemplateType(formTemplateType);
            }
            this.alertService.showSuccessToast(res.message);
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  deleteSelectedInverter(qestWoMapId: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete this inverter from this form?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.subscription.add(
          this.workorderService.deleteUploadedForm(qestWoMapId).subscribe({
            next: (res: any) => {
              if (res) {
                this.getAllSavedQestFormForWO();
                this.alertService.showSuccessToast(res.message);
              }
            },
            error: e => {
              this.removeApiCount();
            }
          })
        );
      }
    });
  }

  deleteSelectedZone(qestWoMapId: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete this zone from this form?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.subscription.add(
          this.workorderService.deleteUploadedForm(qestWoMapId).subscribe({
            next: (res: any) => {
              if (res) {
                this.getAllSavedMTRQQestFormForWO();
                this.alertService.showSuccessToast(res.message);
              }
            },
            error: e => {
              this.removeApiCount();
            }
          })
        );
      }
    });
  }

  calculateCompletedFormPercentage() {
    const completedFormCount = this.selectedInverters.filter(item => item.formStatus === 3).length;
    const totalInverterFromCount = this.selectedInverters.length;

    if (totalInverterFromCount === 0) {
      this.progressBarCount = 0;
    } else {
      this.progressBarCount = (completedFormCount / totalInverterFromCount) * 100;
    }
  }

  calculateCompletedMTRQFormPercentage() {
    const completedFormCount = this.selectedZones.filter(item => item.formStatus === 3).length;
    const totalZoneFromCount = this.selectedZones.length;

    if (totalZoneFromCount === 0) {
      this.MTRQprogressBarCount = 0;
    } else {
      this.MTRQprogressBarCount = (completedFormCount / totalZoneFromCount) * 100;
    }
  }

  setDownloadNameForForm(selectedForm, formTemplateType: WOTemplateOpenModelForFormConstantValues): string {
    switch (formTemplateType) {
      case this.woTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL:
        return `${selectedForm.formName}-${selectedForm.deviceName}`;
      case this.woTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL:
        return `${selectedForm?.siteName ? selectedForm?.siteName : ''} - ${
          selectedForm?.workorderName ? selectedForm.workorderName : ''
        } - Summary Report`;
      case this.woTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL:
        return `${selectedForm?.siteName ? selectedForm?.siteName : ''} - ${
          selectedForm?.workorderName ? selectedForm.workorderName : ''
        } - Tracker Preventative Maintenance Form`;
      case this.woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL:
        return `${selectedForm?.siteName ? selectedForm?.siteName : ''} - ${
          selectedForm?.workorderName ? selectedForm.workorderName : ''
        } - ${selectedForm.zoneName}`;
      default:
        return '';
    }
  }

  downloadUploadedForm(viewForms, formTemplateType) {
    this.addApiCount();

    this.workorderService.downloadQESTFrom(viewForms.qestwoMapId).subscribe({
      next: (res: Blob) => {
        const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
        link.download = `${this.setDownloadNameForForm(viewForms, formTemplateType)}.pdf`;
        link.click();
        this.removeApiCount();
      },
      error: _e => {
        this.removeApiCount();
      }
    });
  }

  getAllSavedQestFormForWO() {
    const requestObject = {
      workOrderId: this.qestWorkOrderId,
      qestFormId: this.isStartAll ? null : this.qestFormId,
      isApplyPagination: false,
      sortBy: '',
      direction: '',
      page: 0,
      itemsCount: 0
    };
    this.subscription.add(
      this.workorderService.getAllSavedQestFormForWO(requestObject).subscribe(res => {
        this.selectedInverters = res.listOfMappedQESTForm;
        this.isAnyFormStartOrComplete = this.selectedInverters.some(inverter => inverter.formStatus === 1 || inverter.formStatus === 3);
        this.calculateCompletedFormPercentage();
        this.removeApiCount();
      })
    );
  }

  getAllSavedMTRQQestFormForWO(workorderId?) {
    const requestObject = {
      workOrderId: workorderId ?? this.qestWorkOrderId,
      qestFormId: this.isStartAll ? null : this.qestFormId,
      isApplyPagination: false,
      sortBy: '',
      direction: '',
      page: 0,
      itemsCount: 0
    };
    this.subscription.add(
      this.workorderService.viewModuleTorqueForms(requestObject).subscribe(res => {
        this.mapedFormsForZone = res.listOfMappedQESTForm;
        this.selectedZones = res.listOfMappedQESTForm;
        this.isAnyFormStartOrComplete = this.selectedZones.some(inverter => inverter.formStatus === 1 || inverter.formStatus === 3);
        this.calculateCompletedMTRQFormPercentage();
        this.removeApiCount();
      })
    );
  }

  // to be used
  // goToFormFillingPage(qestwoMapId: number, fromWhichModal) {
  //   this.router.navigate(['/entities/operations/custom-forms/fill-form', qestwoMapId], {
  //     queryParams: {
  //       id: this.workOrders.id,
  //       workOrderId: this.qestWorkOrderId,
  //       formId: this.qestFormId,
  //       assementType: this.workOrders.assementType,
  //       frequncyType: this.frequncyType,
  //       pageOpenFrom: fromWhichModal,
  //       isPmReviewForm: this.isPmReviewForm
  //     }
  //   });
  //   if (fromWhichModal === this.pageOpenFromConstant.INVERTER_MODEL_SCREEN) {
  //     this.startFormByInverterModalRef.hide();
  //   } else {
  //     this.viewFromModalRef.hide();
  //   }
  // }

  goToFormFillingPage(viewForm: ViewFormsList | Partial<ViewFormsList>, fromWhichModal, isSrTpmFormActive = true) {
    if (!isSrTpmFormActive) {
      if (fromWhichModal === this.pageOpenFromConstant.FILL_INITIAL_SR) {
        this.alertService.showErrorToast('No summary report available');
        return;
      } else if (fromWhichModal === this.pageOpenFromConstant.FILL_INITIAL_TPM) {
        this.alertService.showErrorToast('No Tracker Preventative Maintenance form available');
        return;
      }
    }
    const qestFillFormRedirection = new QESTFillFormRedirection(
      viewForm?.qestwoMapId,
      this.workOrders.id,
      viewForm?.workOrderId,
      viewForm?.qestFormId,
      this.workOrders.assementType,
      this.frequncyType,
      fromWhichModal,
      this.isPmReviewForm
    );
    this.QESTFillFormRedirection(qestFillFormRedirection);
    if (fromWhichModal === this.pageOpenFromConstant.INVERTER_MODEL_SCREEN) {
      this.startFormByInverterModalRef.hide();
    } else if (fromWhichModal === this.pageOpenFromConstant.ZONE_MODEL_SCREEN) {
      this.startFormUsingZoneRef.hide();
    } else if (fromWhichModal === this.woTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL) {
      this.viewModuleTorqueFormsRef.hide();
    } else if (fromWhichModal === this.pageOpenFromConstant.FILL_INITIAL_SR) {
      return;
    } else if (fromWhichModal === this.pageOpenFromConstant.FILL_INITIAL_TPM) {
      return;
    } else {
      this.viewFromModalRef.hide();
    }
  }

  QESTFillFormRedirection(qestFillFormRedirectionObj: QESTFillFormRedirection): void {
    this.router.navigate(['/entities/operations/custom-forms/fill-form', qestFillFormRedirectionObj.qestwoMapId], {
      queryParams: {
        id: qestFillFormRedirectionObj.id,
        workOrderId: qestFillFormRedirectionObj.workOrderId,
        formId: qestFillFormRedirectionObj.formId,
        assementType: qestFillFormRedirectionObj.assementType,
        frequncyType: qestFillFormRedirectionObj.frequncyType,
        pageOpenFrom: qestFillFormRedirectionObj.pageOpenFrom,
        isPmReviewForm: qestFillFormRedirectionObj.isPmReviewForm,
        backToDashboard: this.backToDashboard
      }
    });
  }

  onCloseOfStartFormUsingInverter() {
    const count = this.selectedInverters.filter(item => item.formStatus === 0).length,
      ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: `There are still ${count} inverter(s) left to be completed. Are you sure you want to go back?`,
          confirmBtnText: 'Back',
          cancelBtnText: 'Continue Editing'
        }
      };

    if (count) {
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.startFormByInverterModalRef.hide();
          this.openTemplateModal(
            this.availableFormList,
            this.woTemplateOpenModelForFormConstant.AVAILABLE_FORM,
            this.qestWorkOrderId,
            this.qestFormId
          );
          this.getCompletedFormCount();
          this.removeBackToWhereParam();
        }
      });
    } else {
      this.startFormByInverterModalRef.hide();
      this.getCompletedFormCount();
      this.removeBackToWhereParam();
    }
  }

  onCloseOfStartFormUsingZone() {
    const count = this.selectedZones.filter(item => item.formStatus === 0).length,
      ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: `There are still ${count} zone(s) left to be completed. Are you sure you want to go back?`,
          confirmBtnText: 'Back',
          cancelBtnText: 'Continue Editing'
        }
      };

    if (count) {
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.startFormUsingZoneRef.hide();
          this.openTemplateModal(
            this.availableModuleTorqueForms,
            this.woTemplateOpenModelForFormConstant.AVAILABLE_MT_FORMS,
            this.qestWorkOrderId,
            this.qestFormId
          );
          this.getCompletedFormCount();
          this.removeBackToWhereParam();
        }
      });
    } else {
      this.startFormUsingZoneRef.hide();
      this.getCompletedFormCount();
      this.removeBackToWhereParam();
    }
  }

  removeBackToWhereParam(): void {
    this.route.queryParams.subscribe(params => {
      // Clone the params and delete the specific one

      let updatedParams = { ...params };
      delete updatedParams['backToWhere'];
      // Navigate to the same route with updated parameters
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: updatedParams
      });
    });
  }

  countStatuses(status) {
    const count = this.selectedInverters.filter(form => form.formStatus === status).length;
    return count ? count : 0;
  }

  countModuleTorqueStatuses(status) {
    const count = this.selectedZones.filter(form => form.formStatus === status).length;
    return count ? count : 0;
  }

  onOpenHistoryModal(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    setTimeout(() => {
      this.historyModalRef = this.modalService.show(template, ngModalOptions);
    }, 0);
  }

  getHistoryForQESTForms(template: TemplateRef<any>, viewForms: ViewFormsList) {
    this.viewFromInfo = viewForms;
    this.addApiCount();
    this.subscription.add(
      this.workorderService.getCompletedFormNoteHistory(viewForms.qestwoMapId).subscribe({
        next: (res: any) => {
          if (res) {
            this.viewFromModalRef ? this.viewFromModalRef.hide() : null;
            this.viewModuleTorqueFormsRef ? this.viewModuleTorqueFormsRef.hide() : null;
            this.formEditNoteHistory = res;
            this.onOpenHistoryModal(template);
            this.removeApiCount();
          }
        },
        error: e => {
          this.removeApiCount();
        }
      })
    );
  }

  downloadAllForms(formTemplateType: WOTemplateOpenModelForFormConstantValues) {
    this.addApiCount();
    const completedFormsId = this.uploadedFormsData[formTemplateType].filter(form => form.formStatus === 3).map(form => form.qestwoMapId);
    const params = {
      listOfQESTWOMapId: completedFormsId,
      workOrderId: this.qestWorkOrderId
    };
    this.workorderService.downloadAllViewModalQESTFrom(params).subscribe({
      next: (res: DownloadAllViewFormResponse) => {
        this.alertService.showSuccessToast(res.message);
        this.removeApiCount();
      },
      error: _e => {
        this.removeApiCount();
      }
    });
  }

  private addApiCount() {
    this.apiCount$.next(this.apiCount$.value + 1);
  }

  private removeApiCount() {
    if (this.apiCount$.value) {
      this.apiCount$.next(this.apiCount$.value - 1);
    }
  }

  workorderAccordionChange(event, workOrderData) {
    if (!event) {
      this.addApiCount();
      const request = {
        id: workOrderData.assesmentId,
        assementType: workOrderData.assesmentType,
        selectedWorkOrderId: workOrderData.id
      };
      this.updateWorkOrderId(workOrderData.id);
      this.subscription.add(
        this.workorderService.getWorkOrderById(request).subscribe({
          next: (res: any) => {
            if (res) {
              // update the workOrderDatas data with received wo data
              this.workOrderDatas = this.workOrderDatas.map(obj => {
                if (obj.id === res.id) {
                  // For the selected work order, set expanded to true
                  this.expandedWoId = obj.id;
                  res.expanded = true;
                  return res;
                } else {
                  // For others, keep expanded as false
                  obj.expanded = false;
                  return obj;
                }
              });
              const workOrderDatas = this.workOrderDatas;
              const element = res;
              element.workOrderSchedules = [];
              if (element.dateScheduled) {
                element.dateScheduled = new Date(element.dateScheduled);
              }
              if (element.dueDate) {
                element.dueDate = new Date(element.dueDate);
              }
              if (element.datePerformed) {
                element.datePerformed = new Date(element.datePerformed);
              }
              if (element.rescheduleDate) {
                element.rescheduleDate = new Date(element.rescheduleDate);
              }
              if (element.reportCompleteDate) {
                element.reportCompleteDateDisplay = new Date(element.reportCompleteDateDisplay);
              }
              if (element.workCompleteDate) {
                element.workCompleteDateDisplay = new Date(element.workCompleteDateDisplay);
              }
              if (!element.fieldTechs) {
                element.fieldTechs = element.fieldTechList.map(x => x.userId);
              } else {
                element.fieldTechs = [];
              }
              if (!element.reportAuthors) {
                element.reportAuthors = element.reportAuthorList.map(x => x.userId);
              } else {
                element.reportAuthors = [];
              }
              if (!element.scanType) {
                element.scanType = this.scanTypeStatus[0].value;
              }
              if (!element.reportStatus) {
                if (this.rtStatus.length > 0) {
                  element.reportStatus = this.rtStatus[0].text;
                }
              }
              if (!element.woStatus) {
                if (this.workOrderStatus.length > 0) {
                  element.woStatus = this.workOrderStatus[0].id;
                }
              }
              this.currentWOStatus = element.woStatus;
              this.selectedWoStatus.push(element.woStatus);
              this.workOrderDatasClone = JSON.parse(JSON.stringify(workOrderDatas));
              this.workOrderDatas = workOrderDatas.map(WO => {
                if (WO.isDeleted) {
                  const newWOValues = new WorkOrderData();
                  return {
                    ...newWOValues,
                    isDeleted: true,
                    workOrderNumber: WO.workOrderNumber,
                    expanded: WO.expanded,
                    id: WO.id,
                    assesmentId: WO.assesmentId,
                    assesmentType: WO.assesmentType
                  };
                }
                return WO;
              });
              this.removeApiCount();
            }
          },
          error: e => {
            this.removeApiCount();
          }
        })
      );
    }
  }

  updateWorkOrderId(newWorkOrderId: number) {
    this.route.queryParams.subscribe(params => {
      if (params['workOrderId']) {
        this.router.navigate([], {
          queryParams: {
            workOrderId: newWorkOrderId
          },
          queryParamsHandling: 'merge'
        });
      }
    });
  }

  buttonCondition(workorderDetail: WorkOrderData) {
    workorderDetail.enableCreateReport = false;

    this.newReportData.reportType = workorderDetail.assesmentType === 'SV' ? 1 : workorderDetail.assesmentType === 'MVPM' ? 3 : 0;
    if (workorderDetail.assesmentType === 'SV' || workorderDetail.assesmentType === 'MVPM') {
      workorderDetail.enableCreateReport = !(
        workorderDetail.woStatus === WO_STATUSES.REPORT_COMPLETE.id || workorderDetail.woStatus === WO_STATUSES.REPORT_COMPLETE.id
      );
    } else {
      workorderDetail.enableCreateReport = false;
    }
  }

  updateWorOderStatus(params) {
    this.reportService.updateWorkOrderStatus(params).subscribe({
      next: res => {
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  commonWoStatusUpdateLogic(workorderDetail: WorkOrderData) {
    if (this.workOrders.assementType === 'SV' || this.workOrders.assementType === 'MVPM') {
      const param = {
        woId: workorderDetail.id,
        woStatusId: WO_STATUSES.FIELD_WORK_PARTIALLY_COMPLETE.id
      };
      this.updateWorOderStatus(param);
    }
  }

  async startNewReport(workorderDetail: WorkOrderData) {
    // check if the report is already created for the workorder or not
    await this.checkReportCreationStatus(workorderDetail);
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Do you want to create a JHA or continue to Report?',
        confirmBtnText: 'Create JHA',
        cancelBtnText: 'Continue to Report',
        hideCloseIcon: true
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (this.isReportExistForWO && !result) {
        // continue to report
        this.commonWoStatusUpdateLogic(workorderDetail);
        this.onViewReport(workorderDetail.id);
      } else if (this.isReportExistForWO && result) {
        // create JHA
        this.commonWoStatusUpdateLogic(workorderDetail);
        this.router.navigate(['/entities/safety/jha/upload'], {
          queryParams: {
            checkin: 1,
            cust: workorderDetail.customerId,
            port: workorderDetail.portfolioId,
            site: workorderDetail.siteId,
            workorderId: workorderDetail.id,
            workOrderNumber: workorderDetail.workOrderNumber,
            reportId: this.newReportData.id,
            returnTo: 'Workorder'
          }
        });
      } else if (!this.isReportExistForWO && result) {
        this.commonWoStatusUpdateLogic(workorderDetail);
        // report is not created, user opted to create JHA. Create report and then Goto JHA
        // generate the report with empty JHA then redirect to JHA create screen and then report fill
        const user = this.storageService.get('user');
        // /api/mobile/v2/uploadreportdata
        const date = new Date();
        const offset = -date.getTimezoneOffset(); // Get offset in minutes
        const offsetHours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, '0');
        const offsetMinutes = String(Math.abs(offset) % 60).padStart(2, '0');
        const timeZoneOffset = (offset >= 0 ? '+' : '-') + offsetHours + ':' + offsetMinutes;

        const localDateWithOffset =
          date.getFullYear() +
          '-' +
          String(date.getMonth() + 1).padStart(2, '0') +
          '-' +
          String(date.getDate()).padStart(2, '0') +
          'T' +
          String(date.getHours()).padStart(2, '0') +
          ':' +
          String(date.getMinutes()).padStart(2, '0') +
          ':' +
          String(date.getSeconds()).padStart(2, '0') +
          '.' +
          String(date.getMilliseconds()).padStart(3, '0') +
          timeZoneOffset;

        this.generateNewReportData = {
          reportId: null, // we will get the report id from the API success.
          reportName: workorderDetail.workOrderNumber,
          siteId: workorderDetail.siteId,
          reportTypeId: workorderDetail.assesmentType === 'SV' ? ReportTypes.reportTypeSVId : ReportTypes.reportTypeMVPMId, // check here what type of wo it is then decide
          workorderId: workorderDetail.id,
          reporterId: user.userId,
          reportCreatedDate: localDateWithOffset,
          reportVersion: 3, // fixed to 3 for now
          miscellaneous: '',
          comment: null,
          uploadJHA: null,
          uploadJHAv3ReportIds: [],
          uploadEquipmentStatus: null,
          uploadChecklist: null,
          uploadNC: null,
          isFromMobileUploaded: false
        };

        this.reportService.createNewSVReport(this.generateNewReportData).subscribe({
          next: res => {
            this.alertService.showSuccessToast(res.message);
            this.loading = false;
            this.router.navigate(['/entities/safety/jha/upload'], {
              queryParams: {
                checkin: 1,
                cust: workorderDetail.customerId,
                port: workorderDetail.portfolioId,
                site: workorderDetail.siteId,
                workorderId: workorderDetail.id,
                workOrderNumber: workorderDetail.workOrderNumber,
                reportId: res.id,
                returnTo: 'Workorder'
              }
            });
          },
          error: e => {
            this.loading = false;
          }
        });
      } else if (!this.isReportExistForWO && !result) {
        this.commonWoStatusUpdateLogic(workorderDetail);
        // report is not created, user opted to continue to Report. Create report then Goto Report
        const user = this.storageService.get('user');
        // /api/mobile/v2/uploadreportdata
        const date = new Date();
        const offset = -date.getTimezoneOffset(); // Get offset in minutes
        const offsetHours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, '0');
        const offsetMinutes = String(Math.abs(offset) % 60).padStart(2, '0');
        const timeZoneOffset = (offset >= 0 ? '+' : '-') + offsetHours + ':' + offsetMinutes;

        const localDateWithOffset =
          date.getFullYear() +
          '-' +
          String(date.getMonth() + 1).padStart(2, '0') +
          '-' +
          String(date.getDate()).padStart(2, '0') +
          'T' +
          String(date.getHours()).padStart(2, '0') +
          ':' +
          String(date.getMinutes()).padStart(2, '0') +
          ':' +
          String(date.getSeconds()).padStart(2, '0') +
          '.' +
          String(date.getMilliseconds()).padStart(3, '0') +
          timeZoneOffset;

        this.generateNewReportData = {
          reportId: null, // we will get the report id from the API success.
          reportName: workorderDetail.workOrderNumber,
          siteId: workorderDetail.siteId,
          reportTypeId: workorderDetail.assesmentType === 'SV' ? ReportTypes.reportTypeSVId : ReportTypes.reportTypeMVPMId, // check here what type of wo it is then decide
          workorderId: workorderDetail.id,
          reporterId: user.userId,
          reportCreatedDate: localDateWithOffset,
          reportVersion: 3, // fixed to 3 for now
          miscellaneous: '',
          comment: null,
          uploadJHA: null,
          uploadJHAv3ReportIds: [],
          uploadEquipmentStatus: null,
          uploadChecklist: null,
          uploadNC: null,
          isFromMobileUploaded: false
        };

        this.reportService.createNewSVReport(this.generateNewReportData).subscribe({
          next: res => {
            this.alertService.showSuccessToast(res.message);
            this.loading = false;
            this.onViewReport(workorderDetail.id);
          },
          error: e => {
            this.loading = false;
          }
        });
      }
    });
  }

  checkReportCreationStatus(workorderDetail: WorkOrderData): Promise<void> {
    return new Promise((resolve, reject) => {
      if (workorderDetail.assesmentType === 'SV' || workorderDetail.assesmentType === 'MVPM') {
        this.allowStartNewReport = true;
        this.subscription.add(
          this.reportService.getDataById(workorderDetail.id, 'Order', true).subscribe({
            next: report => {
              if (report.reports.length) {
                // report is already exist for this WO
                // this.newReportBtnText = 'Update Report';
                this.isReportExistForWO = true;
                this.newReportData.id = report.reports[0].reportGuid;
              } else {
                this.isReportExistForWO = false;
                this.newReportData.id = '';
              }
              this.buttonCondition(workorderDetail);
              resolve();
            },
            error: () => {
              // if we are here it could mean the report is not generated for this WO.
              // update details for report creation
              this.newReportData.id = null;
              this.newReportData.isFromMobileUploaded = false;
              this.newReportData.customerId = workorderDetail.customerId;
              this.newReportData.portfolioId = workorderDetail.portfolioId;
              this.newReportData.siteId = workorderDetail.siteId;
              this.newReportData.workorderId = workorderDetail.id;
              this.isReportExistForWO = false;
              this.buttonCondition(workorderDetail);
              reject();
            }
          })
        );
      }
    });
  }

  ngOnDestroy() {
    this.modalRef?.hide();
    this.startFormByInverterModalRef?.hide();
    this.availableFormModalRef?.hide();
    this.availableModuleTorqueFormModalRef?.hide();
    this.selectInverterModalRef?.hide();
    this.selectZoneModalRef?.hide();
    this.viewFromModalRef?.hide();

    this.subscription?.unsubscribe();

    if (this.isWOLocked) {
      this.lockUnlockWO(
        {
          id: this.workOrders.id,
          assementType: this.workOrders.assementType,
          isReLockWo: false
        },
        true
      );
    }

    this.clearTimers();
  }

  isLinkInvalid(item: any): boolean {
    if (!item.documentLink || typeof item.documentLink !== 'string') {
      return true;
    }
    const urlPattern = /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:\d+)?(\/[^\s]*)?$/;
    return !urlPattern.test(item.documentLink.trim());
  }
}
